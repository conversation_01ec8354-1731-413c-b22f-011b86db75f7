<?php

namespace newoffice\control;

require_once __DIR__ . "/../model/officePersonnel.php";
require_once __DIR__ . "/../../common/model/account.php";

function IsOfficePersonnelValid($account_uuid, $personal_account_uuid)
{
    $timezone_str = \common\model\getTimezoneByUUID($account_uuid);
    $validInfo = \newoffice\model\getValidTimeByAccountUUID($personal_account_uuid);
    if (isset($validInfo["IsSetValidTime"]) && $validInfo["IsSetValidTime"] == "1") {
        $timezone = new \DateTimeZone($timezone_str);
        $currentTime = new \DateTime('now', $timezone);
        $endTime = new \DateTime($validInfo["ValidEndTime"], $timezone);
        $startTime = new \DateTime($validInfo["ValidStartTime"], $timezone);
        if ($currentTime < $startTime || $endTime < $currentTime) {
            \util\log\akcsLog::debug("Invalid account: uuid=$personal_account_uuid, info=" . json_encode($validInfo));
            return false;
        }
    }

    return true;
}
