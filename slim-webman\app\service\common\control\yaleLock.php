<?php
namespace common\control;

require_once __DIR__ . "/../../common/model/thirdPartyLockDevice.php";
require_once __DIR__ . "/../../common/model/thirdPartyLockAccount.php";
require_once __DIR__ . "/../../common/model/thirdPartyLockCapture.php";
require_once __DIR__ . "/../../common/model/messageAccountList.php";

function getYaleAuthdata($code, &$authData)
{
    $datas = array();
    $datas['client_id'] = YALE_CLIENT_ID;
    $datas['client_secret'] = YALE_CLIENT_SECRET;
    $datas['code'] = $code;
    $datas['grant_type'] = 'authorization_code';
    $headers=['Content-Type: application/x-www-form-urlencoded'];
    $output = \util\common\httpRequest("post", YALE_AUTH_URL.'/access_token', $headers, $datas);

    $tokenDatas = json_decode($output, true);
    $authData['Token'] = $tokenDatas['access_token'];
    $expires = $tokenDatas['expires_in'];
    $authData['RefreshToken'] = $tokenDatas['refresh_token'];
    $authData['ExpireTime'] = date('Y-m-d H:i:s', time()+$expires);
}

function addYaleLock($userConf, $token)
{
    $headers=['x-august-api-key:'.YALE_API_KEY, 'x-august-access-token:'.$token];
    $output = \util\common\httpRequest("get", YALE_API_URL.'/users/locks/mine', $headers);

    $lockDatas = json_decode($output, true);
    foreach ($lockDatas as $key => $value) {
        //锁开关状态
        $headers2=['x-august-api-key:'.YALE_API_KEY, 'x-august-access-token:'.$token, 'Content-Type:'.'application/json'];
        $url = YALE_API_URL.'/remoteoperate/'.$key.'/status';
        $output2 = \util\common\httpRequest("put", $url, $headers2);
        $lockInfo = json_decode($output2, true);
        if ($lockInfo['status'] == 'kAugLockState_Unlocked' || $lockInfo['status'] == 'kAugLockState_Unlatched') {
            $lockstatus = 1;
        } else {
            $lockstatus = 0;
        }

        if ($lockInfo['doorState'] == 'kAugDoorState_Open') {
            $doorstatus = 0; //door sensor open
        } elseif ($lockInfo['doorState'] == 'kAugDoorState_Closed') {
            $doorstatus = 1; //door sensor close
        } else {
            $doorstatus = 2; //door sensor unknown
        }

        $uuid = \util\common\getMysqlUUID();
        $lockType = YALE_LOCK_TYPE;
        \common\model\addThirdPartyLockDevice($userConf, $key, $uuid, $lockType, $value['LockName'], $lockstatus, $token, $doorstatus);
        
    }
}

function yaleCheckSign($sign, $payload) {
    //签名示例：t=1663153466504,v=16030aec4e14279f620f075548d4c98312d9248a206127a3553fd458b851f7da
    //t为时间戳ms v代表hash_hmac后的内容
    $signArr = explode(',', $sign);
    foreach($signArr as $value) {
        $tmpArr = explode('=', $value);
        if($tmpArr[0] == 't') {
            $timestamps = $tmpArr[1];
        } else if($tmpArr[0] == 'v') {
            $signValue = $tmpArr[1];
        }
    }

    $nowTime = time()*1000; //当前ms时间戳
    if($timestamps > $nowTime + 600000 || $timestamps < $nowTime - 600000) {
        \util\log\akcsLog::debug("Replay attack detected. Timestamps: ".$timestamps);
        return false; 
    } //允许10分钟偏差，超过10分钟认为是重放攻击

    $checkSign = hash_hmac('sha256', $timestamps.'.'.$payload, YALE_API_KEY);   
    if($checkSign != $signValue) {
        \util\log\akcsLog::debug("Signature mismatch. Request Sign: ".$signValue.", Calculated Sign: ".$checkSign);
        return false; 
    }//签名错误 

    return true;
}

function delYaleWebhook($lockID, $token)
{
    \util\log\akcsLog::debug("delYaleWebhook now: ".$lockID);
    $headers=['x-august-api-key:'.YALE_API_KEY, 'x-august-access-token:'.$token];
    $output = \util\common\httpRequest("delete", YALE_API_URL.'/webhook/'.$lockID.'/'.YALE_CLIENT_ID, $headers);
    if ($output) {
        $outputArr = json_decode($output, true);
        if ($outputArr['message'] == 'success') {
            return true;
        } else {
            \util\log\akcsLog::debug("delYaleWebhook error:" . $output);
        }
    }
    return false;
}

function handleYaleDoorSensorLog($yaleLockInfo, $type)
{
    if ($type == 'open') {
        $captureType = LOCK_CAPTURE_TYPE_OPEN;
        $doorStatus = THIRDPARTY_DOOR_STATUS_OPEN;
    } elseif ($type == 'closed') {
        $captureType = LOCK_CAPTURE_TYPE_CLOSE;
        $doorStatus = THIRDPARTY_DOOR_STATUS_CLOSE;
    } else {
        return;
    }

    \common\model\addYaleLockCapture($yaleLockInfo, $captureType);
    \common\model\updateDoorStatus($yaleLockInfo['UUID'], $doorStatus);
}

function handleYaleLowBatteryMsg($yaleLockInfo, $type)
{
    if ($type == 'lock_state_battery_warning_2week') {
        $messageType = LOCK_MESSAGE_TYPE_2WEEK;
    } elseif ($type == 'lock_state_battery_warning_1week') {
        $messageType = LOCK_MESSAGE_TYPE_1WEEK;
    } elseif ($type == 'lock_state_battery_warning_2day') {
        $messageType = LOCK_MESSAGE_TYPE_2DAY;
    } else {
        return;
    }

    \common\model\addYaleBatteryMsg($yaleLockInfo['LockName'], $messageType, $yaleLockInfo['PersonalAccountUUID']);

    //通知应用后台推送消息给APP
    newTextMesage();
}

function openYaleDoor($UserConf, $devUUID, $operate_type)
{
    $thirdLockData = array();

    if ($operate_type) {
        $thirdLockData['message_type'] = LINKER_MSG_TYPE_YALE_OPEN_DOOR;
        $thirdLockData['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR;
    } else {
        $thirdLockData['message_type'] = LINKER_MSG_TYPE_YALE_CLOSE_DOOR;
        $thirdLockData['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_LOCK_BY_APP;
    }

    $thirdLockData['lock_type'] = YALE_LOCK_TYPE;
    $thirdLockData['uuid'] = $devUUID;
    $thirdLockData['personal_account_uuid'] = $UserConf['NodeUUID'];
    $thirdLockData['personal_account_uuid_for_operator'] = $UserConf['UUID']; 
    $thirdLockData['initiator'] = $UserConf['Name'];
    $thirdLockData['lock_name'] = "";
    
    openThirdPartyLockNotify($thirdLockData);
}

function GetAndUpdateYaleLockinfo($userConf, &$yaleLockList)
{
    //查找第三方token
    $authInfo = $authInfo = \common\model\getThirdPartyLockInfo($userConf['NodeUUID'], YALE_LOCK_TYPE);
    if ($authInfo['Token'] != null) {
        //根据token获取锁列表和开关状态
        $token = $authInfo['Token'];
        $headers=['x-august-api-key:'.YALE_API_KEY, 'x-august-access-token:'.$token];
        $output = \util\common\httpRequest("get", YALE_API_URL.'/users/locks/mine', $headers);
        \util\log\akcsLog::debug("Yale get locks API response for user " . $userConf['UserAccount'] . ": " . $output);

        $data = [];
        $lockDatas = json_decode($output, true);

        //token失效，退出刷新
        if ($lockDatas['error'] || $lockDatas['code'] == 'InvalidCredentials') {
            \util\log\akcsLog::debug("Yale get locks failed,user:".$userConf['Account']);
            \util\log\akcsLog::debug("Yale return data:$output");
            return false;
        }

        $i = 0;
        foreach ($lockDatas as $key => $value) {
            $data[$i]['DeviceUUID'] = $key;
            $data[$i]['LockName'] = $value['LockName'];
            $thirdUuidArr[$i] = $data[$i]['DeviceUUID'];

            //获取锁开关状态
            $headers2=['x-august-api-key:'.YALE_API_KEY, 'x-august-access-token:'.$token, 'Content-Type:'.'application/json'];
            $url = YALE_API_URL.'/remoteoperate/'.$key.'/status';
            $output2 = \util\common\httpRequest("put", $url, $headers2);
            $lockInfo = json_decode($output2, true);

            if ($lockInfo['status'] == 'kAugLockState_Unlocked' || $lockInfo['status'] == 'kAugLockState_Unlatched') {
                $data[$i]['LockStatus'] = 1;
            } else {
                $data[$i]['LockStatus'] = 0;
            }

            if ($lockInfo['doorState'] == 'kAugDoorState_Open') {
                $data[$i]['DoorStatus'] = THIRDPARTY_DOOR_STATUS_OPEN; //door sensor open
            } elseif ($lockInfo['doorState'] == 'kAugDoorState_Closed') {
                $data[$i]['DoorStatus'] = THIRDPARTY_DOOR_STATUS_CLOSE; //door sensor close
            } else {
                $data[$i]['DoorStatus'] = THIRDPARTY_DOOR_STATUS_UNKNOWN; //door sensor unknown
            }

            //获取锁详细信息
            $headers_locks=['x-august-api-key:'.YALE_API_KEY, 'x-august-access-token:'.$token];
            $url = YALE_API_URL . '/locks/' . $key;
            $output = \util\common\httpRequest("get", $url, $headers_locks);
            \util\log\akcsLog::debug("Yale get lock info API response for user " . $userConf['UserAccount'] . ": " . $output);
            $lock_detail = json_decode($output, true);

            $data[$i]['IsYaleL2Lock'] = 0;
            if (in_array($lock_detail['hostHardwareID'], YALE_L2_HHID_LIST))
            {
                $data[$i]['IsYaleL2Lock'] = 1;
            }

            $i++;
        }

        \common\model\updateThirdPartyDevlist($userConf, $data, $thirdUuidArr, YALE_LOCK_TYPE, $token);
    }
    \common\model\getThirdPartyDevList($userConf, $yaleLockList);
    return true;
}


