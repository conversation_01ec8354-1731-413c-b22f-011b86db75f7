<?php

namespace common\model;


function recordVerificationCode($loginAccount, $code)
{
    $redis = \util\container\getRedis();
    $redis->select(REDIS_DB_TWO_FACTOR_AUTH_CODE);
    $loginAccount = "APP_TwoFactorAuth_Verify_" . $loginAccount;
    if (!$redis->setex($loginAccount, TWO_FACTOR_AUTH_CODE_EXPIRE_TIME, $code)) 
    {
        \util\log\akcsLog::debug("recordVerificationCode: Failed to set Redis key");
    } 
}

function getTempToken($tempTokenKey)
{
    $redis = \util\container\getRedis();
    $redis->select(REDIS_DB_TWO_FACTOR_AUTH_CODE);
    $tempToken = $redis->get($tempTokenKey);
    return $tempToken;  
}