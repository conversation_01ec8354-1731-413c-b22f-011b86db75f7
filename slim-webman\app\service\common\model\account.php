<?php

namespace common\model;

use PDO;

function getAccountByID($id)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select Location,UUID,ManageGroup,Account,Language,ParentID,ParentUUID from Account where ID = :id");
    $sth->bindParam(':id', $id, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result;
}

function getAccountByUUID($uuid)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select Location,UUID,ManageGroup,Account,Language,ParentID,ParentUUID,Grade from Account where UUID = :uuid");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result;
}

function getAccountByParentId($parentId)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select UUID from Account where ID = :parentId");
    $sth->bindParam(':parentId', $parentId, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result;
}

function getCommunityContactSwitch($userInfo)
{
    $db = \util\container\getDb();
    if ($userInfo['Role'] != ROLE_TYPE_COMMUNITY_MASTER && $userInfo['Role'] != ROLE_TYPE_COMMUNITY_SLAVE) {
        return 0;
    }
    $mngID = $userInfo['MngID'];
    $sth = $db->prepare("select D.Flags as Dis_Flags, C.Flags as Comm_Flags from Account D join Account C on C.ParentUUID = D.UUID where C.ID = :mngid");
    $sth->bindParam(':mngid', $mngID, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        if (\util\utility\switchHandle($result['Dis_Flags'], ACCOUNT_FLAGS_COMMUNITY_CONTACT) && \util\utility\switchHandle($result['Comm_Flags'], ACCOUNT_FLAGS_COMMUNITY_CONTACT)) {
            return 1;
        }
    }
    return 0;
}

function checkExpireEmailOff($id)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select SendRenew from Account where ID = :UID");
    $sth->bindParam(':UID', $id, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $ret = $result['SendRenew'] ? 0 : 1;
    }
    return $ret;
}

function getUserMngSipType($role, $ParentID)
{
    $db = \util\container\getDb();
    $sipType = 0;
    $rtpConfuse = 0;
    if ($role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_COMMUNITY_MASTER || $role == ROLE_TYPE_COMMUNITY_PM) { //个人主账号 社区主账号 PM账号
        $sth = $db->prepare("select ManageGroup From Account where ID=:ID");
        $sth->bindParam(':ID', $ParentID, PDO::PARAM_INT);
        $ret = $sth->execute();
        $mngid = $sth->fetch(PDO::FETCH_ASSOC);
        $mngid = $mngid['ManageGroup'];

        $sth = $db->prepare("select SipType,Flags from Account where ID=:ID");
        $sth->bindParam(':ID', $mngid, PDO::PARAM_INT);
        $ret = $sth->execute();
        $mngid = $sth->fetch(PDO::FETCH_ASSOC);
        $sipType = $mngid['SipType'];
        $rtpConfuse = $mngid['Flags'] & RTP_CONFUSE;
    } else { //从账号
        $sth = $db->prepare("select ParentID  From PersonalAccount where ID=:ID");
        $sth->bindParam(':ID', $ParentID, PDO::PARAM_INT);
        $ret = $sth->execute();
        $mngid = $sth->fetch(PDO::FETCH_ASSOC);
        $mngid = $mngid['ParentID'];

        $sth = $db->prepare("select ManageGroup From Account where ID=:ID");
        $sth->bindParam(':ID', $mngid, PDO::PARAM_INT);
        $ret = $sth->execute();
        $mngid = $sth->fetch(PDO::FETCH_ASSOC);
        $mngid = $mngid['ManageGroup'];

        $sth = $db->prepare("select SipType,Flags from Account where ID=:ID");
        $sth->bindParam(':ID', $mngid, PDO::PARAM_INT);
        $ret = $sth->execute();
        $mngid = $sth->fetch(PDO::FETCH_ASSOC);
        $sipType = $mngid['SipType'];
        $rtpConfuse = $mngid['Flags'] & RTP_CONFUSE;
    }
    return  ["SipType" => $sipType, "RtpConFuse"=>$rtpConfuse];
}

function getCommunityTime($db, $mngid)
{
    $sth = $db->prepare("select TimeZone From Account where ID=:ID");
    $sth->bindParam(':ID', $mngid, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC)['TimeZone'];
    return $result;
}

function getTimezoneByUUID($uuid)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select TimeZone From Account where UUID=:UUID");
    $sth->bindParam(':UUID', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC)['TimeZone'];
    return $result;
}

function getSubDisAccountByInsUUID($uuid)
{
    $db = \util\container\getDb();
    $sub_dis_grade = GRADE_TYPE_SUB_DIS;
    $sth = $db->prepare("select A.Account,A.Language from Account A LEFT JOIN SubDisMngList S on A.UUID = S.DistributorUUID where S.InstallerUUID = :uuid AND A.Grade = :grade");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindParam(':grade', $sub_dis_grade, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result;
}

function getAccountInfoByAccount($account)
{
    return \util\container\medooDb()->get("Account", ["Language"], ["Account" => $account]);
}

function getAccountByUserInfoUUID($userInfoUUID)
{
    $accountMap = \common\model\getAccountMapByUserInfoUUID($userInfoUUID);
    if (!$accountMap) 
    {
        return false;
    }
    $account = \common\model\getAccountByUUID($accountMap['AccountUUID']);
    if (!$account) 
    {
        return false;
    }
    return  $account;
}

// 根据项目UUID获取INSUUID
function getInsUUIDByProjectUUID($project_uuid)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT INS.UUID from Account P
                        JOIN Account INS ON P.ManageGroup = INS.ID
                        where P.UUID = :uuid");
    $sth->bindParam(':uuid', $project_uuid, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    if (empty($result) || !isset($result['UUID']) || empty($result['UUID'])) {
        return "";
    }

    return $result['UUID'];
}
