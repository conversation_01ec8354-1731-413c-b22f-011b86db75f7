<?php

namespace  resident\control;

require_once __DIR__ . "/../../common/control/faceHandle.php";
require_once __DIR__ . "/../../common/model/faceMng.php";

function uploadFace($request, $response)
{
    $userConf = \util\container\getUserData();

    $files = (array) $request->file();
    if (($file = reset($files)) === false || !$file->isValid()) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Please upload face image");
    }
    $fileName = $file->getUploadName();
    \util\log\akcsLog::debug("filename = $fileName");

    $mngAccountID = $userConf['MngID'];
    $node = $userConf['Account'];
    $faceEncFileRelativePath = '/' . (int) $mngAccountID % 100 . '/' . $mngAccountID . '/' . (int) $node % 64;
    $extractPath = FACE_FILE_PREFIX . $faceEncFileRelativePath;
    \util\common\mkDirs($extractPath);

    $fileInfo = array();
    $fileInfo[FILE_NAME] = $fileName;
    $fileInfo[FILE_FULL_NAME] = $extractPath . '/' . $fileName;
    $fileInfo[FILE_ENC_NAME] = $extractPath . '/' . $fileName . '.enc';
    $file->move($fileInfo[FILE_FULL_NAME]);
    
    $detect_result = \util\common\faceUpload($fileInfo[FILE_FULL_NAME], $userConf['UserAccount']);
    if (!$detect_result || $detect_result['code'] != UPLOAD_FACEPIC_SUCCESS) {
        \util\log\akcsLog::debug("Please upload face image");
        unlink($fileInfo[FILE_FULL_NAME]);
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Please upload face image");
    }

    $fileInfo[FACE_FILE_MD5] = $detect_result['data']['face_file_md5'];
    $fileInfo[FACE_FILE_URL] = $detect_result['data']['face_file_url'];

    \util\log\akcsLog::debug("fileInfo=" . json_encode($fileInfo));

    $personalAccountInfo['PersonalAccountID'] = $userConf['UserAccountID'];
    $personalAccountInfo['UnitID'] = $userConf['UnitID'];
    $personalAccountInfo['Node'] = $userConf['Account'];
    $personalAccountInfo['UUID'] = $userConf['UUID'];
    \util\log\akcsLog::debug("personalAccountInfo=" . json_encode($personalAccountInfo));

    if (!\common\model\mergeFaceMngWithoutUpload($personalAccountInfo, $fileInfo, $mngAccountID)) {
        \util\log\akcsLog::debug("mergeFaceMngWithoutUpload falied!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Failed to write database");
    }

    if (unlink($fileInfo[FILE_FULL_NAME])) {
        \util\log\akcsLog::debug("remove file:" . $fileInfo[FILE_FULL_NAME] . " success!");
    } else {
        \util\log\akcsLog::debug("remove file:" . $fileInfo[FILE_FULL_NAME] . " failed!");
    }

    \common\control\uploadFaceNotify($userConf);

    $auditLog = \util\container\getAuditLog();
    $auditLog->insertAuditLog($request->getRemoteIp(), $userConf['UserAccount'], AUDIT_TYPE_IMPORT_FACE, $userConf['UserAccount'], $userConf['Role'], $userConf['MngID']);

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function uploadFaceV65($request, $response)
{
    $userConf = \util\container\getUserData();
    \util\log\akcsLog::debug("getConnInfo:" . json_encode($userConf));
    $postData = $request->getParsedBody();
    $subAccount = $postData['sub_account'];

    $files = (array) $request->file();
    $result = uploadFaceAboveV65($request, $files, $userConf, $subAccount);
    if ($result == FACE_UPLOAD_SUCCESS) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], $result);
    }
}

function deleteFace($request, $response)
{
    $userConf = \util\container\getUserData();
    $personalAccountID = $userConf['UserAccountID'];
    $subAccount = $request->getQueryParams()['sub_account'];
    $operatorAccount = $userConf['UserAccount'];
    if (!empty($subAccount)) {
        $subUserInfo = \common\model\getProjectSubUserInfo($subAccount);
        $personalAccountID = $subUserInfo['ID'];
        if ($subUserInfo['ParentID'] != $userConf['UserAccountID']) {
            return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], FACE_UPLOAD_ERROR[4]);
        }
        $userConf['UserAccount'] = $subAccount;
    }
    $data = \common\model\queryFaceMng($personalAccountID);
    if ($data) {
        $faceUrl = $data["FaceUrl"];
        if ($faceUrl) {
            if (!\util\fdfs\fdfs_del_pic_by_url($faceUrl)) {
                $fullPath = "/var/www/download/face/" . $faceUrl;
                unlink($fullPath);
            }
        }
    }
    $affected = \common\model\deleteFaceMng($personalAccountID);
    if ($affected > 0) {
        $ret = deleteFaceNotify($userConf);
    }
    $auditLog = new \util\auditlog\AuditLog();
    $auditLog->insertAuditLog($request->getRemoteIp(), $operatorAccount, AUDIT_TYPE_DELETE_FACE, $userConf['UserAccount'], $userConf['Role'], $userConf['MngID']);

    if ($ret == 0) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"The PersonalAccount's Role is Wrong.");
    }
    
}

function deleteFaceNotify($userConf)
{
    $role = $userConf['Role'];
    $changeType = 0;
    $mac = "";
    $node = $userConf['Account'];
    $accounts = [];
    if ($role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_PERSONNAL_SLAVE) {  //个人主账号,个人从账号
        $changeType = APP_PERSONAL_DEL_FACE_PIC;
        $insatllID = 0;
        webPersonalModifyNotify($changeType, $node, $mac, $insatllID);
    } elseif ($role == ROLE_TYPE_COMMUNITY_MASTER || $role == ROLE_TYPE_COMMUNITY_SLAVE) { //社区主账号,社区从账号
        $changeType = WEB_COMM_DEL_FACE_PIC;
        $communitid = $userConf['MngID'];
        $unitid = $userConf['UnitID'];
        $accounts[0] = $userConf['UserAccount'];
        $accounts[1] = $userConf['Account'];
        WebCommunityAccountModifyNotify($communitid, $accounts, 0);
        webCommunityModifyNotify($changeType, $node, $mac, $communitid, $unitid);
    } elseif ($role == ROLE_TYPE_COMMUNITY_PM) { //社区PM账号
        $communitid = $userConf['MngID'];
        WebCommunityAccountModifyNotify($communitid, $userConf['UserAccount'], 0);
        webCommunityModifyNotify(WEB_COMM_DEL_FACE_PIC, $node, $mac, $communitid, 0);
    } else {
        return -1;
    }
    return 0;
}

function uploadFaceDynamic($request, $response)
{
    $postDatas = $request->getParsedBody();
    $tag = $postDatas['tag'];
    $all = $postDatas['all'];
    $index = $postDatas['index'];

    if ($index == $all) {
        return uploadFace($request, $response);
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

const FACE_UPLOAD_ERROR = [
    "Please upload face image",
    "Encrypt face image failed",
    "Move File failed",
    "Failed to write database",
    "Sub account not belong to the account"
];
const FACE_UPLOAD_SUCCESS = "Success";
/*
 *@description pp高于版本V6.5是调用该方法上传人脸
 *<AUTHOR> 2024-10-25 17:42:12 V7.1.0
 *@lastEditor cj 2024-10-25 17:42:12 V7.1.0
 *@param {*} files 上传的文件
 *@param {*} mngAccountID 社区ID
 *@param {*} userConf 登录的app信息 
 *@param {*} selfAccount 需要导入的人脸账号，未传时为给用户自己导，只允许传用户从账号
 *@return FACE_UPLOAD_ERROR | FACE_UPLOAD_SUCCESS
 */
function uploadFaceAboveV65($request, $files, $userConf, $assignAccount = '')
{
    if (($file = reset($files)) === false || !$file->isValid()) {
        return FACE_UPLOAD_ERROR[0];
    }
    $fileName = $file->getUploadName();
    \util\log\akcsLog::debug("filename = $fileName");

    $mngAccountID = $userConf['MngID'];
    $node = $userConf['Account']; // 表示主账号
    $role = $userConf['Role'];

    $faceEncFileRelativePath = '/' . (int) $mngAccountID % 100 . '/' . $mngAccountID . '/' . (int) $node % 64;
    $extractPath = FACE_FILE_PREFIX . $faceEncFileRelativePath;
    \util\log\akcsLog::debug("extractPath:" . $extractPath);
    \util\common\mkDirs($extractPath);

    $fileInfo = array();
    $fileInfo[FILE_NAME] = $fileName;
    $fileInfo[FILE_FULL_NAME] = $extractPath . '/' . $fileName;
    $fileInfo[FILE_ENC_NAME] = $extractPath . '/' . $fileName . '.enc';
    $file->move($fileInfo[FILE_FULL_NAME]);

    if (!is_file($fileInfo[FILE_FULL_NAME])) {
        \util\log\akcsLog::debug("Please upload face image");
        unlink($fileInfo[FILE_FULL_NAME]);
        return FACE_UPLOAD_ERROR[0];
    }

    if (empty($assignAccount)) {
        $account = $userConf['UserAccount'];
        $personalAccountID = $userConf['UserAccountID'];
        $personalAccountUUID = $userConf['UUID'];
    } else {
        $account = $assignAccount;
        $subUserInfo = \common\model\getProjectSubUserInfo($assignAccount);
        $personalAccountID = $subUserInfo['ID'];
        $personalAccountUUID = $subUserInfo['UUID'];
        if ($subUserInfo['ParentID'] != $userConf['UserAccountID']) {
            return FACE_UPLOAD_ERROR[4];
        }

        // 需要重置user信息为从账号的用于notify，避免后续出问题
        $assignAccountUserConf = $userConf;
        $assignAccountUserConf['Role'] = $subUserInfo['Role'];
        $assignAccountUserConf['UserAccount'] = $assignAccount;
        $assignAccountUserConf['ID'] = $subUserInfo['ID'];
        $assignAccountUserConf['UUID'] = $subUserInfo['UUID'];
    }
    
    $aes = new \util\aes\MYAES128CBC;
    $key = $aes->getKey($account);
    \util\log\akcsLog::debug("key=" . $key);
    $ret = $aes->encryptFile($fileInfo[FILE_FULL_NAME], $key, $fileInfo[FILE_ENC_NAME]);
    if (!$ret) {
        \util\log\akcsLog::debug("encryptFile:" . $fileInfo[FILE_FULL_NAME] . " error!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return FACE_UPLOAD_ERROR[1];
    }

    $fileInfo[FACE_FILE_MD5] = md5_file($fileInfo[FILE_ENC_NAME]);
    $fileInfo[FACE_FILE_URL] = $faceEncFileRelativePath . '/' . $fileInfo[FACE_FILE_MD5] . '.jpg';
    $faceEncFilePath = FACE_FILE_PREFIX . $fileInfo[FACE_FILE_URL];
    if (!rename($fileInfo[FILE_ENC_NAME], $faceEncFilePath)) {
        \util\log\akcsLog::debug("rename File:" . $fileInfo[FILE_ENC_NAME] . " to File:" . $faceEncFilePath . " error!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return FACE_UPLOAD_ERROR[2];
    }
    \util\log\akcsLog::debug("fileInfo=" . json_encode($fileInfo));

    $personalAccountInfo['PersonalAccountID'] = $personalAccountID;
    $personalAccountInfo['UnitID'] = $userConf['UnitID'];
    $personalAccountInfo['Node'] = $node;
    $personalAccountInfo['UUID'] = $personalAccountUUID;
    \util\log\akcsLog::debug("personalAccountInfo=" . json_encode($personalAccountInfo));

    //上传人脸并更新数据库
    if (!\common\model\mergeFaceMng($personalAccountInfo, $fileInfo, $mngAccountID)) {
        \util\log\akcsLog::debug("mergeFaceMng falied!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return FACE_UPLOAD_ERROR[3];
    }

    unlink($fileInfo[FILE_FULL_NAME]);

    if (empty($assignAccount)) {
        \common\control\uploadFaceNotify($userConf);
    } else {
        \common\control\uploadFaceNotify($assignAccountUserConf);
    }

    $auditLog = \util\container\getAuditLog();
    $auditLog->insertAuditLog($request->getRemoteIp(), $userConf['UserAccount'], AUDIT_TYPE_IMPORT_FACE, $account, $role, $mngAccountID);

    return FACE_UPLOAD_SUCCESS;
}

