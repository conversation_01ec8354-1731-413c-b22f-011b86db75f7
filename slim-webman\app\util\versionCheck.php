<?php

namespace util\version;

function isSupportMultiSite()
{
    $version = \util\container\getApiVersion();
    // api-version < 6.6，不允许登录一人多套房账号
    if (floatval($version) < 6.6) {
        return false;
    } else {
        return true;
    }
}

function isNeedUpdatePinInitStatus()
{
    $version = \util\container\getApiVersion();
    if (floatval($version) < 6.5) {
        return true;
    } else {
        return false;
    }
}

function isNoSupportCheckDev()
{
    $version = \util\container\getApiVersion();
    if ($version == '3.0') {
        return true;
    } else {
        return false;
    }
}

function isNeedReturnPinInit()
{
    $version = \util\container\getApiVersion();
    if (floatval($version) >= 6.5) {
        return true;
    } else {
        return false;
    }
}

function  isSupportExRelay()
{
    $version = \util\container\getApiVersion();
    if (floatval($version) >= 6.73) {
        return true;
    } else {
        return false;
    }    
}

function  isNeedSmsRateLimited()
{
    $version = \util\container\getApiVersion();
    if (floatval($version) >= 7.11) {
        return true;     
    }
}

function isShowNewBrandLock()
{
    $version = \util\container\getApiVersion();
    if (floatval($version) >= 7.10) {
        return true;
    } else {
        return false;
    }    
}
function isNeedEligibleIndoorOnlineCheck()
{
    $version = \util\container\getApiVersion();
    if (floatval($version) >= 7.13) {
        return true;
    } else {
        return false;
    }    
}