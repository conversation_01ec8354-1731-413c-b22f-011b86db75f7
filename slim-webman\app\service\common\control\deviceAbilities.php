<?php

namespace common\control;

require_once __DIR__ . "/videoStorage.php";

function getDeviceAbilities($request, $response)
{
    $postData = $request->getParsedBody();
    \util\log\akcsLog::debug(json_encode($postData));

    $datas = array();
    if (isset($postData['mac']) && isset($postData['abilities'])) {
        foreach ($postData['abilities'] as $ability) {
            switch ($ability) {
                case 'video_record':
                    getVideoRecordAbilities($postData["mac"], $datas);
                    break;
                default:
                    $datas[$ability]['enable'] = 0;
                    break;
            }
        }
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED);
    }
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function getVideoRecordAbilities($mac, &$datas)
{
    // 判断是否为视频存储角色
    if (!isVideoRecordRole()) {
        $datas['video_record']['status'] = VIDEO_STORAGE_CANCEL;
        return;
    }

    $devVideoStorageInfo = \common\control\deviceVideoStorageInfo($mac);

    // 未查询到视频存储设备 或者 视频存储开关关闭
    if (!$devVideoStorageInfo || $devVideoStorageInfo['IsEnable'] == 0) {
        $datas['video_record']['status'] = VIDEO_STORAGE_CANCEL;
        return;
    }

    // 视频存储方案过期
    if ($devVideoStorageInfo['Expire']) {
        $datas['video_record']['status'] = VIDEO_STORAGE_EXPIRE;
        return;
    }
    
    $datas['video_record']['status'] = VIDEO_STORAGE_ENABLE;
    return;
}

function isVideoRecordRole()
{
    $userData = \util\container\getUserData();

    // pm app,社区主从账号和单住户主从账号都能录制
    if ($userData["Role"] == ROLE_TYPE_PERSONNAL_MASTER
     || $userData["Role"] == ROLE_TYPE_PERSONNAL_SLAVE 
     || $userData["Role"] == ROLE_TYPE_COMMUNITY_PM
     || $userData["Role"] == ROLE_TYPE_COMMUNITY_MASTER
     || $userData["Role"] == ROLE_TYPE_COMMUNITY_SLAVE) {
        return true;
    }
    return false;
}