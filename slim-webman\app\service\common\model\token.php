<?php

namespace common\model;

use PDO;

function getTokenInfoByAppToken($token)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("/*master*/ select Account,WebToken,AppTokenEt,AppMainUserAccount from Token WHERE AppToken = :token");
    $sth->bindParam(':token', $token, PDO::PARAM_STR);
    $sth->execute();
    $tokenInfo = $sth->fetch(PDO::FETCH_ASSOC);
    return $tokenInfo;
}

function getTokenInfoByAppMainAccount($main_account)
{
    $tokenInfo = \util\container\medooDb()->get("Token", ["Account", "WebToken", "AppTokenEt", "EnableCallkit"], ["AppMainUserAccount" => $main_account]);
    return $tokenInfo;
}

function updateAccountByToken($account, $token)
{
    $db = \util\container\getDb();
    $db->beginTransaction();

    // 先删除account，防止更新时唯一键冲突
    $sth = $db->prepare("delete from Token where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();

    // 再更新Token表的Account
    $sth1 = $db->prepare("update Token set Account = :account where AppToken = :token");
    $sth1->bindParam(':token', $token, PDO::PARAM_STR);
    $sth1->bindParam(':account', $account, PDO::PARAM_STR);
    $sth1->execute();

    $db->commit();
}

function setAppEnableCallkit($main_account, $switch)
{
    \util\container\medooDb()->update("Token", ["EnableCallkit" => $switch], ["AppMainUserAccount" => $main_account]);
}