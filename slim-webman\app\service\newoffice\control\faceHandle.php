<?php

namespace newoffice\control;

require_once __DIR__ . "/../model/face.php";
require_once __DIR__ . "/../../common/model/faceMng.php";
require_once __DIR__ . "/../../common/control/faceHandle.php";
require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../../../util/aesManage.php";


function uploadNewOfficeFaceV70($request, $response)
{
    $userConf = \util\container\getUserData();

    $files = (array) $request->file();
    if (($file = reset($files)) === false || !$file->isValid()) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Please upload face image");
    }
    $fileName = \util\common\randomkeys(7) . "_" . $file->getUploadName();
    \util\log\akcsLog::debug("filename=" . $fileName);
    \util\common\mkDirs(FACE_FILE_PREFIX);

    $fileInfo = array();
    $fileInfo[FILE_NAME] = $fileName;
    $fileInfo[FILE_FULL_NAME] = FACE_FILE_PREFIX . '/' . $fileName;
    $fileInfo[FILE_ENC_NAME] = FACE_FILE_PREFIX . '/' . $fileName . '.enc';
    $file->move($fileInfo[FILE_FULL_NAME]);

    /*大于6.5版本的app无需检测
    $detect_result = \util\common\faceDetectV2(FACE_FILE_PREFIX, $fileName);
    if ($detect_result != 0) {
        \util\log\akcsLog::debug("Please upload face image");
        unlink($fileInfo[FILE_FULL_NAME]);
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Please upload face image");
    }
    */

    $aes = new \util\aes\MYAES128CBC;
    $key = $aes->getKey($userConf['UserAccount']);

    $ret = $aes->encryptFile($fileInfo[FILE_FULL_NAME], $key, $fileInfo[FILE_ENC_NAME]);
    if (!$ret) {
        \util\log\akcsLog::debug("encryptFile:" . $fileInfo[FILE_FULL_NAME] . " error!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Encrypt face image failed");
    }

    $fileInfo[FACE_FILE_MD5] = md5_file($fileInfo[FILE_ENC_NAME]);
    $fileInfo[FACE_FILE_URL] = FACE_FILE_PREFIX . $fileInfo[FACE_FILE_MD5] . '.jpg';
    if (!rename($fileInfo[FILE_ENC_NAME], $fileInfo[FACE_FILE_URL])) {
        \util\log\akcsLog::debug("rename File:" . $fileInfo[FILE_ENC_NAME] . " to File:" . $fileInfo[FACE_FILE_URL] . " error!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Move File failed");
    }
    \util\log\akcsLog::debug("fileInfo=" . json_encode($fileInfo));

    $personalAccountInfo['AccountUUID'] = $userConf['MngUUID'];         // 项目UUID
    $personalAccountInfo['PersonalAccountUUID'] = $userConf['UUID'];    // 用户UUID
    \util\log\akcsLog::debug("personalAccountInfo=" . json_encode($personalAccountInfo));

    if (\newoffice\model\mergeFace($personalAccountInfo, $fileInfo) == false) {
        \util\log\akcsLog::debug("mergeFaceMng falied!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Failed to write database");
    }

    if (unlink($fileInfo[FILE_FULL_NAME])) {
        \util\log\akcsLog::debug("remove file:" . $fileInfo[FILE_FULL_NAME] . " success!");
    } else {
        \util\log\akcsLog::debug("remove file:" . $fileInfo[FILE_FULL_NAME] . " failed!");
    }

    $jsondata = \util\common\createGeneralMessage(
        "account_modify",
        array(
            "account_uuid" => strval($userConf['UUID']),
            "office_uuid" => strval($userConf['MngUUID'])
        )
    );
    JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function deleteFace($request, $response)
{
    $userConf = \util\container\getUserData();
    $personalAccountUUID = $userConf['UUID'];
    $data = \newoffice\model\queryFace($personalAccountUUID);
    if ($data) {
        $faceUrl = $data["FaceUrl"];
        if ($faceUrl) {
            if (!\util\fdfs\fdfs_del_pic_by_url($faceUrl)) {
                $fullPath = "/var/www/download/face/" . $faceUrl;
                unlink($fullPath);
            }
        }
    }
    $affected = \newoffice\model\deleteFace($personalAccountUUID);
    if ($affected > 0) {
        $jsondata = \util\common\createGeneralMessage(
            "account_modify",
            array(
                "account_uuid" => strval($userConf['UUID']),
                "office_uuid" => strval($userConf['MngUUID'])
            )
        );

        JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);
    }
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}


function getFaceStatus($request, $response)
{
    $userData = \util\container\getUserData();

    /**
     * 1存在人脸
     * 0不存在人脸
     */
    $data = \newoffice\model\queryFace($userData['UUID']);
    if ($data) {
        $statusJson["status"] = "1";
    } else {
        $statusJson["status"] = "0";
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $statusJson);
}