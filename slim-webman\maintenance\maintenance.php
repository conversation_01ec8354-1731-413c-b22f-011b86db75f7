<?php
namespace maintenance;
require_once __DIR__ . "/../app/util/response.php";
require_once __DIR__ . "/../app/config/define.php";
require_once __DIR__ . "/../app/util/redisManage.php";

$smsKeyList = ['sms_limit_*', 'new_office_admin_sms_limit_*'];

// 获取Redis连接的辅助函数
function getRedisConnection($redisDB)
{
    static $redis = null;
    if ($redis === null) {
        $redisConnection = new \util\redisManage\RedisManage();
        $redis = $redisConnection->getRedisInstance();
    }
    $redis->select($redisDB);
    return $redis;
}

function getRedisAllKeys($keyList, $redisDB)
{
    $redis = getRedisConnection($redisDB);

    // 使用 KEYS 获取所有匹配的键
    $keys = array();
    foreach ($keyList as $key) {
        $key_tmp = $redis->keys($key);
        if ($key_tmp) {
            $keys = array_merge($keys, $key_tmp);
        }
    }

    return $keys;
}

function getAllSmsLimitKey($request, $response)
{
    global $smsKeyList;

    // 使用 KEYS 获取所有匹配的键
    $keys = getRedisAllKeys($smsKeyList, REDIS_DB_SMS_RATE_LIMIT);

    $result = [];

    if ($keys) {    
        $redis = getRedisConnection(REDIS_DB_SMS_RATE_LIMIT);
        
        foreach ($keys as $key) {
            $count = $redis->get($key);
            $ttl = $redis->ttl($key);

            // 仅记录达到限流次数的 Key
            if ((int)$count >= SMS_RATE_LIMIT_NUM) {
                $result[] = [
                    'key' => $key,
                    'count' => (int)$count,
                    'ttl' => $ttl,
                ];
            }
        }
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $result, 'Keys retrieved successfully');
}


function clearSmsLimitKey($request, $response)
{
    $params = $request->getQueryParams();
    $phone = $params['key'] ?? null; // 从请求参数中获取手机号
    $userType = $params['user_type'] ?? null; // 从请求参数中获取用户类型

    if (empty($phone)) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], 'Phone is required');
    }

    if (is_null($userType)) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], 'User type is required');
    }

    $redis = getRedisConnection(REDIS_DB_SMS_RATE_LIMIT);

    // 使用通配符匹配包含手机号的 Key
    $pattern = $userType == 0 ? "sms_limit_" . "{$phone}*" : "new_office_admin_sms_limit_" . "{$phone}*";
    $keys = $redis->keys($pattern); // 获取匹配的所有 Key

    if (!empty($keys)) {
        $deletedCount = 0;

        // 遍历匹配的 Key 并删除
        foreach ($keys as $key) {
            $redis->del($key);
            $deletedCount++;
        }

        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, [], "Deleted {$deletedCount} keys containing phone {$phone}");
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "No keys found containing phone {$phone}");
    }
}

function clearAllSmsLimitKey($request, $response)
{
    global $smsKeyList;

    // 获取所有限流 keys
    $keys = getRedisAllKeys($smsKeyList, REDIS_DB_SMS_RATE_LIMIT);
    if (empty($keys)) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, [], 'No rate limit keys to clear');
    }

    $redis = getRedisConnection(REDIS_DB_SMS_RATE_LIMIT);
    // 删除所有 keys
    foreach ($keys as $key) {
        $redis->del($key);
    }
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, [], 'All rate limit keys have been cleared');
}



