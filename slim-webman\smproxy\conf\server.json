{"server": {"user": "root", "password": "123456", "charset": "utf8mb4", "host": "0.0.0.0", "port": "3366", "mode": "SWOOLE_PROCESS", "sock_type": "SWOOLE_SOCK_TCP", "logs": {"open": true, "config": {"system": {"log_path": "/var/www/html/slim_webman/smproxy/logs", "log_file": "system.log", "format": "Y/m/d"}, "mysql": {"log_path": "/var/www/html/slim_webman/smproxy/logs", "log_file": "mysql.log", "format": "Y/m/d"}}}, "swoole": {"worker_num": "swoole_cpu_num()", "max_coro_num": 8000, "open_tcp_nodelay": true, "daemonize": false, "heartbeat_check_interval": 60, "heartbeat_idle_time": 600, "reload_async": true, "log_file": "/var/www/html/slim_webman/smproxy/logs/swoole.log", "pid_file": "/var/www/html/slim_webman/smproxy/logs/pid/server.pid"}, "swoole_client_setting": {"package_max_length": 16777215}, "swoole_client_sock_setting": {"sock_type": "SWOOLE_SOCK_TCP"}}}