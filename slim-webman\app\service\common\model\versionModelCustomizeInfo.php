<?php

namespace common\model;

use PDO;

function getVersionModelCustomizeInfo($disUUID, $versionModelIDList)
{
    // 确保所有ID都是整数，防止SQL注入
    $versionModelIDList = array_map('intval', $versionModelIDList);
    $placeholders = implode(',', array_fill(0, count($versionModelIDList), '?'));
    
    $db = \util\container\getDb();
    $sql = "SELECT CustomizeName FROM VersionModelCustomizeInfo 
            WHERE AccountUUID = ? AND VersionModelID IN ($placeholders)";
    
    $stmt = $db->prepare($sql);
    
    // 绑定参数 - 第一个是disUUID，然后是各个ID
    $stmt->bindValue(1, $disUUID);
    foreach ($versionModelIDList as $k => $id) {
        $stmt->bindValue(2 + $k, $id, PDO::PARAM_INT);
    }
    
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}