<?php

namespace common\control\thirdNvr;

require_once __DIR__ . '/../model/thirdNvr.php';

// 适配app api数据格式
function adaptAppThirdNvrList($nvrList)
{
    $result = [];
    foreach ($nvrList as $nvr) {
        $result[] = [
            'uuid' => $nvr['UUID'],
            'type' => intval($nvr['Type']),
            'name' => $nvr['Name'],
            'address' => $nvr['Url'],
            'username' => $nvr['UserName'],
            'password' => $nvr['Password'],
        ];
    }
    return $result;
}

// pm下发公共和楼栋的所有Nvr
function getPmThirdNvrList()
{
    $userConf = \util\container\getUserData();
    $pubNvrList = \common\model\getComPubAllThirdNvrList($userConf['MngUUID']);
    $nvrList = adaptAppThirdNvrList($pubNvrList);
    return $nvrList;
}

// 社区账号下发pub/unit/apt所有有权限的Nvr
function getComAptThirdNvrList()
{
    $userConf = \util\container\getUserData();
    $allThirdNvrList = \common\model\getComAptThirdNvrList($userConf['MngUUID'], $userConf['CommunityUnitUUID'], $userConf['NodeUUID']);
    $nvrList = adaptAppThirdNvrList($allThirdNvrList);
    return $nvrList;
}

// 单住户账号下发所有有权限的Nvr
function getPerAptThirdNvrList()
{
    $userConf = \util\container\getUserData();
    $allThirdNvrList = \common\model\getPerAptThirdNvrList($userConf['NodeUUID']);
    $nvrList = adaptAppThirdNvrList($allThirdNvrList);
    return $nvrList;
}