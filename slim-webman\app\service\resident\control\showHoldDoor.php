<?php

namespace resident\control;
require_once __DIR__ . "/../../resident/model/personalAccountCnf.php";

function setShowHoldDoor($request, $response)
{          
    $userConf = \util\container\getUserData();  

    $postData = $request->getParsedBody();
    $enableShowHoldDoor = intval($postData['enable_show_hold_door']);

    \resident\model\updateEnableShowHoldDoor($enableShowHoldDoor, $userConf['UserAccount']);

    return \util\response\setResponseMessage($response);
}