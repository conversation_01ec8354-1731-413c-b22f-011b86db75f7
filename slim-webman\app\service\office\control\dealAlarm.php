<?php

namespace office\control;

function dealOfficePersonalAlarm($request, $response)
{
    $userConf = \util\container\getUserData();
    
    $bodyValue = $request->getParsedBody();
    $alarmID = $bodyValue["ID"];
    $alarmResult = $bodyValue["Result"];
    $alarmNode = $userConf['Account'];

    \common\model\updateAlarms($alarmID, $alarmResult);
    officeAlarmDealNotify($alarmNode, $userConf['UserAccount'], $alarmID, $alarmResult);

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}
