<?php

namespace newoffice\control;

require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/appPushToken.php";
require_once __DIR__ . "/../../common/model/account.php";
require_once __DIR__ . "/../../common/model/token.php";
require_once __DIR__ . "/../../office/model/officeInfo.php";
require_once __DIR__ . "/../model/officePersonnel.php";

require_once __DIR__ . "/adminUserConf.php";
require_once __DIR__ . "/personnelUserConf.php";
require_once __DIR__ . "/callType.php";


function getOfficeCommConf($request, $response)
{
    $userConf = \util\container\getUserData();

    $commCode = \common\model\getCommCodeConf($userConf['UserAccount']);

    $bleCode = $commCode['BLECode'];
    $nfcCode = $commCode['NFCCode'];
    $enableNFC = strlen($commCode['NFCCode']) > 0 ? "1" : "0";
    $enableBLE = strlen($commCode['BLECode']) > 0 ? "1" : "0";

    $callType = \newoffice\control\getNewOfficeCalltype($userConf);
    
    $appInfo = \common\model\getAppPushCnf($userConf['UserAccount']);

    $tokenInfo = \common\model\getTokenInfoByAppMainAccount($userConf['UserAccount']);

    //IOS V4.3版本在新的版本设置为不存在的calltype出现崩溃
    if ($appInfo['AppType'] == 0 && $appInfo['Version'] < 4400 && $callType > 2) {
        $callType = 0;
    }

    $accountInfo = \common\model\getAccountByParentId($userConf['MngID']);

    if ($accountInfo) {
        $commInfo = \office\model\getOfficeInfo($accountInfo['UUID']);
        if ($commInfo) {
            $commConf['calltype_show_phone'] = strval(\util\utility\switchHandle($commInfo['Switch'], DevSwitchEnableLandline));
        } else {
            $commConf['calltype_show_phone'] = "0";
        }
    } else {
        $commConf['calltype_show_phone'] = "0";
    }

    $datas = [
        'ble' => [
            'enable' => $enableBLE,
            'code' => $bleCode,
            'opendoor_type' => $commCode['BLEOpenDoorType']
        ],
        'nfc' => [
            'enable' => $enableNFC,
            'code' => $nfcCode
        ],
        'calltype_personal' => "0",
        'calltype' => $callType,
        'calltype_show_phone' => $commConf['calltype_show_phone'],
        'enable_callkit' => $tokenInfo['EnableCallkit']
    ];
    
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}