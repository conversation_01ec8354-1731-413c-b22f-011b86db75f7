<?php

namespace util\aes;

class MYAES128CBC
{
    const METHOD = 'AES-128-CBC';
    //added by chen<PERSON><PERSON>,新建用户,二维码发送加密秘钥
    const QRCODE_SECRET_KEY = 'Akuvox55069013Akuvox';
    const FACE_MNG_KEY = 'ak202008';
    const AES_IV = '0000000000000000';

    /**
     * Define the number of blocks that should be read from the source file for each chunk.
     * For 'AES-128-CBC' each block consist of 16 bytes.
     * So if we read 10,000 blocks we load 160kb into memory. You may adjust this value
     * to read/write shorter or longer chunks.
     */
    const FILE_ENCRYPTION_BLOCKS = 10000;

    public function encrypt($text)
    {
        $iv = self::AES_IV;
        return openssl_encrypt($text, self::METHOD, self::QRCODE_SECRET_KEY . $text, 0, $iv);
    }

    public function decrypt($text)
    {
        $iv = self::AES_IV;
        $opensslDecrypt = openssl_decrypt($text, self::METHOD, self::QRCODE_SECRET_KEY . $text, 0, $iv);
        return $opensslDecrypt;
    }

    public function getKey($account)
    {
        return substr($account . self::FACE_MNG_KEY, 0, 16);
    }
    
    /**
     * Encrypt the passed file and saves the result in a new file with ".enc" as suffix.
     *
     * @param string $source Path to file that should be encrypted
     * @param string $key    The key used for the encryption
     * @param string $dest   File name where the encryped file should be written to.
     * @return string|false  Returns the file name that has been created or FALSE if an error occured
     */
    public function encryptFile($source, $key, $dest)
    {
        $key = substr(sha1($key, false), 0, 16);
        $iv = self::AES_IV;

        $error = false;
        if ($fpOut = fopen($dest, 'w')) {
            if ($fpIn = fopen($source, 'rb')) {
                $plaintext = file_get_contents($source);
                $ciphertext = openssl_encrypt($plaintext, self::METHOD, $key, OPENSSL_RAW_DATA, $iv);
                fwrite($fpOut, $ciphertext);
                fclose($fpIn);
            } else {
                $error = true;
            }
            fclose($fpOut);
        } else {
            $error = true;
        }

        return $error ? false : $dest;
    }

    /**
     * Dencrypt the passed file and saves the result in a new file, removing the
     * last 4 characters from file name.
     *
     * @param string $source Path to file that should be decrypted
     * @param string $key    The key used for the decryption (must be the same as for encryption)
     * @param string $dest   File name where the decryped file should be written to.
     * @return string|false  Returns the file name that has been created or FALSE if an error occured
     */
    public function decryptFile($source, $key, $dest)
    {
        $key = substr(sha1($key, false), 0, 16);
        $iv = self::AES_IV;

        $error = false;
        if ($fpOut = fopen($dest, 'w')) {
            if ($fpIn = fopen($source, 'rb')) {
                $ciphertext = fread($fpIn, filesize($source));
                $plaintext = openssl_decrypt($ciphertext, self::METHOD, $key, OPENSSL_RAW_DATA, $iv);
                fwrite($fpOut, $plaintext);
                fclose($fpIn);
            } else {
                $error = true;
            }
            fclose($fpOut);
        } else {
            $error = true;
        }

        return $error ? false : $dest;
    }
}
