# Redis连接池实现文档

## 概述

本项目实现了一个高效的Redis连接池，用于优化Redis连接管理，提高应用程序性能和资源利用率。Redis连接池基于webman框架，支持连接复用、健康检查、监控统计等功能，同时支持直连和Sentinel模式。

## 主要特性

- **连接复用**: 避免频繁创建/销毁Redis连接
- **自动管理**: 支持最小/最大连接数控制
- **健康检查**: 定期检查连接有效性，自动重连
- **监控统计**: 提供详细的性能指标和使用统计
- **故障恢复**: 连接失败时自动重试和降级处理
- **向后兼容**: 保持现有代码接口不变
- **Sentinel支持**: 支持Redis Sentinel高可用模式
- **数据库切换**: 支持Redis多数据库切换

## 架构组件

### 1. 核心类

- **RedisConnectionPool**: Redis连接池核心管理类
- **DatabasePool**: Bootstrap启动器，管理连接池生命周期
- **DatabasePoolController**: 管理API控制器

### 2. 配置文件

- **config/redis_pool.php**: Redis连接池配置参数
- **config/bootstrap.php**: 启动器注册

### 3. 中间件集成

- **GlobalResourceMiddle**: 支持Redis连接池的延迟初始化

## 安装和配置

### 1. 配置参数

编辑 `config/redis_pool.php` 文件：

```php
return [
    'min_connections' => 3,        // 最小连接数
    'max_connections' => 10,       // 最大连接数
    'connection_timeout' => 5,     // 连接超时(秒)
    'idle_timeout' => 300,         // 空闲超时(秒)
    'health_check_interval' => 60, // 健康检查间隔(秒)
    'debug' => false,              // 调试模式
];
```

### 2. 环境配置

根据不同环境调整配置：

- **开发环境**: 较少连接数，开启调试
- **测试环境**: 中等连接数，开启调试
- **生产环境**: 较多连接数，关闭调试

### 3. Redis连接模式

支持两种连接模式：

#### 直连模式 (ENABLE_REDIS_SENTINEL = 0)
```php
// 在 define.php 中配置
define('ENABLE_REDIS_SENTINEL', 0);
define('REDISIP', '127.0.0.1');
define('REDISSOCKET', 6379);
define('REDISPW', 'your_password');
```

#### Sentinel模式 (ENABLE_REDIS_SENTINEL = 1)
```php
// 在 define.php 中配置
define('ENABLE_REDIS_SENTINEL', 1);
define('REDIS_SENTINEL_HOSTS', 'sentinel1:26379,sentinel2:26379,sentinel3:26379');
define('REDIS_SENTINEL_TIMEOUT', 5);
define('REDISPW', 'your_password');
```

## 使用方法

### 1. 基本使用

现有代码无需修改，继续使用原有接口：

```php
// 获取Redis连接
$redis = \util\container\getRedis();

// 正常使用Redis操作
$redis->set('key', 'value');
$value = $redis->get('key');
```

### 2. 直接使用连接池

如需直接操作连接池：

```php
use support\bootstrap\DatabasePool;

// 获取Redis连接
$connection = DatabasePool::getRedisConnection();

// 使用连接
$result = $connection['redis']->ping();

// 归还连接
DatabasePool::releaseRedisConnection($connection);
```

### 3. 数据库切换

```php
$redis = \util\container\getRedis();

// 切换到数据库1
$redis->select(1);
$redis->set('key_in_db1', 'value');

// 切换回数据库0
$redis->select(0);
```

## API接口

### Redis连接池管理API

- `GET /admin/redis_pool/status` - 获取Redis连接池状态
- `GET /admin/redis_pool/test` - 测试Redis连接
- `GET /admin/redis_pool/config` - 获取Redis配置信息
- `GET /admin/pool/status` - 获取所有连接池状态
- `GET /admin/pool/info` - 获取详细信息

### 响应示例

```json
{
  "success": true,
  "data": {
    "healthy": true,
    "stats": {
      "current_active": 1,
      "current_idle": 2,
      "current_total": 3,
      "peak_active": 5,
      "total_created": 8,
      "total_destroyed": 5
    },
    "timestamp": "2024-01-01 12:00:00"
  }
}
```

## 测试

### 运行测试

```bash
cd slim-webman
php tests/RedisPoolTest.php
```

### 测试内容

- Redis连接池初始化测试
- 连接获取和释放测试
- 并发访问测试
- 连接池耗尽测试
- 健康检查测试
- Redis命令执行测试

## 监控和告警

### 关键指标

- **连接池使用率**: 当前活跃连接数 / 总连接数
- **连接成功率**: 成功连接数 / 总请求数
- **平均等待时间**: 获取连接的平均等待时间
- **连接错误数**: 连接失败的次数

### 告警阈值

在配置文件中设置告警阈值：

```php
'monitoring' => [
    'alert_thresholds' => [
        'pool_utilization_percent' => 90,      // 连接池使用率告警
        'connection_errors_per_minute' => 5,   // 连接错误率告警
        'avg_wait_time_ms' => 500,             // 平均等待时间告警
    ]
]
```

## 性能优化建议

### 1. 连接数配置

- **最小连接数**: 根据应用基础负载设置
- **最大连接数**: 考虑Redis服务器连接限制
- **建议比例**: 最大连接数 = 最小连接数 × 3-4

### 2. 超时配置

- **连接超时**: 通常设置为3-5秒
- **空闲超时**: 根据应用访问模式设置，建议5-10分钟
- **健康检查**: 建议1-2分钟间隔

### 3. 环境调优

- **开发环境**: min=2, max=5
- **测试环境**: min=3, max=8  
- **生产环境**: min=5, max=20

## 故障排除

### 常见问题

1. **Redis连接池启动失败**
   - 检查Redis配置是否正确
   - 确认Redis服务是否可访问
   - 查看错误日志获取详细信息

2. **连接获取超时**
   - 检查连接池配置是否合理
   - 监控连接池使用情况
   - 考虑增加最大连接数

3. **Sentinel模式连接失败**
   - 检查Sentinel配置是否正确
   - 确认Sentinel服务是否正常
   - 验证主节点是否可访问

### 日志查看

Redis连接池相关日志会输出到系统错误日志中：

```bash
# 查看Redis连接池日志
tail -f /var/log/php/error.log | grep "RedisConnectionPool"
```

## 升级和维护

### 版本兼容性

- 当前版本与现有代码完全兼容
- 如连接池不可用会自动降级到原有方式
- 支持热重启和平滑升级

### 维护建议

- 定期检查Redis连接池状态和指标
- 根据业务增长调整连接数配置
- 监控Redis服务器连接数使用情况
- 定期更新和优化配置参数

## 与MySQL连接池的区别

| 特性 | MySQL连接池 | Redis连接池 |
|------|-------------|-------------|
| 默认最小连接数 | 5 | 3 |
| 默认最大连接数 | 20 | 10 |
| 健康检查命令 | SELECT 1 | PING |
| 连接超时 | 30秒 | 5秒 |
| 支持模式 | 直连 | 直连 + Sentinel |
| 数据库切换 | 不支持 | 支持多DB |

## 技术支持

如遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志和错误信息
3. 使用API接口获取详细状态信息
4. 运行测试用例验证功能

---

**注意**: Redis连接池功能已集成到现有系统中，无需修改业务代码即可享受性能提升。
