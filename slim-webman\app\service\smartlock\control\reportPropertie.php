<?php

namespace  smartlock\control;
require_once __DIR__ . "/../../../util/smartlockAdapt.php";
require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../../common/model/smartLock.php";

function lockReportPropertie($request, $response)
{
    $user =  $request->header('Username');
    $pwd = $request->header('Password');
    $mqttClientId = $request->header('Client-Id');
    $clientId = \util\smartlockAdapt\mqttClientToRealClient($mqttClientId);

    //终端锁鉴权
    $userParts = explode('-', $user);
    $deviceStyle = $userParts[0]; 
    $deviceMac = $userParts[1]; 
    if($deviceStyle == SMARTLOCK_MQTT_CLIENT_FLAG_SL50
       && \common\model\SmartLockMqttAuthCheck($deviceMac, $clientId, $pwd))
    {
        $jsondata = $request->getParsedBody();
        //修改键名和mqtt上来的消息一致
        if (array_key_exists('property', $jsondata)) {
            $jsondata['command'] = $jsondata['property']; // 新的键名
            unset($jsondata['property']); // 删除旧的键
        }        
        $json_data1 = json_encode($jsondata);
        $traceID = \util\utility\createTraceID(10); 
        smartlockHttpUpMessageRoute($clientId, $json_data1, $traceID);         
    }
    else
    {   
        \util\log\akcsLog::debug("auth error. user:$user, client_id:$mqttClientId, pwd:$pwd");
        $response_data = [
            'success' => false,
            'timestamp' => time(),
            'result' => array()
        ];
        return json($response_data);             
    }

    $response_data = [
        'success' => true,
        'timestamp' => time(),
        'result' => array()
    ];
    return json($response_data);
}