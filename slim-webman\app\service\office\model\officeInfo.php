<?php

namespace office\model;

use PDO;

require_once __DIR__ . "/../../common/model/manegeFeature.php";

function getOfficeInfo($accountUUID)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select Switch,IsNew from OfficeInfo where AccountUUID = :accountUUID");
    $sth->bindParam(':accountUUID', $accountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result;
}

function getOfficeUserMngSipType($parentID)
{
    $db = \util\container\getDb();
    $sipType = 0;
    $rtpConfuse = 0;

    $sth = $db->prepare("select ManageGroup From Account where ID=:ID");
    $sth->bindParam(':ID', $parentID, PDO::PARAM_INT);
    $ret = $sth->execute();
    $mngid = $sth->fetch(PDO::FETCH_ASSOC);
    $mngid = $mngid['ManageGroup'];

    $sth = $db->prepare("select SipType,Flags from Account where ID=:ID");
    $sth->bindParam(':ID', $mngid, PDO::PARAM_INT);
    $ret = $sth->execute();
    $mngid = $sth->fetch(PDO::FETCH_ASSOC);
    $sipType = $mngid['SipType'];
    $rtpConfuse = $mngid['Flags'] & RTP_CONFUSE;

    return ["SipType" => $sipType, "RtpConFuse" => $rtpConfuse];
}

function officeFeaturePlanIsExpire($accountUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select FeatureExpireTime < now() as expire, ISNULL(FeatureExpireTime) as fnull from OfficeInfo where AccountUUID = :accountUUID");
    $sth->bindParam(':accountUUID', $accountUUID, PDO::PARAM_STR);
    $sth->execute();
    $checkexpire = $sth->fetch(PDO::FETCH_ASSOC);
    if ($checkexpire['fnull'] == 1) {
        return true;
    }
    if ($checkexpire['expire'] == 1) {
        return true;
    }

    return false;
}

function getIsShowTempKey($resultToken, $accountInfo)
{
    //tmpkey显示控制
    if (!\office\model\officeFeaturePlanIsExpire($accountInfo['UUID']) && \common\model\checkCommunityFeaturePlan($resultToken['MngID'], FeatureItemTempkey)) {
        $isShowTmpkey = $resultToken['TempKeyPermission']; //没过期且有对应得权限则按照配置
    } else {
        $isShowTmpkey = 1;
    }

    if (null == $isShowTmpkey) {
        $isShowTmpkey = 0;
    }
    
    return $isShowTmpkey;
}