<?php
namespace common\control;

require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/../model/personalDevices.php";
require_once __DIR__ . "/../model/saltoLock.php";
require_once __DIR__ . "/../model/dormakabaLock.php";
require_once __DIR__ . "/../model/accessGroupDevices.php";
require_once __DIR__ . "/../../resident/model/communityInfo.php";
require_once __DIR__ . '/../model/thirdLock.php';
require_once __DIR__ . "/../../resident/model/devices.php";

function getDormakabaLockList($accessDevs = [])
{
    $userConf = \util\container\getUserData();
    $userRole = $userConf["Role"];

    $dormakabaLockList = array();
    if ($userRole == ROLE_TYPE_COMMUNITY_PM) {
        $dormakabaLockList = getPmDormakabaLockList();
    } elseif ($userRole == ROLE_TYPE_COMMUNITY_MASTER || $userRole == ROLE_TYPE_COMMUNITY_SLAVE) {
        $dormakabaLockList = getCommunityEnduserDormakabaLockList($accessDevs);
    } elseif ($userRole == ROLE_TYPE_PERSONNAL_MASTER || $userRole == ROLE_TYPE_PERSONNAL_SLAVE) {
        $dormakabaLockList = getPersonnalEnduserDormakabaLockList();
    }

    //  link的设备默认在线,兼容ios处理
    transferLinkLockStatus($dormakabaLockList);

    return ["lock_dev_list" => $dormakabaLockList];
}

function transferLinkLockStatus(&$lockList)
{
    foreach ($lockList as &$lock) {
        if (strlen($lock["bonded_devices"]["mac"]) > 0) {
            $lock["connected"] = strval(THIRD_LOCK_CONNECTED_ONLINE);
        }
    }
}

// pm下发公共和楼栋的所有lock,无需判断权限组
function getPmDormakabaLockList()
{    
    $userConf = \util\container\getUserData();
    $unitLockList = \common\model\getCommunityUnitDormakabaLockList($userConf["MngUUID"]);
    $publicLockList = \common\model\getCommunityPublicDormakabaLockList($userConf["MngUUID"]);
    $allLockList = array_merge($unitLockList, $publicLockList);
    if(empty($allLockList)) {
        return [];
    }

    getLinkDeviceInfo($allLockList);
    return adaptApiLockList($allLockList);
}

// 社区账号下发pub/unit/apt所有有权限的lock
function getCommunityEnduserDormakabaLockList($accessDevs)
{
    $userConf = \util\container\getUserData();
    $aptLockList = \common\model\getAptDormakabaLockList($userConf["NodeUUID"]);
    $unitLockList = \common\model\getCommunityUnitDormakabaLockList($userConf["MngUUID"]);
    $publicLockList = \common\model\getCommunityPublicDormakabaLockList($userConf["MngUUID"]);
    $allLockList = array_merge($aptLockList, $unitLockList, $publicLockList);
    if(empty($allLockList))
    {
        return [];
    }

    // 获取link设备的mac信息
    getLinkDeviceInfo($allLockList);
    // 获取有权限的lock列表
    $accessLockList = getAccessLockList($allLockList, $accessDevs, $userConf['UserAccount']);
    // 适配app api数据格式
    $adaptApiLockList = adaptApiLockList($accessLockList);
    
    return $adaptApiLockList ;
}

// 单住户账号下发apt内所有lock
function getPersonnalEnduserDormakabaLockList()
{
    $userConf = \util\container\getUserData();
    $aptLockList = \common\model\getAptDormakabaLockList($userConf["NodeUUID"]);
    if(empty($aptLockList)) {
        return [];
    }
    
    getLinkDeviceInfo($aptLockList);
    return adaptApiLockList($aptLockList);
}

// 转换为下发给app的数据格式
function adaptApiLockList($lockList)
{
    $adaptApiLockList = array();

    foreach ($lockList as $eachLock) {
        $payActive = \common\model\thirdLock\getThirdLockPayActiveStatus($eachLock['UUID'], DORMAKABA_LOCK_TYPE);
        $adaptedLock = array(
            "brand" => "Dormakaba",
            "location" => $eachLock["Name"],
            "uuid" => $eachLock["ThirdUUID"],
            "type" => DORMAKABA_LOCK_TYPE,
            "connected" => strval(THIRD_LOCK_CONNECTED_OFFLINE), // 默认离线, 异步接口再更新状态
            "pay_active" => intval($payActive)
        );

        // 构建绑定设备信息
        $bondedDevices = array();
        $bondedDevices['mac'] = $eachLock['mac'] ? $eachLock['mac'] : "";
        // log运算将1248转换为0123,接口请求时开0123relay,每把锁只能绑定一个relay
        $bondedDevices['relay_id'] = $eachLock['Relay'] ? log($eachLock['Relay'], 2) : -1;

        // 将绑定设备信息添加到适配后的锁数据中
        $adaptedLock['bonded_devices'] = $bondedDevices;

        // 将适配后的锁数据添加到结果数组的末尾
        $adaptApiLockList[] = $adaptedLock;
    }

    return $adaptApiLockList;
}

function getLinkDeviceInfo(&$allLockList)
{
    foreach ($allLockList as &$lock) {
        if (!$lock["DeviceUUID"]) {
            continue;
        }

        if ($lock["ProjectType"] == DORMAKABA_LOCK_PROJECT_TYPE_COMMUNITY) {
            $bindDeviceInfo = \common\model\getDevicesInfoByUUID($lock["DeviceUUID"]);
        } elseif ($lock["ProjectType"] ==  DORMAKABA_LOCK_PROJECT_TYPE_PERSONNAL) {
            $bindDeviceInfo = \common\model\getPersonalDevicesInfoByUUID($lock["DeviceUUID"]);
        }

        $lock["mac"] = $bindDeviceInfo['MAC'];
    }
}

function getAccessLockList($allLockList, $accessDevs, $user)
{
    $accessLockList = array();
    foreach ($allLockList as $lock) {
        // 没link设备的lock
        if (empty($lock["DeviceUUID"])) {
            $accessGroup = \common\model\thirdLock\getAccessGroupThirdLock($lock['UUID'], DORMAKABA_LOCK_TYPE, $user);
            //APT内或者有相关权限组
            if($lock['Grade'] == COMMUNITY_DEVICE_TYPE_PERSONAL || $accessGroup)
            {
                $accessLockList[] = $lock;
            }
            continue;
        }

        // 判断mac是否有权限
        if (!array_key_exists($lock["mac"], $accessDevs)) {
            continue;
        }

        // 判断relay是否有权限
        $bindRelay = intval($lock["Relay"]);
        $accessRelay = intval($accessDevs[$lock["mac"]]['Relay']);
        if (\util\common\checkRelay($accessRelay, $bindRelay)) {
            $accessLockList[] = $lock;
        } else{
            \util\log\akcsLog::debug("dormakaba bind relay has no access, bindRelay = $bindRelay, accessRelay = $accessRelay");
        }
    }

    return $accessLockList;
}

function openDormakabaDoor($userConf, $lockUUID)
{
    $isJson = true;
    $heaader = array();
    $linkMAC = "";
    $lockInfo = \common\model\getDormakabaLockInfo($lockUUID);
    if (isset($lockInfo['DeviceUUID'])) {
        $deviceInfo = \common\model\getDeviceInfoByRoleAndUUID($userConf['Role'], $lockInfo['DeviceUUID']);
        if($deviceInfo) {
            $linkMAC = $deviceInfo['MAC'];
        }
    }

    // 判断锁link的mac relay是否有权限
    if (strlen($lockInfo['DeviceUUID']) > 0) {
        $openDoorRelay = $lockInfo['Relay'] - 1;
        // 只能link relay, 不能link security relay, 所以security relay传null
        $canOpendoor = \common\model\checkOpendoorPermission($userConf, $linkMAC, $openDoorRelay, null);
        if (!$canOpendoor) {
            \util\log\akcsLog::debug("dormakaba open door has no access, linkMAC = {$linkMAC}, linkRelay = {$lockInfo['Relay']}");
            return ERR_CODE_NO_PERMISSION;
        }
    }
    
    $data = array();
    $data["link_mac"] = $linkMAC;
    $data['lock_uuid'] = $lockUUID;
    $data['role'] = $userConf['Role'];
    $data['initiator'] = $userConf['Name'];
    $data['lock_name'] = $lockInfo["Name"];
    $data['lock_type'] = DORMAKABA_LOCK_TYPE;
    $data["account"] = $userConf['UserAccount'];
    $data['personal_account_uuid'] = $userConf['NodeUUID'];
    $data['personal_account_uuid_for_operator'] = $userConf['UUID']; // 添加操作者UUID
    $data['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR;

    // 请求到cslinke进行开锁
    $response = \util\common\httpRequest(
        "post",
        "http://" . CSLINKER_HTTP_SERVER . '/app_open_dormakaba_lock',
        $heaader,
        $data,
        $isJson,
        20
    );

    $result = ERR_CODE_OPEN_DOOR_FAILED;
    $json_data = json_decode($response, true);

    if ($json_data['code'] == 0 && $json_data['data']['err_code'] == \common\model\FAILED_TYPE_SUCCESS) {
        $result = ERR_CODE_SUCCESS;
    } else if ($json_data['data']['err_code'] == \common\model\THIRD_LOCK_FAILED_TYPE_DORMAKABA_NOT_EXIST) {
        $result = ERR_CODE_DORMAKABA_LOCK_NOT_EXIST;
    } else {
        $result = ERR_CODE_OPEN_DOOR_FAILED;
    }

    \util\log\akcsLog::debug('dormakaba open door response=' . $response);
    return $result;
}

// 获取锁的实时状态
function getDormakabaLockRealTimeStatus($userConf, &$dormakabaLockList)
{
    if (!\common\model\RelatedDormakabaLock($userConf['MngUUID'])) {
        return;
    }
    
    if ($userConf["Role"] == ROLE_TYPE_COMMUNITY_MASTER || $userConf["Role"] == ROLE_TYPE_COMMUNITY_SLAVE) {
        $aptMacs = [];
        $accessDevs = [];
        \resident\model\getNewCommDevicesList($aptMacs, $accessDevs);
        $dormakabaLockList = getCommunityEnduserDormakabaLockList($accessDevs);
    } elseif ($userConf["Role"] == ROLE_TYPE_COMMUNITY_PM) {
        $dormakabaLockList = getPmDormakabaLockList();
    } elseif ($userConf["Role"] == ROLE_TYPE_PERSONNAL_MASTER || $userConf["Role"] == ROLE_TYPE_PERSONNAL_SLAVE) {
        $dormakabaLockList = getPersonnalEnduserDormakabaLockList();
    }

    // 提取锁的uuid
    $thirdUUIDString = implode(',', array_map(function($item) { return $item['uuid'];}, $dormakabaLockList));

    if (empty($thirdUUIDString)) {
        return;
    }

    // 请求锁实时状态
    $output = \util\common\httpRequest("get", "http://" . CSLINKER_HTTP_SERVER . '/dormakaba_lock_status?thirdUUID='.$thirdUUIDString, $heaader = []);

    // 更新锁的状态
    $realTimeLockStatus = json_decode($output, true);
    if (isset($realTimeLockStatus['data']['list'])) {
        $realTimeLockStatusList = $realTimeLockStatus['data']['list'];
    
        // 创建一个映射以便于快速查找
        $uuidToConnectedMap = [];
        foreach ($realTimeLockStatusList as $lockInfo) {
            if (isset($lockInfo['thirdUUID']) && isset($lockInfo['connected'])) {
                $uuidToConnectedMap[$lockInfo['thirdUUID']] = $lockInfo['connected'] ? "1" : "0";
            }
        }
    
        // 更新 dormakabaLockList 中的 connected 值
        foreach ($dormakabaLockList as &$lock) {
            if (isset($lock['uuid']) && isset($uuidToConnectedMap[$lock['uuid']])) {
                $lock['connected'] = $uuidToConnectedMap[$lock['uuid']];
            }
        }
        unset($lock); // 释放引用
    }
}