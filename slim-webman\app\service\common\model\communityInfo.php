<?php

namespace common\model;

use PDO;

function updateCommunityPackageDetectionStatus($enablePackageDetection, $mngId)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("UPDATE CommunityInfo SET EnablePackageDetection = :EnablePackageDetection WHERE AccountID = :AccountID");
    $sth->bindParam(':EnablePackageDetection', $enablePackageDetection, PDO::PARAM_INT);
    $sth->bindParam(':AccountID', $mngId, PDO::PARAM_INT);
    $sth->execute();
}

function updateCommunitySoundDetectionStatus($enableSoundDetection, $soundType, $mngId)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("UPDATE CommunityInfo SET EnableSoundDetection = :EnableSoundDetection, SoundType = :SoundType WHERE AccountID = :AccountID");
    $sth->bindParam(':EnableSoundDetection', $enableSoundDetection, PDO::PARAM_INT);
    $sth->bindParam(':SoundType', $soundType, PDO::PARAM_INT);
    $sth->bindParam(':AccountID', $mngId, PDO::PARAM_INT);
    $sth->execute();
}

function getCommunityDetectionInfo($mngId)
{
    $db =  \util\container\getDb();
    // EnableSoundDetection, SoundType暂时不上
    $sth = $db->prepare("/*master*/ select EnableMotion, MotionTime, EnablePackageDetection from CommunityInfo where AccountID = :mngId");
    $sth->bindParam(':mngId', $mngId, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    return $result;
}