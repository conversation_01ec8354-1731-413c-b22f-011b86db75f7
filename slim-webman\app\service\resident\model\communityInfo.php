<?php

namespace resident\model;

use PDO;


function getCommunityInfo($mngID)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select Switch,IsNew,CommunityPlan from CommunityInfo where AccountID = :mngid");
    $sth->bindParam(':mngid', $mngID, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result;
}

function checkFeaturePlanIsExpire($mngID)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select FeatureExpireTime < now() as expire, ISNULL(FeatureExpireTime) as fnull from CommunityInfo where AccountID = :account_id");
    $sth->bindParam(':account_id', $mngID, PDO::PARAM_INT);
    $sth->execute();
    $checkexpire = $sth->fetch(PDO::FETCH_ASSOC);
    if ($checkexpire['fnull'] == 1) {
        return true;
    }
    if ($checkexpire['expire'] == 1) {
        return true;
    }
    return false;
}

function checkIsNewCommunity($userInfo)
{
    $db = \util\container\getDb();
    $communityID = $userInfo['MngID'];
    $sth = $db->prepare("select IsNew from CommunityInfo where AccountID = :community_id");
    $sth->bindParam(':community_id', $communityID, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    $isNewCommunity = $result['IsNew'];
    return $isNewCommunity;
}