<?php

namespace common\control;
use Webman\Http\Request;
require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../model/twoFactorAuthCode.php";
require_once __DIR__ . "/../model/accountUserInfo.php";
require_once __DIR__ . "/../model/personalAccount.php";
require_once __DIR__ . "/../model/accountMap.php";
require_once __DIR__ . "/../model/oemHandle.php";

function sendTwoFactorAuthCode($request, $response)
{
    $bodyValue = $request->getParsedBody();
    $loginAccount = $bodyValue['login_account'];
    if (!$loginAccount)
    {
        return \util\response\setResponseMessage($response, ERR_CODE_LOGIN_ACCOUNT_NOT_EXIST);
    }
    $userInfo = \common\model\getAccountUserInfoByLoginAccount($loginAccount);
    if (!$userInfo) 
    {
        return \util\response\setResponseMessage($response, ERR_CODE_LOGIN_ACCOUNT_NOT_EXIST);
    }
    $account = \common\model\getAccountByUserInfoUUID($userInfo['UUID']);
    if (!$account) 
    {
        return \util\response\setResponseMessage($response, ERR_CODE_LOGIN_ACCOUNT_NOT_EXIST);
    }
    $code = mt_rand(100000,999999);
    \common\model\recordVerificationCode($loginAccount, $code);

    $oemName = \common\model\getOemNameByAccountInfo($account);
    if($account['Grade'] == GRADE_TYPE_PM)
    {
        //多站点pm 邮件使用主站点名字
        $mainAccountInfo = \common\model\getPersonalAccountInfoByAccount($userInfo['AppMainUserAccount']);
        
        //pm的loginAccount是email
        sendTwoFactorAuthCodeEmail($code, $loginAccount, $mainAccountInfo['Name'], $mainAccountInfo['Language'], $oemName);
    }
    else //ins
    {
        sendTwoFactorAuthCodeEmail($code, $userInfo['Email'], $loginAccount, $account['Language'], $oemName);
    }
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function handleTwoFactorAuthentication($request)
{
    $postDatas = $request->getParsedBody();
    $login_account = $postDatas['login_account'];
    $tempTokenFromApp = $postDatas['two_factor_auth_temp_token'];

    if (empty($login_account) || empty($tempTokenFromApp)) {
        return false; 
    }
    //获取redis记录的tempToken
    $tempTokenKey = 'APP_TwoFactorAuth_Temp_Token_' . $login_account;
    
    $tempToken = \common\model\getTempToken($tempTokenKey);
    if ($tempTokenFromApp != $tempToken) 
    {
        return false; // 鉴权失败
    }
    return true; // 鉴权成功
}