<?php

namespace common\model\iTec;

use PDO;

const DB_ITECLOCK_TABLE = 'ITecLock';
const DB_ITECLOCK_FIELD = [
    'UUID',
    'Name',
    'LockId',
    'DeviceUUID',
    'Relay',
    'ProjectType',
    'IsBind',
    'AccountUUID',
    'CommunityUnitUUID',
    'PersonalAccountUUID',
    'Grade',
    'IsOnline'
];
// 获取位于项目下的所有lock
function getLockListByAccountUUID($projectUUID)
{
    return \util\container\medooDb()->select(
        DB_ITECLOCK_TABLE,
        DB_ITECLOCK_FIELD,
        ['AccountUUID' => $projectUUID]
    );
}

// 获取位于社区public area的所有lock
function getPublicLockList($projectUUID)
{
    return \util\container\medooDb()->select(
        DB_ITECLOCK_TABLE,
        DB_ITECLOCK_FIELD,
        ['AccountUUID' => $projectUUID, 'Grade' => COMMUNITY_DEVICE_TYPE_PUBLIC]
    );
}

// 获取位于社区unit area所有lock
function getUnitLockList($projectUUID)
{
    return \util\container\medooDb()->select(
        DB_ITECLOCK_TABLE,
        DB_ITECLOCK_FIELD,
        ['AccountUUID' => $projectUUID, 'Grade' => COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT]
    );
}

// 获取家庭独占的所有lock
function getAptLockList($nodeUUID)
{
    return \util\container\medooDb()->select(
        DB_ITECLOCK_TABLE,
        DB_ITECLOCK_FIELD,
        ['PersonalAccountUUID' => $nodeUUID, 'Grade' => COMMUNITY_DEVICE_TYPE_PERSONAL]
    );
}

// 获取lock信息
function getLockInfo($UUID)
{
    return \util\container\medooDb()->get(
        DB_ITECLOCK_TABLE,
        DB_ITECLOCK_FIELD,
        ['UUID' => $UUID]
    );
}
