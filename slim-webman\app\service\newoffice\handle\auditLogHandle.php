<?php
// 审计日志处理
namespace newoffice\auditlog;

require_once(dirname(__FILE__) . '/../../../config/define.php');
require_once(dirname(__FILE__) . '/../../../notify/notify_newoffice.php');

/*
{
  "trace_id": "最大长度64",
  "timestamp": 0,
  "data": {
    "user": "用户uuid",
    "userType": "super|dis|subDis|ins|pm|admin|endUser",
    "action": "当前用户操作数据所属动作，参考全局配置文件中action的定义，例如add",
    "model": "当前用户操作数据所属的模块，参考全局配置文件中model的定义，例如officeAdmin",
    "ip": "用户id地址",
    "list": [
      {
        "operaType": "当前项的操作动作类型，create，update，delete",
        "model": "当前项的所属的模块，参考全局配置文件中model的定义，例如officeAdmin",
        "action": "当前项的动作，参考全局配置文件中action的定义，例如add",
        "object": {
          "uuid": "操作对象uuid",
          "name": "操作对象描述名字，一般使用对应的name，非必须，可放空"
        },
        "item": [
          {
            "dataType": "数据类型   input(用户输入类型)，
                                    enum(系统定义枚举类型，需要让前端显示多语言词条，若需要统一由web后端定义，和前端对齐，不可随便新增类型)，
                                    select(其他模块的uuid)",

            "key": "描述数据的key，参考全局配置文件中key的定义，不可以随便新增，若需要统一由web后端定义，和前端对齐",
            "value": "插入的值，删除前的值，更新后的值",
            "valueBefore": "更新前的值，更新时需要",
            "model": "数据所属的模块，参考全局配置文件中model的定义，例如officeAdmin",
            "isEncrypt": true,
            "isHidden": true
          }
        ]
      }
    ]
  }
}
*/

// ACTION 定义
const AUDIT_ACTION_TYPE_CREATE              = "create";
const AUDIT_ACTION_TYPE_UPDATE              = "update";
const AUDIT_ACTION_TYPE_DELETE              = "delete";
const AUDIT_ACTION_TYPE_BATCHDELETE         = "batchDelete";
const AUDIT_ACTION_TYPE_IMPORT              = "import";
const AUDIT_ACTION_TYPE_LOGIN               = "login";
const AUDIT_ACTION_TYPE_ADMINAPPLOGIN       = "adminAppLogin";
const AUDIT_ACTION_TYPE_PMAPPLOGIN          = "pmAppLogin";
const AUDIT_ACTION_TYPE_INSTALLERAPPLOGIN   = "installerAppLogin";

// 根据用户角色获取用户类型
function getUserType($role)
{
    // super|dis|subDis|ins|pm|admin|endUser
    if ($role == ROLE_TYPE_OFFICE_NEW_PER) {
        return "personnel";
    } elseif ($role == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        return "officeAdminApp";
    }

    return "personnel";
}

// 根据用户角色获取mode
function getModeByRole($role)
{
    if ($role == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        return 'officeAdminApp';
    }

    return 'officePersonnel';
}

function MinuteToTimeString($minutes)
{
    if ($minutes < 0) {
        return "00:00";
    }
    $hours = intdiv($minutes, 60);
    $remainingMinutes = $minutes % 60;

    return sprintf("%02d:%02d", $hours, $remainingMinutes);
}

/*
// 用户登录(登录的审计日志在csgate中实现)
function handleUserAppLogin(&$request, &$response)
{
    $userConf = \util\container\getUserData();
    
    // 获取mode
    $mode = 'officePersonnel';
    if ($userConf['Role'] == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        $mode = 'officeAdmin';
    }
    
    // 设置用户信息
    $roleSet = array();
    $roleSet['dis'] = \common\model\getDisUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['ins'] =  \common\model\getInsUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['project'] = $userConf['ParentUUID'];
    $roleSet['company'] = $userConf['OfficeCompanyUUID'];
    $roleSet['mainUser'] = $userConf['UUID'];

    // 设置审计日志数据
    $audio_data = array();
    $audio_data['roleSet'] = $roleSet;
    $audio_data['user'] = $userConf['UUID'];
    $audio_data['userType'] = getUserType($userConf['Role']);
    $audio_data['action'] = AUDIT_ACTION_TYPE_LOGIN;
    $audio_data['model'] = "office";
    $audio_data['ip'] = $request->getRealIp();
    $audio_data['list'] = array();

    // 发送审计日志
    $json_data = array();
    $json_data['trace_id'] = \util\container\getTraceID();
    $json_data['timestamp'] = time() * 1000000;
    $json_data['data'] = $audio_data;

    AuditLogNotify(PROJECT_TYPE_NEW_OFFICE, $json_data);
}

// adminapp登录(登录的审计日志在csgate中实现)
function handleAdminAppLogin(&$request, &$response)
{
    $userConf = \util\container\getUserData();

    // 设置用户信息
    $roleSet = array();
    $roleSet['dis'] = \common\model\getDisUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['ins'] =  \common\model\getInsUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['project'] = $userConf['ParentUUID'];
    $roleSet['company'] = $userConf['OfficeCompanyUUID'];
    $roleSet['mainUser'] = $userConf['UUID'];

    // 设置审计日志数据
    $audio_data = array();
    $audio_data['roleSet'] = $roleSet;
    $audio_data['user'] = $userConf['UUID'];
    $audio_data['userType'] = getUserType($userConf['Role']);
    $audio_data['action'] = AUDIT_ACTION_TYPE_ADMINAPPLOGIN;
    $audio_data['model'] = "officeAdmin";
    $audio_data['ip'] = $request->getRealIp();
    $audio_data['list'] = array();

    // 发送审计日志
    $json_data = array();
    $json_data['trace_id'] = \util\container\getTraceID();
    $json_data['timestamp'] = time() * 1000000;
    $json_data['data'] = $audio_data;

    AuditLogNotify(PROJECT_TYPE_NEW_OFFICE, $json_data);
}
*/

// 上传人脸
function handleUploadFace(&$request, &$response)
{
    $userConf = \util\container\getUserData();
    // 获取mode
    $mode = getModeByRole($userConf['Role']);

    // 设置用户信息
    $roleSet = array();
    $roleSet['dis'] = \common\model\getDisUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['ins'] =  \common\model\getInsUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['project'] = $userConf['ParentUUID'];
    $roleSet['company'] = $userConf['OfficeCompanyUUID'];
    $roleSet['mainUser'] = $userConf['UUID'];

    // 设置操作数据
    $audio_item = array();
    $audio_item['dataType'] = "select";
    $audio_item['key'] = "owner";
    $audio_item['value'] = $userConf['UUID'];
    $audio_item['valueBefore'] = "";
    $audio_item['valueComplex'] = [];
    $audio_item['valueComplexBefore'] = [];
    $audio_item['model'] = $mode;
    $audio_item['isEncrypt'] = false;
    $audio_item['isHidden'] = false;

    // 设置操作项
    $audio_list_item = array();
    $audio_list_item['operaType'] = "create";
    $audio_list_item['model'] = "officeFace";
    $audio_list_item['action'] = AUDIT_ACTION_TYPE_CREATE;
    $audio_list_item['object'] = array(
        'uuid' => $userConf['UUID'],
        'name' => $userConf['Name']
    );
    $audio_list_item['item'] = array($audio_item);

    // 设置审计日志数据
    $audio_data = array();
    $audio_data['roleSet'] = $roleSet;
    $audio_data['user'] = $userConf['UUID'];
    $audio_data['userType'] = getUserType($userConf['Role']);
    $audio_data['action'] = AUDIT_ACTION_TYPE_CREATE;
    $audio_data['model'] = "officeFace";
    $audio_data['ip'] = $request->getRealIp();
    $audio_data['list'] = array($audio_list_item);

    // 发送审计日志
    $json_data = array();
    $json_data['trace_id'] = \util\container\getTraceID();
    $json_data['timestamp'] = time() * 1000000;
    $json_data['data'] = $audio_data;

    AuditLogNotify(PROJECT_TYPE_NEW_OFFICE, $json_data);
}

// 删除人脸
function handleDeleteFace(&$request, &$response)
{
    $userConf = \util\container\getUserData();
    // 获取mode
    $mode = getModeByRole($userConf['Role']);

    // 设置用户信息
    $roleSet = array();
    $roleSet['dis'] = \common\model\getDisUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['ins'] =  \common\model\getInsUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['project'] = $userConf['ParentUUID'];
    $roleSet['company'] = $userConf['OfficeCompanyUUID'];
    $roleSet['mainUser'] = $userConf['UUID'];

    // 设置操作数据
    $audio_item = array();
    $audio_item['dataType'] = "select";
    $audio_item['key'] = "owner";
    $audio_item['value'] = $userConf['UUID'];
    $audio_item['valueBefore'] = "";
    $audio_item['valueComplex'] = [];
    $audio_item['valueComplexBefore'] = [];
    $audio_item['model'] = $mode;
    $audio_item['isEncrypt'] = false;
    $audio_item['isHidden'] = false;

    // 设置操作项
    $audio_list_item = array();
    $audio_list_item['operaType'] = "delete";
    $audio_list_item['model'] = "officeFace";
    $audio_list_item['action'] = AUDIT_ACTION_TYPE_DELETE;
    $audio_list_item['object'] = array(
        'uuid' => $userConf['UUID'],
        'name' => $userConf['Name']
    );
    $audio_list_item['item'] = array($audio_item);

    // 设置审计日志数据
    $audio_data = array();
    $audio_data['roleSet'] = $roleSet;
    $audio_data['user'] = $userConf['UUID'];
    $audio_data['userType'] = getUserType($userConf['Role']);
    $audio_data['action'] = AUDIT_ACTION_TYPE_DELETE;
    $audio_data['model'] = "officeFace";
    $audio_data['ip'] = $request->getRealIp();
    $audio_data['list'] = array($audio_list_item);

    // 发送审计日志
    $json_data = array();
    $json_data['trace_id'] = \util\container\getTraceID();
    $json_data['timestamp'] = time() * 1000000;
    $json_data['data'] = $audio_data;

    AuditLogNotify(PROJECT_TYPE_NEW_OFFICE, $json_data);
}

// 设置calltype key=calltype
function handleSetCallType(&$request, $old_callType, $new_callType)
{
    $userConf = \util\container\getUserData();

    // 获取mode
    $mode = getModeByRole($userConf['Role']);

    // 设置用户信息
    $roleSet = array();
    $roleSet['dis'] = \common\model\getDisUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['ins'] =  \common\model\getInsUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['project'] = $userConf['ParentUUID'];
    $roleSet['company'] = $userConf['OfficeCompanyUUID'];
    $roleSet['mainUser'] = $userConf['UUID'];

    // 根据web反馈和前端定义的值，进行映射
    $callTypeMap = [
        "0" => "app&device",
        "1" => "phone&device",
        "2" => "app&device-phone",
    ];
    // 设置操作数据
    $audio_item = array();
    $audio_item['dataType'] = "enum";
    $audio_item['key'] = "callType";
    $audio_item['value'] = isset($callTypeMap[$new_callType]) ? $callTypeMap[$new_callType] : $new_callType;
    $audio_item['valueBefore'] = isset($callTypeMap[$old_callType]) ? $callTypeMap[$old_callType] : $old_callType;
    $audio_item['valueComplex'] = [];
    $audio_item['valueComplexBefore'] = [];
    $audio_item['model'] = $mode;
    $audio_item['isEncrypt'] = false;
    $audio_item['isHidden'] = false;

    // 设置操作项
    $audio_list_item = array();
    $audio_list_item['operaType'] = "update";
    $audio_list_item['model'] = $mode;
    $audio_list_item['action'] = AUDIT_ACTION_TYPE_UPDATE;
    $audio_list_item['object'] = array(
        'uuid' => $userConf['UUID'],
        'name' => $userConf['Name']
    );
    $audio_list_item['item'] = array($audio_item);

    // 设置审计日志数据
    $audio_data = array();
    $audio_data['roleSet'] = $roleSet;
    $audio_data['user'] = $userConf['UUID'];
    $audio_data['userType'] = getUserType($userConf['Role']);
    $audio_data['action'] = AUDIT_ACTION_TYPE_UPDATE;
    $audio_data['model'] = $mode;
    $audio_data['ip'] = $request->getRealIp();
    $audio_data['list'] = array($audio_list_item);

    // 发送审计日志
    $json_data = array();
    $json_data['trace_id'] = \util\container\getTraceID();
    $json_data['timestamp'] = time() * 1000000;
    $json_data['data'] = $audio_data;

    AuditLogNotify(PROJECT_TYPE_NEW_OFFICE, $json_data);
}

// 设置蓝牙     key=ble
function handleSetBle(&$request, &$old_data, &$new_data)
{
    // app端优先判断蓝牙开关，默认进去都会设置，因此这里特殊处理，数据不变更不通知审计日志
    if ($new_data['BLECode'] == $old_data['BLECode'] && $new_data['BLEOpenDoorType'] == $old_data['BLEOpenDoorType']) {
        return;
    }
    $userConf = \util\container\getUserData();

    // 获取mode
    $mode = getModeByRole($userConf['Role']);

    // 设置用户信息
    $roleSet = array();
    $roleSet['dis'] = \common\model\getDisUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['ins'] =  \common\model\getInsUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['project'] = $userConf['ParentUUID'];
    $roleSet['company'] = $userConf['OfficeCompanyUUID'];
    $roleSet['mainUser'] = $userConf['UUID'];

    $bleTypeMap = [
        "0" => "shake",
        "1" => "handsFree",
        "2" => "tapAsCard",
    ];

    // 设置操作数据
    $audio_item = array();
    $audio_item['dataType'] = "complex";
    $audio_item['key'] = "ble";
    $audio_item['value'] = "";
    $audio_item['valueBefore'] = "";
    $audio_item['model'] = $mode;
    $audio_item['valueComplex'] = array(
        // blecode: 蓝牙开门CODE
        array(
            "dataType" => "enum",
            "key" => "ble",
            "value" => empty($new_data['BLECode']) ? "disable":"enable",
            "model" => $mode,
            "isEncrypt" => false,
            "isHidden" => false,
        ),
        // opendoor_type: 蓝牙开门的方式 0=摇一摇, 1=无感
        array(
            "dataType" => "enum",
            "key" => "type",
            "value" => isset($bleTypeMap[$new_data['BLEOpenDoorType']]) ? $bleTypeMap[$new_data['BLEOpenDoorType']] : $new_data['BLEOpenDoorType'],
            "model" => $mode,
            "isEncrypt" => false,
            "isHidden" => false,
        ),
    );
    $audio_item['valueComplexBefore'] = array(
        // blecode: 蓝牙开门CODE
        array(
            "dataType" => "enum",
            "key" => "ble",
            "value" => empty($old_data['BLECode']) ? "disable":"enable",
            "model" => $mode,
            "isEncrypt" => false,
            "isHidden" => false,
        ),
        // opendoor_type: 蓝牙开门的方式 0=摇一摇, 1=无感
        array(
            "dataType" => "enum",
            "key" => "type",
            "value" => isset($bleTypeMap[$old_data['BLEOpenDoorType']]) ? $bleTypeMap[$old_data['BLEOpenDoorType']] : $old_data['BLEOpenDoorType'],
            "model" => $mode,
            "isEncrypt" => false,
            "isHidden" => false,
        ),
    );
    $audio_item['isEncrypt'] = false;
    $audio_item['isHidden'] = false;

    // 设置操作项
    $audio_list_item = array();
    $audio_list_item['operaType'] = "update";
    $audio_list_item['model'] = $mode;
    $audio_list_item['action'] = AUDIT_ACTION_TYPE_UPDATE;
    $audio_list_item['object'] = array(
        'uuid' => $userConf['UUID'],
        'name' => $userConf['Name']
    );
    $audio_list_item['item'] = array($audio_item);

    // 设置审计日志数据
    $audio_data = array();
    $audio_data['user'] = $userConf['UUID'];
    $audio_data['userType'] = getUserType($userConf['Role']);
    $audio_data['action'] = AUDIT_ACTION_TYPE_UPDATE;
    $audio_data['model'] = $mode;
    $audio_data['ip'] = $request->getRealIp();
    $audio_data['roleSet'] = $roleSet;
    $audio_data['list'] = array($audio_list_item);

    // 发送审计日志
    $json_data = array();
    $json_data['trace_id'] = \util\container\getTraceID();
    $json_data['timestamp'] = time() * 1000000;
    $json_data['data'] = $audio_data;

    AuditLogNotify(PROJECT_TYPE_NEW_OFFICE, $json_data);
}

// 设置nfc      key=nfc
function handleSetNfc(&$request, &$old_data, &$new_data)
{
    $userConf = \util\container\getUserData();

    // 获取mode
    $mode = getModeByRole($userConf['Role']);

    // 设置用户信息
    $roleSet = array();
    $roleSet['dis'] = \common\model\getDisUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['ins'] =  \common\model\getInsUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['project'] = $userConf['ParentUUID'];
    $roleSet['company'] = $userConf['OfficeCompanyUUID'];
    $roleSet['mainUser'] = $userConf['UUID'];

    // 设置操作数据
    $audio_item = array();
    $audio_item['dataType'] = "enum";
    $audio_item['key'] = "nfc";
    $audio_item['value'] = empty($new_data) ? "disable":"enable";
    $audio_item['valueBefore'] = empty($old_data) ? "disable":"enable";
    $audio_item['model'] = $mode;
    $audio_item['valueComplex'] = [];
    $audio_item['valueComplexBefore'] = [];
    $audio_item['isEncrypt'] = false;
    $audio_item['isHidden'] = false;

    // 设置操作项
    $audio_list_item = array();
    $audio_list_item['operaType'] = "update";
    $audio_list_item['model'] = $mode;
    $audio_list_item['action'] = AUDIT_ACTION_TYPE_UPDATE;
    $audio_list_item['object'] = array(
        'uuid' => $userConf['UUID'],
        'name' => $userConf['Name']
    );
    $audio_list_item['item'] = array($audio_item);

    // 设置审计日志数据
    $audio_data = array();
    $audio_data['user'] = $userConf['UUID'];
    $audio_data['userType'] = getUserType($userConf['Role']);
    $audio_data['action'] = AUDIT_ACTION_TYPE_UPDATE;
    $audio_data['model'] = $mode;
    $audio_data['ip'] = $request->getRealIp();
    $audio_data['roleSet'] = $roleSet;
    $audio_data['list'] = array($audio_list_item);

    // 发送审计日志
    $json_data = array();
    $json_data['trace_id'] = \util\container\getTraceID();
    $json_data['timestamp'] = time() * 1000000;
    $json_data['data'] = $audio_data;

    AuditLogNotify(PROJECT_TYPE_NEW_OFFICE, $json_data);
}

// 设置dnd      key=dnd
function handleSetDnd(&$request, &$old_data, &$new_data)
{
    $userConf = \util\container\getUserData();

    // 获取mode
    $mode = getModeByRole($userConf['Role']);

    // 构造时间段字符串
    if (!$old_data) {
        $old_data = array(
            "Status" => "0",
            "StartTime" => "00:00",
            "EndTime" => "00:00"
        );
    }
    if (!$new_data) {
        $new_data = array(
            "Status" => "0",
            "StartTime" => "00:00",
            "EndTime" => "00:00"
        );
    }

    $old_timestr = MinuteToTimeString($old_data['StartTime']) . '~' . MinuteToTimeString($old_data['EndTime']);
    $new_timestr = MinuteToTimeString($new_data['StartTime']) . '~' . MinuteToTimeString($new_data['EndTime']);

    // 设置操作数据
    $statusMap = [
        "0" => "disable",
        "1" => "enable"
    ];
    $audio_item = array();
    $audio_item['dataType'] = "complex";
    $audio_item['key'] = "DND";
    $audio_item['value'] = "";
    $audio_item['valueBefore'] = "";
    $audio_item['model'] = $mode;
    $audio_item['valueComplex'] = array(
        // 开关
        array(
            "dataType" => "enum",
            "key" => "switch",
            "value" => isset($statusMap[$new_data['Status']]) ? $statusMap[$new_data['Status']] : $new_data['Status'],
            "model" => $mode,
            "isEncrypt" => false,
            "isHidden" => false,
        ),
        // 时间段
        array(
            "dataType" => "input",
            "key" => "time",
            "value" => $new_timestr,
            "model" => $mode,
            "isEncrypt" => false,
            "isHidden" => false,
        ),
    );
    $audio_item['valueComplexBefore'] = array(
        // 开关
        array(
            "dataType" => "enum",
            "key" => "switch",
            "value" => isset($statusMap[$old_data['Status']]) ? $statusMap[$old_data['Status']] : $old_data['Status'],
            "model" => $mode,
            "isEncrypt" => false,
            "isHidden" => false,
        ),
        // 时间段
        array(
            "dataType" => "input",
            "key" => "time",
            "value" => $old_timestr,
            "model" => $mode,
            "isEncrypt" => false,
            "isHidden" => false,
        ),
    );
    $audio_item['isEncrypt'] = false;
    $audio_item['isHidden'] = false;

    // 设置操作项
    $audio_list_item = array();
    $audio_list_item['operaType'] = "update";
    $audio_list_item['model'] = $mode;
    $audio_list_item['action'] = AUDIT_ACTION_TYPE_UPDATE;
    $audio_list_item['object'] = array(
        'uuid' => $userConf['UUID'],
        'name' => $userConf['Name']
    );
    $audio_list_item['item'] = array($audio_item);

    // 设置用户UUID
    $roleSet = array();
    $roleSet['dis'] = \common\model\getDisUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['ins'] =  \common\model\getInsUUIDByProjectUUID($userConf['ParentUUID']);
    $roleSet['project'] = $userConf['ParentUUID'];
    $roleSet['company'] = $userConf['OfficeCompanyUUID'];
    $roleSet['mainUser'] = $userConf['UUID'];

    // 设置审计日志数据
    $audio_data = array();
    $audio_data['user'] = $userConf['UUID'];
    $audio_data['userType'] = getUserType($userConf['Role']);
    $audio_data['action'] = AUDIT_ACTION_TYPE_UPDATE;
    $audio_data['model'] = $mode;
    $audio_data['ip'] = $request->getRealIp();
    $audio_data['roleSet'] = $roleSet;
    $audio_data['list'] = array($audio_list_item);

    // 发送审计日志
    $json_data = array();
    $json_data['trace_id'] = \util\container\getTraceID();
    $json_data['timestamp'] = time() * 1000000;
    $json_data['data'] = $audio_data;

    AuditLogNotify(PROJECT_TYPE_NEW_OFFICE, $json_data);
}
