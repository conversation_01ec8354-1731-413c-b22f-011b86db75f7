<?php

namespace common\model;

use PDO;


function canGetProviderInfo($insInfo, $disInfo)
{
    $db = \util\container\getDb();
    $query =  "SELECT ID, UUID, AccountUUID, CompanyName, CompanyAddress, Phone, Email, 
                              TaxId, WebSite, CreateTime, UpdateTime FROM ProviderInfo WHERE AccountUUID = :accountUuid";

    $sth = $db->prepare($query);

    // 检查 insInfo['UUID']
    $sth->bindParam(':accountUuid', $insInfo['UUID'], PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        return true;
    }
    // 如果 insInfo['UUID'] 没有找到，检查 disInfo['UUID']
    $sth = $db->prepare($query);
    $sth->bindParam(':accountUuid', $disInfo['UUID'], PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        return true;
    }
    return false ;
}
