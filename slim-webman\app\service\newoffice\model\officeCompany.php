<?php

namespace newoffice\model;

function getAppInfoByAdmin($personalAccountUUID)
{
    return \util\container\medooDb()->get("OfficeAdmin",
    [
        "[>]OfficeCompany" => ["OfficeAdmin.OfficeCompanyUUID" => "UUID"],
    ],
    ["OfficeCompany.Name(OfficeCompanyName)", "OfficeCompany.UUID(OfficeCompanyUUID)"],
    ["PersonalAccountUUID" => $personalAccountUUID]);

}

function getAppInfoByPersonnel($personalAccountUUID)
{
    return \util\container\medooDb()->get("OfficePersonnel",
    [
        "[>]OfficeCompany" => ["OfficePersonnel.OfficeCompanyUUID" => "UUID"],
    ],
    ["OfficeCompany.Name(OfficeCompanyName)", "OfficeCompany.UUID(OfficeCompanyUUID)","OfficePersonnel.IsFreeAppIntercome"],
    ["PersonalAccountUUID" => $personalAccountUUID]);

}