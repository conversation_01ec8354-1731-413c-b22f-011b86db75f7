<?php

namespace util\s3;

require_once __DIR__ . '/aliyun.phar';
require_once __DIR__ . '/aws.phar';
require_once __DIR__ . '/ukdHeader.php';

use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use OSS\OssClient;
use OSS\Core\OssException;
use UFileClient;

class S3Handle
{
    private $cfg;
    public function __construct()
    {
        $this->cfg = $this->InitS3Cfg();
    }
    private function InitS3Cfg()
    {
        $cfg = parse_ini_file("/etc/oss_install.conf");
        if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_OSS) {
            $oss = new OssClient($cfg['User'], $cfg['Password'], $cfg['Endpoint']);
            $cfg["oss"] = $oss;
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_AWS) {
            $credentials = new \Aws\Credentials\Credentials($cfg['User'], $cfg['Password']);
            $s3 = new \Aws\S3\S3Client([
                'version'     => 'latest',
                'region'      => $cfg['RegionID'],
                'credentials' => $credentials
            ]);
            $cfg["s3"] = $s3;
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_UKD) {
            $ukd = new UFileClient($cfg['User'], $cfg['Password'], $cfg['Endpoint']);
            $cfg["ukd"] = $ukd;
        }
        return $cfg;
    }
    public function DownloadFile($filepath, $localfile)
    {
        $cfg = $this->cfg;
        if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_OSS) {
            $options = array(
                OssClient::OSS_FILE_DOWNLOAD => $localfile
            );
            $cfg['oss']->getObject($cfg['BucketForPic'], $filepath, $options);
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_AWS) {
            $file = $cfg['s3']->getObject([
                'Bucket' => $cfg['BucketForPic'],
                'Key' => $filepath
            ]);
            $body = $file->get('Body');
            file_put_contents($localfile, $body->getContents());
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_UKD) {
            $cfg['ukd']->DownloadFile($cfg['BucketForPic'], $filepath, $localfile);
        }
    }
    public function UploadFile($remote_path, $localfile, $innerFlag = 0)
    {
        $cfg = $this->cfg;
        if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_OSS){
            $cfg['oss']->uploadFile($cfg['BucketForLog'], $remote_path, $localfile);
            $signedUrl = $cfg['oss']->signUrl($cfg['BucketForLog'], $remote_path, 604800, "GET", "");
            //oss需做内外网转换的特殊处理
            $url = str_replace("http:", "https:", $signedUrl);
            $url = str_replace("%2F", "/", $url);
            #替换为外网地址
            $url = str_replace("oss-eu-central-1-internal.aliyuncs.com", "oss-eu-central-1.aliyuncs.com", $url);
            $url = str_replace("oss-ap-southeast-1-internal.aliyuncs.com", "oss-ap-southeast-1.aliyuncs.com", $url);
            $url = str_replace("oss-us-west-1-internal.aliyuncs.com", "oss-us-west-1.aliyuncs.com", $url);
            $url = str_replace("oss-cn-shenzhen-internal.aliyuncs.com", "oss-cn-shenzhen.aliyuncs.com", $url);
            return $url;
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_AWS) {
            $cfg['s3']->putObject([
                'Bucket' => $cfg['BucketForLog'],
                'Key' => $remote_path,
                'SourceFile' => $localfile,
            ]);
            $cmd = $cfg['s3']->getCommand('GetObject', [
                'Bucket' => $cfg['BucketForLog'],
                'Key' => $remote_path
            ]);
            $request = $cfg['s3']->createPresignedRequest($cmd, '+604800 second');
            $presignedUrl = (string)$request->getUri();
            return $presignedUrl;
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_UKD) {
            $res = $cfg['ukd']->UploadFile($cfg['BucketForLog'], $remote_path, $localfile);
            if ($res != null) {
                return ""; //上传失败，返回空url
            }
            if($innerFlag)
            {
                $expire_time_sec = 0;
            }
            else
            {
                $expire_time_sec = 604800; // 下载url过期时间
            }
            return $cfg['ukd']->GetSignUrl($cfg['BucketForLog'], $remote_path, $expire_time_sec);
        }
    }
}
