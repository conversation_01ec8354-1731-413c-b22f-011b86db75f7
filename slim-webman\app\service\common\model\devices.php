<?php

namespace common\model;

use PDO;

require_once __DIR__ . "/version.php";
require_once __DIR__ . "/accessGroupDevices.php";
require_once __DIR__ . "/pubDevMngList.php";
require_once __DIR__ . "/accessGroup.php";
require_once __DIR__ . "/personalDevices.php";

const DB_TABLE_DEVICES = "Devices";
const DB_FIELD_DEVICES= ["MngAccountID", "Node", "MAC", "UUID", "UnitID", "Relay", "Flags", "Status", "CreateTime"];

function checkIndoorPayPlanDevOnline($userData)
{
    $macs = array();//家庭设备列表

    \common\model\getNodeIndoorDevicesList($userData, $macs);
    $checkDev = \common\model\checkIndoorPayPlan($macs, $userData['Role'], $userData['Account']);
    
    return $checkDev;
}

function getNodeIndoorDevicesList($userData, &$macs)
{
    $db =  \util\container\getDb();
    
    if ($userData['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userData['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        $sth = $db->prepare("select Status,MAC,SipAccount,Location,Firmware,Type,RtspPwd,SipPwd,Relay,SecurityRelay,DclientVer,ArmingFunction,Flags,Brand from PersonalDevices where Node = :account");
        $sth->bindParam(':account', $userData['Account'], PDO::PARAM_STR);
    }
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE || $userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER) {
        $sth = $db->prepare("select Status,MAC,SipAccount,Location,Firmware,Type,RtspPwd,SipPwd,Relay,SecurityRelay,DclientVer,ArmingFunction,Flags,Brand from Devices where Node = :Node");
        $sth->bindParam(':Node', $userData['Account'], PDO::PARAM_STR);
    }
    $ret = $sth->execute();
    $devList = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($devList as $row => $dev) {
        array_push($macs, $dev['MAC']);
    }
}

function checkIndoorPayPlan($macs, $role, $node, $is_smarthome_conf = 0)
{
    $db = \util\container\getDb();
    
    $checkDev = 1;

    $sth = $db->prepare("select MAC from DevicesSpecial where Account=:account limit 1");
    $sth->bindValue(':account', $node, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if (!$result) {
        return $checkDev; 
    }

    $check_mac = $result['MAC'];
    if (!in_array($check_mac, $macs)) {
        return $checkDev;
    }

    //室内机上线校验
    if ($role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_PERSONNAL_SLAVE) {
        $sth1 = $db->prepare("select Flags, Function from PersonalDevices where MAC = :mac");
    } else {
        $sth1 = $db->prepare("select Flags, Function from Devices where MAC = :mac");
    }
    $sth1->bindValue(':mac', $check_mac, PDO::PARAM_STR);
    $sth1->execute();
    $dev_info = $sth1->fetch(PDO::FETCH_ASSOC);

    $is_indoor_online = \util\utility\switchHandle($dev_info['Flags'], DevSwitchIndoorOnLine);
    if (!$is_indoor_online) {
        $checkDev = 0;
    }

    if ($is_smarthome_conf) {
        if (\util\utility\switchHandle($dev_info['Function'], DevFunctionSmartHomeType)) {
            $checkDev = 1;
        }
    }

    return $checkDev;
}

//旧方法 
/*
function getDevicesList($resultToken, $isNewComm, &$aptMacs, &$accessDevs, &$devList)
{
    $db =  \util\container\getDb();
    $redis = \util\container\getRedis();
    $redis->select(4);

    //获取房间下的设备
    $isCommunityAccount = 0;
    if ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE || $resultToken['Role'] == ROLE_TYPE_PERSONNAL_MASTER) {
        $sth = $db->prepare("select Status,MAC,SipAccount,Location,Firmware,Type,RtspPwd,SipPwd,Relay,SecurityRelay,DclientVer,ArmingFunction,Flags,Brand,IsRepost,DoorRelayStatus,DoorSeRelayStatus,UUID,AllowEndUserMonitor from PersonalDevices where Node = :account");
        $sth->bindParam(':account', $resultToken['Account'], PDO::PARAM_STR);
    }
    if ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_MASTER) {
        $isCommunityAccount = 1;
        $sth = $db->prepare("select Status,MAC,SipAccount,Location,Firmware,Type, RtspPwd,SipPwd, Relay,SecurityRelay, DclientVer,ArmingFunction,Flags,Brand,IsRepost,DoorRelayStatus,DoorSeRelayStatus,UUID,AllowEndUserMonitor from Devices  where Node = :Node");
        $sth->bindParam(':Node', $resultToken['Account'], PDO::PARAM_STR);
    }
    $sth->execute();
    $aptDevList = $sth->fetchALL(PDO::FETCH_ASSOC);


    //获取权限组设备列表
    $agMacList = array();//key is mac,value is relay
    $haveDefaultAg = 0;
    if ($isNewComm && $isCommunityAccount) {
        \common\model\getUserAGDeviceList($resultToken['UserAccount'], $agMacList, $haveDefaultAg);
        if($haveDefaultAg) {
            $removeDefaultAgMacs = \resident\model\getRemoveDefaultAgMacs($resultToken['MngUUID']);
        }
    }

    $noMonitorList = \common\model\getNoMonitorList();
    foreach ($aptDevList as $row => $dev) {
        if ($isCommunityAccount && \util\common\deviceIsAGDevType($dev['Type']) && $isNewComm && !array_key_exists($dev['MAC'], $agMacList)) {
            \util\log\akcsLog::debug("user devices not in account access. account:" . $resultToken['UserAccount']. " dev:".$dev['MAC']);
            continue;
        }

        $agRelay = 127;//设置的比较大，防止后面relay个数大于4时候没有处理到
        $agSecurityRelay = 127;
        if ($isCommunityAccount && $isNewComm && \util\common\deviceIsAGDevType($dev['Type'])) {
            $agRelay = $agMacList[$dev['MAC']];
            $agSecurityRelay = $agMacList[\util\common\getUserAGSecurityKey($dev['MAC'])];
        }

        if (!$dev['MAC']) {
            continue;
        }
        $curNode = array();
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        #added by chenyc,2018-05-24,要考虑效率问题..
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce ? $nonce : "";
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = $dev['Type'];
        $curNode['is_public'] = '0';
        $curNode['dclient_ver'] = $dev['DclientVer'];
        $curNode['dtmf'] = '#';
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], $agRelay, RELAY_TYPE_LOCAL);
        
        //只有新社区和单租户的室内机下发
        if (($isNewComm || ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE || $resultToken['Role'] == ROLE_TYPE_PERSONNAL_MASTER)) && \util\version\isSupportExRelay() && $dev['Type'] == DEVICE_TYPE_INDOOR)
        {
            if( \common\model\CheckExRelaySwitch($dev['UUID']))
            {
                $exRelayInfoList = \common\model\GetExRelayInfoList($dev['UUID']);
                \util\common\AddExRelayInfoList($curNode['relay'], $exRelayInfoList);
            }
        }
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], $agSecurityRelay, RELAY_TYPE_SE_LOCAL);
        }
        $curNode['firmware'] = $dev['Firmware'];

        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } elseif ($dev['Brand'] != 0) {//第三方中控
            $curNode['arming_function'] = 0;
        } else {
            $curNode['arming_function'] = 1;
        }
        $curNode['repost'] = intval($dev['IsRepost']);
        //转流的门口机状态置为在线
        if ($curNode['repost'] && $resultToken['Role'] != ROLE_TYPE_COMMUNITY_PM && ($dev['type'] == DEVICE_TYPE_STAIR || $dev['type'] == DEVICE_TYPE_DOOR))
        {
            $curNode['status'] = "1";
        }
        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }
        array_push($devList, $curNode);
        array_push($aptMacs, $dev['MAC']);

        $accessDevs[$dev['MAC']]['Relay'] = $agRelay;
        $accessDevs[$dev['MAC']]['SecurityRelay'] = $agSecurityRelay;
    }

    //处理3.3公共设备

    if ($isCommunityAccount == 1) {//社区公共设备
        $sth = $db->prepare("select D.Status,D.MAC,D.SipAccount,D.Location,D.Type, D.RtspPwd,D.Relay,D.SecurityRelay,D.DclientVer,D.Firmware,D.Flags,D.ID,D.Grade,D.ArmingFunction,D.IsRepost,D.DoorRelayStatus,D.DoorSeRelayStatus,D.UUID,D.AllowEndUserMonitor,D.UnitID from Devices D where MngAccountID=:MngAccountID and Grade in (1,2);");
        $sth->bindParam(':MngAccountID', $resultToken['MngID'], PDO::PARAM_STR);
    } else {
        //根据Account查找主账号下面的设备列表
        $sth = $db->prepare("select D.Status,D.MAC,D.SipAccount,D.Location,D.Type, D.RtspPwd,D.Relay,D.SecurityRelay,D.DclientVer,D.Firmware,D.Flags,D.ID,D.IsRepost,D.ArmingFunction,D.DoorRelayStatus,D.DoorSeRelayStatus,D.UUID,D.AllowEndUserMonitor  from PerNodeDevices left join PersonalDevices D on D.id=PerDevID   where NodeID=:NodeID");
        $sth->bindParam(':NodeID', $resultToken['AccountID'], PDO::PARAM_STR);
    }

    $sth->execute();
    $pubDevList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($pubDevList as $row => $dev) {
        $curNode = array();
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        #added by chenyc,2018-05-24,要考虑效率问题..
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce ? $nonce : "";
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = $dev['Type'];
        $curNode['is_public'] = '1';
        $curNode['dclient_ver'] = $dev['DclientVer'];

        $agRelay = 127;//设置的比较大，防止后面relaycommon个数大于4时候没有处理到
        $agSecurityRelay = 127;
        if ($isCommunityAccount) {
            if (\util\common\deviceIsManageBuilding($dev['Type'], $dev['Flags'], $dev['Grade'])) {
                if (!\common\model\isManageBuilding($dev['ID'], $resultToken['UnitID'])) {
                    \util\log\akcsLog::debug("public devices not manage building, Account:" . $resultToken['UserAccount']. " dev:".$dev['MAC'] . "  unit id:" . $resultToken['UnitID']);
                    continue;
                }
            }

            if(!$isNewComm)
            {
                if($dev['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && $resultToken['UnitID'] != $dev['UnitID'])
                {
                    \util\log\akcsLog::debug($dev['MAC'] . " unit devices not belong ". $resultToken['UserAccount']);
                    continue;
                }
            }
            //新社区，如果不属于本楼栋的管理机（没在权限组管控的机型），需要过滤
            else
            {
                if($dev['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && $resultToken['UnitID'] != $dev['UnitID']
                && !\util\common\deviceIsAGDevType($dev['Type']))
                {
                    \util\log\akcsLog::debug($dev['MAC'] . " unit devices not belong ". $resultToken['UserAccount']);
                    continue;
                }
            }

            $unitDefaultAg = \util\common\devHavaDefaultAccessGroup($haveDefaultAg, $dev, $agMacList, $removeDefaultAgMacs);

            if ($isNewComm && \util\common\deviceIsAGDevType($dev['Type']) && 
            //没有对应楼栋默认权限组
            !$unitDefaultAg && 
            //没在自定义权限组
            !array_key_exists($dev['MAC'], $agMacList)) 
            {
                \util\log\akcsLog::debug("public devices not in account access. account:" . $resultToken['UserAccount']. " dev:".$dev['MAC']);
                continue;
            }

            if ($isNewComm && \util\common\deviceIsAGDevType($dev['Type']) && 
            //没有对应楼栋默认权限组
            !$unitDefaultAg &&
            //在自定义权限组
            array_key_exists($dev['MAC'], $agMacList))
            {
                $agRelay = $agMacList[$dev['MAC']];
                $agSecurityRelay = $agMacList[\util\common\getUserAGSecurityKey($dev['MAC'])];
            }
        }

        $curNode['dtmf'] = '#';
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], $agRelay, RELAY_TYPE_LOCAL);
        //只有新社区和单租户的室内机下发
        if (($isNewComm || ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE || $resultToken['Role'] == ROLE_TYPE_PERSONNAL_MASTER)) && \util\version\isSupportExRelay() && $dev['Type'] == DEVICE_TYPE_INDOOR)
        {
            //检查设备是否开启外接relay总开关
            if( \common\model\CheckExRelaySwitch($dev['UUID']))
            {
                $exRelayInfoList = \common\model\GetExRelayInfoList($dev['UUID']);
                \util\common\AddExRelayInfoList($curNode['relay'], $exRelayInfoList);
            }
        }
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], $agSecurityRelay, RELAY_TYPE_SE_LOCAL);
        }
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } else {
            $curNode['arming_function'] = 1;
        }
        $curNode['repost'] = intval($dev['IsRepost']);
        //转流的门口机状态置为在线
        if ($curNode['repost'] && $resultToken['Role'] != ROLE_TYPE_COMMUNITY_PM && ($dev['type'] == DEVICE_TYPE_STAIR || $dev['type'] == DEVICE_TYPE_DOOR))
        {
            $curNode['status'] = "1";
        }
        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }
        array_push($devList, $curNode);

        $accessDevs[$dev['MAC']]['Relay'] = $agRelay;
        $accessDevs[$dev['MAC']]['SecurityRelay'] = $agSecurityRelay;
    }
}
*/

function getPubDevicesList($resultToken, &$devList)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select D.Status,D.MAC,D.SipAccount,D.Location,D.Type, D.RtspPwd,D.Relay,D.SecurityRelay,D.DclientVer,D.Firmware,D.Flags,D.ID,D.Grade,D.ArmingFunction,D.UnitID,D.Grade,D.DoorRelayStatus,D.DoorSeRelayStatus,D.Function  from Devices D where Grade in(1,2) and MngAccountID=:MngAccountID;");
    $sth->bindParam(':MngAccountID', $resultToken['MngID'], PDO::PARAM_STR);
    $ret = $sth->execute();
    $devList = $sth->fetchALL(PDO::FETCH_ASSOC);
}

function IsPersonalCanOpenDoor($UserConf, $mac)
{
    $db =  \util\container\getDb();
    $isCanOpendoor = 0;
    //个人
    $sth = $db->prepare("select count(1) as count From PersonalDevices where Node=:Account and Mac=:Mac;");
    $sth->bindParam(':Account', $UserConf['Account'], PDO::PARAM_STR);
    $sth->bindParam(':Mac', $mac, PDO::PARAM_STR);
    $sth->execute();

    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result['count'] <= 0) {
        //公共
        $sth = $db->prepare("select count(1) as count From PersonalDevices D left join PerNodeDevices P on D.ID=P.PerDevID where D.Mac=:Mac and P.NodeID=:AccountID;");
        $sth->bindParam(':AccountID', $UserConf['AccountID'], PDO::PARAM_STR);
        $sth->bindParam(':Mac', $mac, PDO::PARAM_STR);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        if ($result['count'] > 0) {
            $isCanOpendoor = 1;
        }
    } else {
        $isCanOpendoor = 1;
    }

    return $isCanOpendoor;
}

function isCommCanOpenDoor($userConf, $mac, $relay, $securityRelay)
{   
   // 新社区检查权限组
    if (\resident\model\checkIsNewCommunity($userConf)) 
    {
        return \common\model\isNewCommCanOpenDoor($userConf, $mac, $relay, $securityRelay);
    } 
    else 
    {
        // 旧社区权限验证
        return \common\model\isCommCanOpenDoorWithoutCheckAg($userConf, $mac);
    }
}

function isNewCommCanOpenDoor($userConf, $mac, $relay, $securityRelay)
{
    if (!is_null($relay)) 
    {
        $relay2int = \util\common\relayId2RelayInt($relay);
        return \common\model\checkAccessGroup($userConf['UserAccount'], $mac, $relay2int, ACCESS_GROUP_CHECK_RELAY);
    } 
    elseif (!is_null($securityRelay)) 
    {
        $securityRelay2int = \util\common\relayId2RelayInt($securityRelay);
        return \common\model\checkAccessGroup($userConf['UserAccount'], $mac, $securityRelay2int, ACCESS_GROUP_CHECK_SECURITY_RELAY);
    }
}

function isPmCanOpenDoor($UserConf, $mac)
{
    $db =  \util\container\getDb();
    $isCanOpendoor = 0;
    $sth = $db->prepare("select count(1) as count From Devices where Mac=:Mac and (MngAccountID=:MngID and Grade in(1,2));");
    $sth->bindParam(':MngID', $UserConf['MngID'], PDO::PARAM_STR);
    $sth->bindParam(':Mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result['count'] > 0) {
        $isCanOpendoor = 1;
    }
    return $isCanOpendoor;
}
//社区不校验权限组的 门口机开门权限校验
function isCommCanOpenDoorWithoutCheckAg($UserConf, $mac)
{
    $db =  \util\container\getDb();
    $isCanOpendoor = 0;
    //个人
    $sth = $db->prepare("select count(1) as count From Devices where Node=:Account and Mac=:Mac;");
    $sth->bindParam(':Account', $UserConf['Account'], PDO::PARAM_STR);
    $sth->bindParam(':Mac', $mac, PDO::PARAM_STR);
    $sth->execute();

    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result['count'] <= 0) {
        //公共
        $sth = $db->prepare("select count(1) as count From Devices where Mac=:Mac and (MngAccountID=:MngID and Grade=1);");
        $sth->bindParam(':MngID', $UserConf['MngID'], PDO::PARAM_STR);
        $sth->bindParam(':Mac', $mac, PDO::PARAM_STR);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        if ($result['count'] > 0) {
            $isCanOpendoor = 1;
        } else {
            //单元
            $sth = $db->prepare("select count(1) as count From Devices where Mac=:Mac and (MngAccountID=:MngID and Grade=2 and UnitID=:UnitID);");
            $sth->bindParam(':MngID', $UserConf['MngID'], PDO::PARAM_STR);
            $sth->bindParam(':UnitID', $UserConf['UnitID'], PDO::PARAM_STR);
            $sth->bindParam(':Mac', $mac, PDO::PARAM_STR);
            $sth->execute();
            $result = $sth->fetch(PDO::FETCH_ASSOC);
            if ($result['count'] > 0) {
                $isCanOpendoor = 1;
            }
        }
    } else {
        $isCanOpendoor = 1;
    }
    return $isCanOpendoor;
}

//获取设备对应relay开关状态
function getAllDoorRelayStatus($mac, &$doorRelayStatus, &$doorSeRelayStatus)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select DoorRelayStatus,DoorSeRelayStatus From Devices where Mac=:mac union all select DoorRelayStatus,DoorSeRelayStatus From PersonalDevices where Mac=:mac limit 1");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    $dev = $sth->fetch(PDO::FETCH_ASSOC);
    $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
    $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
}

function isDevActivateRepost($mac)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select IsRepost from Devices where MAC =:mac union all select IsRepost from PersonalDevices where MAC =:mac");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['IsRepost'];
}

function getDevicesFunctionByMac($mac)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select Function from Devices where MAC =:mac union all select Function from PersonalDevices where MAC =:mac");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['Function'];
}


function getDevicesInfoByMac($mac)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select MngAccountID, UnitID, Node, UUID From Devices where MAC=:mac");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}

function getDevicesInfoByUUID($uuid)
{
    return \util\container\medooDb()->get(DB_TABLE_DEVICES, DB_FIELD_DEVICES, ["UUID" => $uuid]);
}

function getDeviceUUIDByMac($mac)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select UUID from Devices where MAC =:mac union all select UUID from PersonalDevices where MAC =:mac");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['UUID'];
}

function getDeviceInfoByRoleAndUUID($role, $UUID)
{
    if(\util\common\isSingleHouseUser($role)) {
        $deviceInfo = \common\model\getPersonalDevicesInfoByUUID($UUID);
    }
    else {
        $deviceInfo = \common\model\getDevicesInfoByUUID($UUID);
    }
    return $deviceInfo;
}

function checkOpendoorPermission($userConf, $mac, $relay, $securityRelay)
{   
    if ($userConf['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userConf['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        // 单住户权限验证
        return \common\model\IsPersonalCanOpenDoor($userConf, $mac);
    } elseif ($userConf['Role'] == ROLE_TYPE_COMMUNITY_PM) { 
        // 社区PM权限验证
        return \common\model\isPmCanOpenDoor($userConf, $mac);
    } else {
        // 社区权限验证
        return \common\model\isCommCanOpenDoor($userConf, $mac, $relay, $securityRelay);
    }
}