<?php

namespace newoffice\control;

function dealNewOfficeAlarm($request, $response)
{
    $userConf = \util\container\getUserData();
    
    $bodyValue = $request->getParsedBody();
    $alarmID = $bodyValue["ID"];
    $alarmResult = $bodyValue["Result"];
    $alarmNode = $userConf['Account'];

    \common\model\updateAlarms($alarmID, $alarmResult);
    newOfficeAlarmDealNotify($alarmNode, $userConf['UserAccount'], $alarmID, $alarmResult);

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}
