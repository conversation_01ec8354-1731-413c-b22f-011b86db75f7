<?php

namespace common\model;

require_once __DIR__ . "/../../../config/define.php";

use PDO;

function getFacePicInfoByPersonalAccountID($personalAccountID)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("/*master*/ select ID,MngAccountID,UnitID,Node,FileName,FaceUrl,FaceMD5 from FaceMng where PersonalAccountID = :PersonalAccountID");
    $sth->bindParam(':PersonalAccountID', $personalAccountID, PDO::PARAM_INT);
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function updateFaceMng($personalAccountInfo, $faceMngID, $fileInfo)
{
    $db = \util\container\getDB();
    $faceUrl = $fileInfo[FACE_FILE_URL];
    $fileName = $fileInfo[FILE_NAME];
    $faceMD5 = $fileInfo[FACE_FILE_MD5];
    $CreatorType = FACE_CREATOR_TYPE_ENDUSER;

    $sth = $db->prepare("update FaceMng set FaceUrl = :FaceUrl, FileName = :FileName, FaceMD5 = :FaceMD5, CreatorType = :CreatorType, CreatorUUID = :CreatorUUID where ID = :ID");
    $sth->bindParam(':FaceUrl', $faceUrl, PDO::PARAM_STR);
    $sth->bindParam(':FileName', $fileName, PDO::PARAM_STR);
    $sth->bindParam(':FaceMD5', $faceMD5, PDO::PARAM_STR);
    $sth->bindParam(':CreatorUUID', $personalAccountInfo['UUID'], PDO::PARAM_STR);
    $sth->bindParam(':CreatorType', $CreatorType, PDO::PARAM_INT);
    $sth->bindParam(':ID', $faceMngID, PDO::PARAM_INT);
    $sth->execute();
}

function insertFaceMng($personalAccountInfo, $fileInfo, $mngAccountID)
{
    $db = \util\container\getDB();
    $creatorType = FACE_CREATOR_TYPE_ENDUSER;

    $sth = $db->prepare("insert into FaceMng (MngAccountID, UnitID, PersonalAccountID, FileName, FaceUrl, Node, FaceMD5, CreatorType, CreatorUUID) values(:MngAccountID, :UnitID, :PersonalAccountID, :FileName, :FaceUrl, :Node, :FaceMD5, :CreatorType, :CreatorUUID)");
    $sth->bindParam(':MngAccountID', $mngAccountID, PDO::PARAM_INT);
    $sth->bindParam(':UnitID', $personalAccountInfo['UnitID'], PDO::PARAM_INT);
    $sth->bindParam(':PersonalAccountID', $personalAccountInfo['PersonalAccountID'], PDO::PARAM_INT);
    $sth->bindParam(':FileName', $fileInfo[FILE_NAME], PDO::PARAM_STR);
    $sth->bindParam(':FaceUrl', $fileInfo[FACE_FILE_URL], PDO::PARAM_STR);
    $sth->bindParam(':Node', $personalAccountInfo['Node'], PDO::PARAM_STR);
    $sth->bindParam(':FaceMD5', $fileInfo[FACE_FILE_MD5], PDO::PARAM_STR);
    $sth->bindParam(':CreatorType', $creatorType, PDO::PARAM_INT);
    $sth->bindParam(':CreatorUUID', $personalAccountInfo['UUID'], PDO::PARAM_STR);
    $sth->execute();
}

function mergeFaceMng($personalAccountInfo, $fileInfo, $mngAccountID)
{
    $picInfo = \common\model\getFacePicInfoByPersonalAccountID($personalAccountInfo['PersonalAccountID']);

    if ($picInfo) {
        $fdfsRet = \util\fdfs\storagePictoFdfs(FACE_FILE_PREFIX . $fileInfo[FACE_FILE_URL], $picInfo['FaceUrl']);
        if ($fdfsRet) {
            $fileInfo[FACE_FILE_URL] = $fdfsRet;
        } else {
            \util\alarmManage\addAkcsAlarm(AKCS_MONITOR_ALARM_FDFS_UPLOAD_FACE_FAILED, $fileInfo[FACE_FILE_URL]);
            return false;
        }
        \common\model\updateFaceMng($personalAccountInfo, $picInfo['ID'], $fileInfo);
    } else {
        $fdfsRet = \util\fdfs\storagePictoFdfs(FACE_FILE_PREFIX . $fileInfo[FACE_FILE_URL], "");
        if ($fdfsRet) {
            $fileInfo[FACE_FILE_URL] = $fdfsRet;
        } else {
            \util\alarmManage\addAkcsAlarm(AKCS_MONITOR_ALARM_FDFS_UPLOAD_FACE_FAILED, $fileInfo[FACE_FILE_URL]);
            return false;
        }
        \common\model\insertFaceMng($personalAccountInfo, $fileInfo, $mngAccountID);
    }

    return true;
}

function mergeFaceMngWithoutUpload($personalAccountInfo, $fileInfo, $mngAccountID)
{
    $picInfo = \common\model\getFacePicInfoByPersonalAccountID($personalAccountInfo['PersonalAccountID']);

    if ($picInfo) {
        //删除fdfs中旧的图片
        \util\fdfs\fdfs_del_pic_by_url($picInfo['FaceUrl']);
        \common\model\updateFaceMng($personalAccountInfo, $picInfo['ID'], $fileInfo);
    } else {
        \common\model\insertFaceMng($personalAccountInfo, $fileInfo, $mngAccountID);
    }

    return true;
}

function queryFaceMng($personalAccountID)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("/*master*/ select ID, MngAccountID, UnitID, PersonalAccountID, FileName, FaceUrl from FaceMng where  PersonalAccountID = :PersonalAccountID");
    $sth->bindParam(':PersonalAccountID', $personalAccountID, PDO::PARAM_INT);
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function deleteFaceMng($personalAccountID)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("delete from FaceMng where  PersonalAccountID = :PersonalAccountID");
    $sth->bindParam(':PersonalAccountID', $personalAccountID, PDO::PARAM_INT);
    $sth->execute();
    $affected = $sth->rowCount();
    return $affected;
}
