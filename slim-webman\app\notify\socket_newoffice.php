<?php

require_once(dirname(__FILE__) . '/notify_newoffice.php');
require_once(dirname(__FILE__) . '/kafka.php');

class CJsonMessageNotify extends CKafka
{
    /**
     * @param mixed $data   json格式消息体, 详见：http://192.168.10.102:8071/x/LoB4B  之 “6 web和应用后台通信协议”
     * 示例：
     * { 
     *     "msg_type":  "xxxx",           //区分大小写
     *     "trace_id":   "xxxx",          //链路请求ID
     *     "timestamp": 1726688999000000, //微秒, long long
     *     "data": { }                    //data内部的消息都是string
     * }
     */
    public function copy($data, $topicName = '', $retry = true)
    {
        $this->byte->writeProtobuf($data);
        $this->setMsgHead();
        $this->sendMsg($topicName, $retry);
        return;
    }

    // 不带消息头的消息
    public function copy_without_head($data, $topicName = '', $retry = true){
        $this->byte->writeProtobuf($data);
        $this->sendMsg($topicName, $retry);
        return;
    }
}

