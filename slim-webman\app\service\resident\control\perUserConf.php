<?php

namespace  resident\control;

//和用户无关，固定写死的配置
function getPerAppConstConf()
{
    $constConf = [];
    $constConf['show_payment'] = 0;
    $constConf['show_subscription'] = 0;
    $constConf['sip_server'] = G_PBX_IPV4;
    $constConf['sip_server_ipv6'] = G_PBX_IPV6;
    $constConf['video_res']  = "2";
    $constConf['video_bitrate'] = "512";
    $constConf['have_public_dev'] = "0";
    $constConf['video_storage_time'] = "0";
    $constConf['data_collection'] = 0; //app数据收集到网管系统 后续如果用户较多可采样收集
    $constConf['check_slave'] = 1;
    $constConf['show_pm'] = "0"; //只有新社区的主从账号需要展示Property Manager开关
    $constConf['show_face'] = 1; //人脸识别开关
    $constConf['show_tempkey'] = 1;
    $constConf['show_thirdparty_lock'] = "1";
    $constConf['community_contact'] = 0;
    $constConf['is_old_community'] = 0;
    $constConf['show_id_access'] = 0;
    $constConf['enable_pin_config'] = 1;
    return $constConf;
}
//直接从数据容器user_data中就能取到的配置
function getPerAppUserDataConf()
{
    $userDataConf = [];
    $userData = \util\container\getUserData(); 
    $userDataConf['sip'] = $userData['SipAccount'];
    $userDataConf['display_name'] = $userData['Name'];
    $userDataConf['sip_passwd'] = \util\utility\passwdDecode($userData['SipPwd']);
    $userDataConf['uid'] = $userData['UserAccount'];
    $userDataConf['uuid'] = $userData['UUID'];
    $userDataConf['node'] = $userData['Account'];
    $userDataConf['codec'] = strval($userData['Codec']); //0=PCMU, 8 =PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义
    $userDataConf['role'] = intval($userData['Role']);
    $userDataConf['enable_smarthome'] = intval($userData['EnableSmartHome']);
    $userDataConf['motion_alert'] = \util\redisManage\getAppMotionStatus($userData['UserAccount']); //app-uid的motion接收开发
    return $userDataConf;
}

//用户开关相关配置
function getPerAppSwitchConf()
{
    $switchConf = [];
    $userData = \util\container\getUserData(); 
    $switch = intval($userData['UserSwitch']);//旧版本兼容的标识
    $masterSwitch = intval($userData['Switch']); //主账户的switch
    $pinInit = \util\utility\switchHandle($switch, PersonlAccountSwitchPinInit);
    $enableConfirmFlag = \util\utility\switchHandle($switch, PersonlAccountSwitchConfirm);
    
    $landLineStatus = 0;        // 落地开关
    if (\common\model\checkAdvanceFeaturesExpire($userData['Account'], $landLineStatus)) {
        $isShowLandline = 0;
        $enableThirdCamera = 0;
    } else {
        if (\util\utility\switchHandle($masterSwitch, PersonlAccountSwitchFeature)) {
            $enableThirdCamera = 1;
        }
        $isShowLandline = $landLineStatus;
    }

    $switchConf['enable_confirm_flag'] = intval($enableConfirmFlag);  //高级设置开关
    $switchConf['show_landline'] = intval($isShowLandline);
    $switchConf['enable_third_camera'] = intval($enableThirdCamera);
    if (\util\version\isNeedReturnPinInit()) {
        $switchConf['pin_init'] = intval($pinInit); //pin初始化标记
    }
    
    // 只有单住户主账号展示创建RF卡开关
    if($userData['Role'] == ROLE_TYPE_PERSONNAL_MASTER){
        $switchConf['enable_create_rf'] = 1;
    }

    return $switchConf;
}
//transtype相关
function getPerAppTransTypeConf()
{
    $transTypeConf = [];
    $userData = \util\container\getUserData(); 
    //trans type
    $transType = $userData['SipType'];
    $mngSipType = \common\model\getUserMngSipType($userData['Role'], $userData['ParentID']);
    if ($mngSipType["SipType"] != APP_SIP_TYPE_NONE) {
        $transType = $mngSipType["SipType"];
    }
    $transTypeConf['trans_type']  = \util\common\transSipType($userData['UUID'], $transType);
    $transTypeConf['rtp_confuse'] = intval($mngSipType["RtpConFuse"]);

    return $transTypeConf;
}

//落地相关
function getPerAppLandlineConf()
{
    $landlineConf = [];
    $userData = \util\container\getUserData(); 
    $landlineConf['landline'] = \util\common\getPbxLandlineNumber($userData['PhoneCode'], $userData['Phone'], $userData['Phone2'], $userData['Phone3']);
    return $landlineConf;
}
    
//多套房相关
function getPerAppMultiSiteConf($appLandLineConf)
{
    $multiSiteConf = []; 
    $userData = \util\container\getUserData(); 

    $userInfo = \common\model\getUserInfoByUUID($userData['UserInfoUUID']);
    $mainSipInfo = \common\model\getUserSipInfo($userInfo['AppMainUserAccount']);
    $multiSiteConf['main_sip'] = $userInfo['AppMainUserAccount'];   
    $multiSiteConf['main_sip_passwd'] = \util\utility\passwdDecode($mainSipInfo['SipPwd']);
    $multiSiteConf['is_site_link'] = \common\control\checkIsMultiSiteUser();

    if ($multiSiteConf['is_site_link']) {
        $allLandlines = [];
        $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($userData['UserInfoUUID']);
        foreach ($personalAccountList as $siteInfo) {
            $siteLandline = \util\common\getPbxLandlineNumber($siteInfo['PhoneCode'], $siteInfo['Phone'], $siteInfo['Phone2'], $siteInfo['Phone3']);
            //将各站点的落地号码合并到总的落地号码数组
            $allLandlines = array_merge($allLandlines, $siteLandline);
        }
        //去重
        //app根据all_sites_landline添加app联系人，根据show_landline和landline字段判断是否弹窗，如果all_sites_landline有变化也需要弹窗
        $multiSiteConf['all_sites_landline'] = array_values(array_unique($allLandlines));
    } else {
        $multiSiteConf['all_sites_landline'] = $appLandLineConf;
    }

    $siteinfo = [];
    \common\control\getCommSiteInfo($userData, $siteinfo);
    $multiSiteConf['room_name'] = $siteinfo['room_name'];
    $multiSiteConf['project_name'] = $siteinfo['project_name'];
    $multiSiteConf['room_title'] = $siteinfo['room_title'];

    return $multiSiteConf;
}

//过期处理相关
function getPerAppExpireConf()
{
    $expireConf = []; 
    $userData = \util\container\getUserData(); 
    \common\model\checkAccountExpire2($expireConf, $userData);
    return $expireConf;
}

function getPerAppConf($checkDev)
{
    //顺序不能调整，否则可能出现覆盖错误问题，比如过期的配置判断必须放最后
    $appConf = [];
    $appConf['check_dev'] = intval($checkDev); //检查室内机的收费方案flag
    $appConf = array_merge($appConf, getPerAppConstConf());
    $appConf = array_merge($appConf, getPerAppUserDataConf());
    $appConf = array_merge($appConf, getPerAppSwitchConf());
    $appConf = array_merge($appConf, getPerAppTransTypeConf());
    $appConf = array_merge($appConf, getPerAppLandlineConf());
    $appConf = array_merge($appConf, getPerAppMultiSiteConf($appConf['landline']));
    $appConf = array_merge($appConf, getPerAppExpireConf());
    
    return $appConf;
}

function getPerAppUnreadMsg()
{
    $unreadData = [];
    $userData = \util\container\getUserData(); 
    $unreadData['messages_num'] = intval(\common\model\getMessagesNumV65());
    $unreadData['activities_num'] = intval(\common\model\getActivitiesNum($userData));
    return $unreadData;
}

function getPerAppThirdDev()
{
    $userData = \util\container\getUserData(); 
    $thirdPartyDevList = [];

    //三方摄像头设备
    \common\model\getThirdPartyCameraList($userData, $thirdPartyDevList);
    //三方锁设备
    \common\model\getThirdPartyDevList($userData, $thirdPartyDevList);
    // dormakaba锁
    $dormakabaLockList = \common\control\getDormakabaLockList();
    // salto 锁
    $saltoLockList = \common\control\salto\getPersonnalEnduserSaltoLockList();
    
    $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $saltoLockList);
    $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $dormakabaLockList["lock_dev_list"]);

    if(\util\version\isShowNewBrandLock())
    {
        //ITec锁
        $itecLockList = \common\control\iTec\getPersonnalEnduserLockList();
        $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $itecLockList);
        //TT锁
        $ttLockList = \common\control\TTLock\getEnduserLockList();
        $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $ttLockList);
    }

    // NVR
    $nvrList = \common\control\thirdNvr\getPerAptThirdNvrList();
    $thirdPartyDevList["third_nvr_list"] = $nvrList;
    
    return $thirdPartyDevList;
}

function getPerUserConfV65($response)
{
    $userData = \util\container\getUserData();

    $aptDevs = [];//家庭设备列表
    $responseDevList = \resident\model\getPerDevicesList($aptDevs);

    //室内机方案,室内机是否上线标识
    $checkDev = \common\model\checkIndoorPayPlan($aptDevs, $userData['Role'], $userData['Account']);
    if (\util\version\isNoSupportCheckDev() && $checkDev == 0) {
        //旧版本直接拦截退出
        \util\log\akcsLog::debug("getPerUserConfV64 checkIndoorPayPlan false. uid = {$userData['UserAccount']}");
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"the token does not exist");
    }

    $datas = [
        "app_conf" => getPerAppConf($checkDev),
        "dev_list" => $responseDevList,
        "third_party_dev_list" => getPerAppThirdDev(),
        "unread_msg" => getPerAppUnreadMsg()
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}


