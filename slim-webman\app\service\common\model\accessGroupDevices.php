<?php

namespace common\model;

use PDO;

//获取用户关联的设备列表
//macList 包含的设备列表
//haveDefaultAg 是否有默认权限组，有的话就应该包含unit+pub设备
function getUserAGDeviceList($account, &$macList, &$haveDefaultAg)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select mac,relay,SecurityRelay from UserAccessGroupDevice  where UserAccessGroupID in (select ID from UserAccessGroup where Account=:Account)");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $ret = $sth->execute();
    $macs = $sth->fetchALL(PDO::FETCH_ASSOC);
    
    foreach ($macs as $key => $value) {
        #array_push($macList, $value["mac"]);
        \util\common\getAgMacInfo($macList, $value);
    }
    
    $sth = $db->prepare("select mac,relay,SecurityRelay from AccessGroupDevice where AccessGroupID in (select AccessGroupID From AccountAccess where Account=:Account)");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $ret = $sth->execute();
    $macs = $sth->fetchALL(PDO::FETCH_ASSOC);
    
    foreach ($macs as $key => $value) {
        #array_push($macList, $value["mac"]);
        \util\common\getAgMacInfo($macList, $value);
    }

    $sth = $db->prepare("select CommunityID, UnitID from AccessGroup G left join AccountAccess  A on A.AccessGroupID=G.ID where A.Account=:Account");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $ret = $sth->execute();
    $macs = $sth->fetchALL(PDO::FETCH_ASSOC);
    
    $unitArray = array();
    $haveDefaultAg = 0;
    foreach ($macs as $key => $value) {
        $unitID = $value['UnitID'];
        if ($unitID > 0) {
            array_push($unitArray, $unitID);
            $haveDefaultAg = 1;
            $macList[\util\common\getUnitAccessGroupKey($unitID)] = 1;
        }
    }
}