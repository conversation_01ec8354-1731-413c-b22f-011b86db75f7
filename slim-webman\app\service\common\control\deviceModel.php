<?php

namespace common\control;

require_once __DIR__ . "/../model/distributorInfo.php";
require_once __DIR__ . "/../model/version.php";
require_once __DIR__ . "/../model/versionModelCustomizeInfo.php";

function getSupportPackageDetectionModelNameList()
{
    // 先根据super配置获取支持的model列表
    $supportPackageDetectionList = \common\model\getSupportPackageDetectionList();

    return getDisplayModelList($supportPackageDetectionList);
}

function getDisplayModelList($supportModelList)
{
    $userData = \util\container\getUserData();
    $distributorInfo = \common\model\getDistributorInfoByMngUUID($userData['MngUUID']);
    if ($distributorInfo['IsCustomizeModel'] == 0) {
        // 不支持定制model，则直接展示super配置的列表
        return array_column($supportModelList, 'VersionName');
    }

    // 获取dis是否支持定制化model的开关
    $userData = \util\container\getUserData();

    // 从supportPackageDetectionList提取VersionModel的ID列表
    $versionModelIDList = array_column($supportModelList, 'ID');

    // 支持定制model，则需要展示定制的model name
    $customizeModelList = \common\model\getVersionModelCustomizeInfo($distributorInfo['DisUUID'], $versionModelIDList);

    return array_column($customizeModelList, 'CustomizeName');
}
