<?php

namespace resident\model;

use PDO;

require_once __DIR__ . "/indoorExtraDevice.php";
require_once __DIR__ . "/indoorExtraDeviceRelayList.php";
require_once __DIR__ . "/indoorExtraDeviceRelayAction.php";

// 从output字段提取对应的数字值：K1-K8(1-8), OT1-OT8(9-16)
function getOutputValue($output) {
    if (strlen($output) >= 2 && $output[0] === RELAY_OUTPUT_PREFIX_K && $output[1] >= '1' && $output[1] <= '8') 
    {
        return intval($output[1]); // K1-K8 → 1-8
    } 
    elseif (strlen($output) >= 3 && substr($output, 0, 2) === RELAY_OUTPUT_PREFIX_OT && $output[2] >= '1' && $output[2] <= '8') 
    {
        return intval($output[2]); // OT1-OT8 → 1-8
    }
    return 0; // 无效值
}

function checkExRelaySwitch($devUUID)
{
    $db = \util\container\getDb();

    $sth = $db->prepare("SELECT ExtraRelaySwitch FROM IndoorMonitorConfig WHERE DeviceUUID = :devUUID");
    $sth->bindParam(":devUUID", $devUUID, PDO::PARAM_STR);
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    
    if ($res && $res['ExtraRelaySwitch'] == 1) 
    {
        return true;
    }
    
    return false;
}

function getIndoorMonitorConfig($devUUID)
{
    $db = \util\container\getDb();

    $sth = $db->prepare("SELECT UUID, ExtraRelaySwitch, ExtraRelayType, ExtraRelayMode
                        FROM IndoorMonitorConfig WHERE DeviceUUID = :devUUID");
    $sth->bindParam(":devUUID", $devUUID, PDO::PARAM_STR);
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    
    return $res ?: null;
}

function getExRelayInfoList($devUUID)
{
    $db = \util\container\getDb();
    $result = [];
    
    $indoorConfig = getIndoorMonitorConfig($devUUID);
    if (!$indoorConfig) 
    {
        return $result;
    }

    $extraDevices = \resident\model\getExtraDevicesByConfigUUID($indoorConfig['UUID']);
    
    if (empty($extraDevices)) 
    {
        return $result;
    }
    
    foreach ($extraDevices as $device) 
    {
        if ($device['EnableSwitch'] != 1) 
        {
            continue;
        }
        
        // 获取RelayList
        $relayLists = \resident\model\getExtraDeviceRelayListsByDeviceUUID($device['UUID']);
        
        foreach ($relayLists as $relay) {
            // 获取RelayAction
            $actions = \resident\model\getExtraDeviceRelayActionsByRelayUUID($relay['UUID']);
            
            foreach ($actions as $action) {
                // 根据Output字段判断输出类型
                $outputType = 0; // 默认为ExternRelay
                if (strpos($action['Output'], RELAY_OUTPUT_PREFIX_OT) === 0) {
                    $outputType = 1; // DigitalOutput
                }
                
                $relayIndex = getOutputValue($action['Output']);
                
                $relayInfo = [];
                $relayInfo['name'] = $relay['Name'];
                $relayInfo['function'] = $relay['Function'];
                $relayInfo['relay_index'] = $relayIndex;
                $relayInfo['relay_switch'] = $relay['EnableSwitch'];
                $relayInfo['status'] = $action['Status'];
                $relayInfo['relay_output_type'] = $outputType;
                $relayInfo['device_index'] = $device['DeviceIndex'];
                $relayInfo['action_type'] = $action['ActionType'];
                $relayInfo['hold_delay'] = $action['HoldDelay'];
                $relayInfo['output'] = $action['Output'];
                
                $result[] = $relayInfo;
            }
        }
    }
    
    return $result;
}