{"database": {"account": {"root": {"user": "root", "password": "123456"}, "app_user": {"user": "app_user", "password": "app_password"}}, "serverInfo": {"main_server": {"write": {"host": ["127.0.0.1"], "port": 3306, "timeout": 5, "account": "root"}, "read": {"host": ["127.0.0.1"], "port": 3306, "timeout": 5, "account": "root", "startConns": "swoole_cpu_num()*5", "maxSpareConns": "swoole_cpu_num()*10", "maxSpareExp": 3600, "maxConns": "swoole_cpu_num()*20"}}, "slave_server": {"read": {"host": ["127.0.0.1"], "port": 3307, "timeout": 5, "account": "root", "startConns": "swoole_cpu_num()*3", "maxSpareConns": "swoole_cpu_num()*8", "maxSpareExp": 3600, "maxConns": "swoole_cpu_num()*15"}}}, "databases": {"akcs_db": {"serverInfo": "main_server", "startConns": "swoole_cpu_num()*3", "maxSpareConns": "swoole_cpu_num()*5", "maxSpareExp": 3600, "maxConns": "swoole_cpu_num()*10", "charset": "utf8mb4"}, "test_db": {"serverInfo": "main_server", "startConns": "swoole_cpu_num()*2", "maxSpareConns": "swoole_cpu_num()*3", "maxSpareExp": 3600, "maxConns": "swoole_cpu_num()*5", "charset": "utf8mb4"}}}}