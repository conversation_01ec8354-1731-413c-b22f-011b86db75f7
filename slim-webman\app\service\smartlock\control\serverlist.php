<?php

namespace  smartlock\control;
require_once __DIR__ . "/../../smartlock/model/smartLock.php";
require_once __DIR__ . "/../../../util/common.php";

function serverlist($request, $response)
{
    $message["port"] = MQTT_TLS_PORT;
    $message["domain"] = MQTT_DOMAIN;
    $message["gate_https_srv"] = SMARTLOCK_HTTP_GATE_SERVER;
    $message["file_https_srv"] = FILE_SERVER;

    $response_data = [
        'success' => true,
        'timestamp' => time(),
        'result' => $message,
    ];
    return json($response_data);
}