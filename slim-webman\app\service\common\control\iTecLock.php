<?php

namespace common\control\iTec;

require_once __DIR__ . '/../model/devices.php';
require_once __DIR__ . '/../model/personalDevices.php';
require_once __DIR__ . '/../model/iTecLock.php';
require_once __DIR__ . '/../model/thirdLock.php';

// pm下发公共和楼栋的所有lock,无需判断权限组
function getPmLockList()
{
    $allLockList = array();
    $userConf = \util\container\getUserData();
    $projectLockList = \common\model\iTec\getLockListByAccountUUID($userConf['MngUUID']);
    foreach ($projectLockList as $row => $device) {
        if ($device['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT || $device['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC) {
            $allLockList[] = $device;
        }
    }

    if (empty($allLockList)) {
        return [];
    }

    getLinkDeviceInfo($allLockList);
    $adaptApiLockList = adaptApiLockList($allLockList);

    return $adaptApiLockList;
}

// 社区账号下发pub/unit/apt所有有权限的lock
function getCommunityEnduserLockList()
{
    $userConf = \util\container\getUserData();
    $allLockList = \common\model\iTec\getAptLockList($userConf['NodeUUID']);
    $projectLockList = \common\model\iTec\getLockListByAccountUUID($userConf['MngUUID']);
    foreach ($projectLockList as $row => $device) {
        if ($device['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT || $device['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC) {
            $allLockList[] = $device;
        }
    }

    if (empty($allLockList)) {
        return [];
    }

    getLinkDeviceInfo($allLockList);
    $permissionLockList = getPermissionLockList($allLockList, $userConf['UserAccount']);
    $adaptApiLockList = adaptApiLockList($permissionLockList);

    return $adaptApiLockList;
}

// 单住户账号下发apt内所有lock
function getPersonnalEnduserLockList()
{
    $userConf = \util\container\getUserData();
    $aptLockList = \common\model\iTec\getAptLockList($userConf['NodeUUID']);
    if (empty($aptLockList)) {
        return [];
    }

    getLinkDeviceInfo($aptLockList);
    $adaptApiLockList = adaptApiLockList($aptLockList);

    return $adaptApiLockList;
}

// 转换为下发给app的数据格式
function adaptApiLockList($lockList)
{
    $adaptApiLockList = array();

    foreach ($lockList as $eachLock) {
        $payActive = \common\model\thirdLock\getThirdLockPayActiveStatus($eachLock['UUID'], ITEC_LOCK_TYPE);
        $adaptedLock = array(
            'brand' => 'iTec',
            'type' => ITEC_LOCK_TYPE,
            'uuid' => strval($eachLock['UUID']),
            'status' => intval($eachLock['IsOnline']),
            'location' => strval($eachLock['Name']),
            'grade' => intval($eachLock['Grade']),
            'pay_active' => intval($payActive) 
        );


        // 构建绑定设备信息
        $bondedDevices = array();
        $bondedDevices['mac'] = $eachLock['mac'] ? $eachLock['mac'] : '';
        // log运算将1248转换为0123,接口请求时开0123relay,每把锁只能绑定一个relay
        $bondedDevices['relay_id'] = $eachLock['Relay'] ? log($eachLock['Relay'], 2) : -1;

        // 将绑定设备信息添加到适配后的锁数据中
        $adaptedLock['bonded_devices'] = $bondedDevices;

        // 将适配后的锁数据添加到结果数组的末尾
        $adaptApiLockList[] = $adaptedLock;
    }

    return $adaptApiLockList;
}

function getLinkDeviceInfo(&$allLockList)
{
    foreach ($allLockList as &$lock) {
        if (!$lock['DeviceUUID']) {
            continue;
        }

        $lockType = $lock['ProjectType'];
        if ($lockType == LOCK_PROJECT_TYPE_COMMUNITY) {
            $bindDeviceInfo = \common\model\getDevicesInfoByUUID($lock['DeviceUUID']);
        } elseif ($lockType ==  LOCK_PROJECT_TYPE_PERSONNAL) {
            $bindDeviceInfo = \common\model\getPersonalDevicesInfoByUUID($lock['DeviceUUID']);
        }

        $lock['mac'] = $bindDeviceInfo['MAC'];
    }
}

function getPermissionLockList($allLockList, $user) {
    $permissionLockList = [];
    foreach ($allLockList as $lock) {
        //未link的锁要判断其自己的权限组
        if(!$lock['mac'] && $lock['Grade'] != ITEC_LOCK_GRADE_APT) {
            $accessGroup = \common\model\thirdLock\getAccessGroupThirdLock($lock['UUID'], ITEC_LOCK_TYPE, $user);
            if(!$accessGroup) {
                continue;
            }
        }

        array_push($permissionLockList, $lock);
    }
    return $permissionLockList;
}

function openDoor($userConf, $uuid)
{
    $linkMAC = "";
    $isJson = true;
    $heaader = array();
    $lockInfo = \common\model\iTec\getLockInfo($uuid);
    if (isset($lockInfo['DeviceUUID'])) {
        $deviceInfo = \common\model\getDeviceInfoByRoleAndUUID($userConf['Role'], $lockInfo['DeviceUUID']);
        if($deviceInfo) {
            $linkMAC = $deviceInfo['MAC'];
        }
    }
    // 判断锁link的mac relay是否有权限
    if (strlen($lockInfo['DeviceUUID']) > 0) {
        $openDoorRelay = $lockInfo['Relay'] - 1;
        // 只能link relay, 不能link security relay, 所以security relay传null
        $canOpendoor = \common\model\checkOpendoorPermission($userConf, $linkMAC, $openDoorRelay, null);
        if (!$canOpendoor) {
            \util\log\akcsLog::debug("itec open door has no access, linkMAC = {$linkMAC}, linkRelay = {$lockInfo['Relay']}");
            return ERR_CODE_NO_PERMISSION;
        }
    }
    
    $data = array();
    $data['link_mac'] = $linkMAC;
    $data['role'] = $userConf['Role'];
    $data['lock_type'] = ITEC_LOCK_TYPE;
    $data['lock_id'] = $lockInfo['LockId'];
    $data['initiator'] = $userConf['Name'];
    $data['lock_name'] = $lockInfo["Name"];
    $data['account'] = $userConf['UserAccount'];
    $data['personal_account_uuid'] = $userConf['NodeUUID'];
    $data['personal_account_uuid_for_operator'] = $userConf['UUID']; // 添加操作者UUID
    $data['message_type'] = LINKER_MSG_TYPE_ITEC_OPEN_DOOR;
    $data['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR;

    // 请求到cslinke进行开锁
    $response = \util\common\httpRequest(
        "post",
        "http://" . CSLINKER_HTTP_SERVER . '/app_open_itec_lock',
        $heaader,
        $data,
        $isJson,
        20
    );

    $result = ERR_CODE_OPEN_DOOR_FAILED;
    $json_data = json_decode($response, true);

    if ($json_data['code'] == 0 && $json_data['data']['err_code'] == \common\model\FAILED_TYPE_SUCCESS) {
        $result = ERR_CODE_SUCCESS;
    } else if ($json_data['code'] == 0 && $json_data['data']['err_code'] == \common\model\FAILED_TYPE_ITEC_REMOTE_OPEN_NOT_IN_ACCESSGROUP) {
        $result = ERR_CODE_NO_PERMISSION;
    } else if ($json_data['data']['err_code'] == \common\model\THIRD_LOCK_FAILED_TYPE_ITECLOCK_NOT_EXIST) {
        $result = ERR_CODE_ITEC_LOCK_NOT_EXIST;
    }  
    else {
        $result = ERR_CODE_OPEN_DOOR_FAILED;
    }

    \util\log\akcsLog::debug('itec open door response=' . $response);
    return $result;
}

