<?php

namespace common\model;

use PDO;

function updatePersonalAlarms($alarmID, $alarmResult)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("update PersonalAlarms set Status = 1,DealTime = now(),DealResult = :result where ID = :id");
    $sth->bindParam(':id', $alarmID, PDO::PARAM_INT);
    $sth->bindParam(':result', $alarmResult, PDO::PARAM_STR);
    $sth->execute();
}

function updateAlarms($alarmID, $alarmResult)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("update Alarms set Status = 1,DealTime = now(),DealResult = :result where ID = :id");
    $sth->bindParam(':id', $alarmID, PDO::PARAM_INT);
    $sth->bindParam(':result', $alarmResult, PDO::PARAM_STR);
    $sth->execute();
}

// 警报类型常量定义
define('ALARM_OLD_DATA', 0);                   // 旧数据
define('ALARM_DOOR_UNLOCK', 1);                // 门未关告警
define('ALARM_INFRARED', 2);                   // 红外告警
define('ALARM_DRMAGENT', 3);                   // 告警
define('ALARM_SMOKE', 4);                      // 烟雾告警
define('ALARM_GAS', 5);                        // 燃气告警
define('ALARM_URGENCY', 6);                    // 紧急告警
define('ALARM_SOS', 7);                        // SOS告警
define('ALARM_TAMPER', 8);                     // 防拆告警
define('ALARM_EMERGENCY', 9);                  // 紧急告警
define('ALARM_DOOR_HELD_OPEN', 10);            // 门常开告警
define('ALARM_EMERGENCY_NOTIFY_OPEN', 11);     // 紧急开门
define('ALARM_EMERGENCY_NOTIFY_CLOSED', 12);   // 紧急关门
define('ALARM_AJAX_ALARM', 13);                // AJAX告警
define('ALARM_MOTION', 14);                    // 移动侦测
define('ALARM_BREAK_IN', 15);                  // 强闯告警

function getAlarmNums()
{
    $db = \util\container\getDb();
    $userConf = \util\container\getUserData();
    $role = $userConf['Role'];

    if ($role === ROLE_TYPE_PERSONNAL_MASTER) { //主账号
        //主账号的时候,根据Account查找主账号下面的设备列表
        $sth = $db->prepare("/*master*/ select count(1) as count from PersonalAlarms where Node = :account and status = 0");
        $sth->bindParam(':account', $userConf['Account'], PDO::PARAM_STR);
    } elseif ($role === ROLE_TYPE_COMMUNITY_MASTER || $role === ROLE_TYPE_COMMUNITY_PM) { //社区主账号
        //主账号的时候,根据Account查找主账号下面的设备列表
        $sth = $db->prepare("/*master*/ select count(1) as count from Alarms where Node = :account and status = 0");
        $sth->bindParam(':account', $userConf['Account'], PDO::PARAM_STR);
    } elseif (\util\common\isOfficeEndUser($role)) { //办公
        $sth = $db->prepare("/*master*/ select count(1) as count from Alarms where Node = :account and status = 0");
        $sth->bindParam(':account', $userConf['Account'], PDO::PARAM_STR);
    } elseif ($role === ROLE_TYPE_COMMUNITY_SLAVE) { //社区从账号
        //从账号的时候,根据Account查找主账号下面的所有未处理告警
        $sth = $db->prepare("/*master*/ select count(1) as count from PersonalAccount A left join PersonalAccount B on B.ID = A.ParentID left join Alarms D on D.Node = B.Account where A.Account = :account and D.status = 0");
        $sth->bindParam(':account', $userConf['UserAccount'], PDO::PARAM_STR);
    } elseif ($role === ROLE_TYPE_OFFICE_NEW_ADMIN) {
        //admin App上展示的alarm：门口机private door分配到公司下的+室内机分配给公司下人员的
        // AlarmCode： 0-old data/1-Door Unlock/2-Infrared/3-Drmagent/4-Smoke/5-Gas/6-Urgency/
        //             7-SOS/8-Tamper/9-Emergency/10-DoorHeldOpen/11-EmergencyNotifyOpen/
        //             12-EmergencyNotifyClose/13-AJAXAlarm/14-Motion/15-BreakIn
        $sth = $db->prepare("/*master*/ SELECT count(*) as count FROM Alarms
            WHERE MngAccountID = :mng_account_id 
            AND status = 0
            AND (
                (CompanyUUID = :companyUUID AND AlarmCode IN (:emergency_notify_open, :emergency_notify_closed) AND Node = :account) 
                OR (CompanyUUID = :companyUUID AND AlarmCode IN (:old_data, :door_unlock, :infrared, :drmagent, :smoke, :gas, :urgency, :sos, :tamper, :door_held_open, :motion, :break_in)) 
                OR (AlarmCode = :emergency)
                )");
        
        // 绑定常量值
        $sth->bindValue(':emergency_notify_open', ALARM_EMERGENCY_NOTIFY_OPEN, PDO::PARAM_INT);
        $sth->bindValue(':emergency_notify_closed', ALARM_EMERGENCY_NOTIFY_CLOSED, PDO::PARAM_INT);
        $sth->bindValue(':old_data', ALARM_OLD_DATA, PDO::PARAM_INT);
        $sth->bindValue(':door_unlock', ALARM_DOOR_UNLOCK, PDO::PARAM_INT);
        $sth->bindValue(':infrared', ALARM_INFRARED, PDO::PARAM_INT);
        $sth->bindValue(':drmagent', ALARM_DRMAGENT, PDO::PARAM_INT);
        $sth->bindValue(':smoke', ALARM_SMOKE, PDO::PARAM_INT);
        $sth->bindValue(':gas', ALARM_GAS, PDO::PARAM_INT);
        $sth->bindValue(':urgency', ALARM_URGENCY, PDO::PARAM_INT);
        $sth->bindValue(':sos', ALARM_SOS, PDO::PARAM_INT);
        $sth->bindValue(':tamper', ALARM_TAMPER, PDO::PARAM_INT);
        $sth->bindValue(':door_held_open', ALARM_DOOR_HELD_OPEN, PDO::PARAM_INT);
        $sth->bindValue(':motion', ALARM_MOTION, PDO::PARAM_INT);
        $sth->bindValue(':break_in', ALARM_BREAK_IN, PDO::PARAM_INT);
        $sth->bindValue(':emergency', ALARM_EMERGENCY, PDO::PARAM_INT);
                
        $sth->bindParam(':mng_account_id', $userConf['MngID'], PDO::PARAM_INT);
        $sth->bindParam(':account', $userConf['Account'], PDO::PARAM_STR);
        $sth->bindParam(':companyUUID', $userConf['OfficeCompanyUUID'], PDO::PARAM_STR);
    } else {
        //从账号的时候,根据Account查找主账号下面的所有未处理告警
        $sth = $db->prepare("/*master*/ select count(1) as count from PersonalAccount A left join PersonalAccount B on B.ID = A.ParentID left join PersonalAlarms D on D.Node = B.Account where A.Account = :account and D.status = 0");
        $sth->bindParam(':account', $userConf['UserAccount'], PDO::PARAM_STR);
    }
    $ret = $sth->execute();
    $count = $sth->fetch(PDO::FETCH_ASSOC);

    return $count['count'];
}
