# 连接池降级机制文档

## 概述

本项目实现了一个优雅的数据库连接降级机制，确保在连接池不可用时能够自动降级到传统的直连方式，保证服务的稳定性和可用性。

## 架构设计

### 降级策略

1. **优先级1**: 连接池方式 (高性能)
2. **优先级2**: 传统直连方式 (兜底保障)

### 核心组件

- `MySQLConnectionPool`: MySQL连接池实现
- `ConnectionPool Bootstrap`: Workerman启动时初始化连接池
- `container.php`: 提供统一的连接获取接口，内置降级逻辑

## 实现细节

### 1. 连接池初始化

在Workerman启动时，通过`support/bootstrap/ConnectionPool.php`初始化连接池：

```php
// 配置参数
$mysqlConfig = [
    'min_connections' => 5,        // 最小连接数
    'max_connections' => 20,       // 最大连接数
    'idle_timeout' => 300,         // 空闲超时(秒)
    'health_check_interval' => 60  // 健康检查间隔(秒)
];

// 初始化连接池
MySQLConnectionPool::getInstance($mysqlConfig);
```

### 2. 降级逻辑

#### getDb() 函数降级流程：

```php
function getDb(){
    $gContainer = GlobalApp::getInstance();
    $db = $gContainer->get('akcs_db');
    
    if(!$db) {
        // 步骤1: 尝试从连接池获取
        try {
            $pool = \util\pool\MySQLConnectionPool::getInstance();
            $db = $pool->getConnection();
            $gContainer->set('akcs_db', $db);
            // 记录成功日志
            return $db;
        } catch (\Exception $e) {
            // 记录降级日志
        }
        
        // 步骤2: 降级到传统方式
        $gContainer->set('akcs_db', initDb());
    }
    
    return $gContainer->get('akcs_db');
}
```

#### medooDb() 函数降级流程：

```php
function medooDb(){
    // 类似getDb()，但获取Medoo实例
    // 1. 尝试 pool->getMedooConnection()
    // 2. 降级到 initMedooDb()
}
```

### 3. 传统连接方式

当连接池不可用时，系统会自动使用传统的直连方式：

```php
// 传统PDO连接
function initDb() {
    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCS";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

// 传统Medoo连接
function initMedooDb() {
    $database = new \util\medoo\Medoo([
        'database_name' => 'AKCS',
        'server' => AKCS_DATABASEIP,
        'port' => AKCS_DATABASEPORT,
        'username' => DATABASEUSER,
        'password' => DATABASEPWD
    ]);
    return $database;
}
```

## 使用方式

### 在应用代码中使用

```php
// 获取数据库连接（自动降级）
$db = \util\container\getDb();

// 获取Medoo实例（自动降级）
$medoo = \util\container\medooDb();

// 获取Redis连接（已支持降级）
$redis = \util\container\getRedis();
```

### 配置文件

确保在`config/bootstrap.php`中启用连接池：

```php
return [
    support\bootstrap\Session::class,
    support\bootstrap\LaravelDb::class,
    support\bootstrap\ConnectionPool::class,  // 连接池启动器
];
```

## 监控和日志

### 日志记录

系统会自动记录连接获取的详细日志：

- `[container] 使用连接池获取DB连接` - 成功使用连接池
- `[container] 连接池获取DB失败，降级到传统方式` - 降级触发
- `[container] 使用传统方式获取DB连接` - 使用传统方式

### 测试脚本

运行测试脚本验证降级机制：

```bash
php test_connection_fallback.php
```

## 优势

1. **高可用性**: 连接池故障时自动降级，确保服务不中断
2. **性能优化**: 优先使用高性能的连接池
3. **透明切换**: 应用代码无需修改，自动处理降级
4. **完整日志**: 详细记录连接获取过程，便于监控和调试
5. **配置灵活**: 连接池参数可根据需求调整

## 注意事项

1. 连接池初始化失败不会影响服务启动
2. 降级到传统方式时，每次调用都会创建新连接
3. 建议监控日志，及时发现连接池问题
4. 在高并发场景下，连接池的性能优势更加明显

## 故障排查

### 常见问题

1. **连接池初始化失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否可用
   - 查看日志中的具体错误信息

2. **频繁降级**
   - 检查连接池配置是否合理
   - 监控数据库连接数是否达到上限
   - 检查网络连接稳定性

3. **性能问题**
   - 调整连接池大小参数
   - 监控连接池使用情况
   - 考虑增加数据库连接数限制
