<?php

namespace resident\model;

use PDO;

function getExtraDeviceRelayActionsByRelayUUID($relayUUID)
{
    $db = \util\container\getDb();
    
    $actionSql = "SELECT UUID, ActionType, Input, Output, HoldDelay, ConnectType, TriggerModel, Status 
                  FROM ExtraDeviceRelayAction 
                  WHERE ExtraDeviceRelayListUUID = :relayUUID";
    $actionStmt = $db->prepare($actionSql);
    $actionStmt->bindParam(":relayUUID", $relayUUID, PDO::PARAM_STR);
    $actionStmt->execute();
    $actions = $actionStmt->fetchAll(PDO::FETCH_ASSOC);
    
    return $actions;
}

function getExtraDeviceRelayActionInfoByUUID($relayActionUUID)
{
    $db = \util\container\getDb();
    
    $sql = "SELECT edra.ActionType, edra.HoldDelay, edra.Status, edra.Output, edrl.Name, edrl.Function, edrl.CreateTime,
                   ed.DeviceIndex, ed.<PERSON>, imc.ExtraRelayType, imc.DeviceUUID, IFNULL(d.MAC, pd.MAC) as MAC, IFNULL(d.Status, pd.Status) as DeviceStatus
            FROM ExtraDeviceRelayAction edra 
            JOIN ExtraDeviceRelayList edrl ON edra.ExtraDeviceRelayListUUID = edrl.UUID 
            JOIN ExtraDevice ed ON edrl.ExtraDeviceUUID = ed.UUID
            JOIN IndoorMonitorConfig imc ON ed.IndoorMonitorConfigUUID = imc.UUID
            LEFT JOIN Devices d ON imc.DeviceUUID = d.UUID
            LEFT JOIN PersonalDevices pd ON imc.DeviceUUID = pd.UUID
            WHERE edra.UUID = :uuid";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':uuid', $relayActionUUID, PDO::PARAM_STR);
    $stmt->execute();
    $relayInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return $relayInfo;
}

function updateExtraDeviceRelayActionHoldDelayByUUID($uuid, $holdDelay)
{
    $db = \util\container\getDb();
    
    $sql = "UPDATE ExtraDeviceRelayAction SET HoldDelay = :holdDelay WHERE UUID = :uuid";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':holdDelay', $holdDelay, PDO::PARAM_STR);
    $stmt->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $result = $stmt->execute();
    
    if ($result) 
    {
        return 0;
    } 
    else 
    {
        return -1;
    }
} 

function incrementIndoorMonitorConfigMeta($deviceUUID) 
{
    $db = \util\container\getDb();
    
    $sql = "UPDATE IndoorMonitorConfig SET Meta = Meta + 1 WHERE DeviceUUID = :deviceUUID";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':deviceUUID', $deviceUUID, PDO::PARAM_STR);
    $result = $stmt->execute();
    
    if ($result) 
    {
        \util\log\akcsLog::debug("incrementIndoorMonitorConfigMeta - successfully updated Meta for device UUID: $deviceUUID");
        return 0;
    } 
    else 
    {
        \util\log\akcsLog::debug("incrementIndoorMonitorConfigMeta - failed to update Meta for device UUID: $deviceUUID");
        return -1;
    }
} 