<?php

namespace common\model;

use PDO;

function updateThirdPartyLockToken($refreshToken, $idToken, $expireTime, $uuid)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("update ThirdPartyLockAccount set RefreshToken = :refresh_token, Token = :id_token, ExpireTime = :expireTime where UUID = :uuid");
        $sth->bindParam(':refresh_token', $refreshToken, PDO::PARAM_STR);
        $sth->bindParam(':id_token', $idToken, PDO::PARAM_STR);
        $sth->bindParam(':expireTime', $expireTime, PDO::PARAM_STR);
        $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
        $sth->execute();
}

function getThirdPartyLockInfo($nodeUuid, $lockType)
{
    $db = \util\container\getDB();
    //查找第三方token
    $sth = $db->prepare("SELECT Token,UUID,RefreshToken FROM ThirdPartyLockAccount where PersonalAccountUUID = :PersonalAccountUUID and LockType = :LockType");
    $sth->bindParam(':PersonalAccountUUID', $nodeUuid, PDO::PARAM_STR);
    $sth->bindParam(':LockType', $lockType, PDO::PARAM_INT);
    $sth->execute();
    $authInfo = $sth->fetch(PDO::FETCH_ASSOC);
    return $authInfo;
}