<?php

namespace  common\control;
require_once __DIR__ . "/../../common/model/thirdPartyLockDevice.php";
require_once __DIR__ . "/../../common/model/thirdPartyLockAccount.php";
require_once __DIR__ . "/qrioLock.php";
require_once __DIR__ . "/yaleLock.php";
require_once __DIR__ . "/saltoLock.php";
require_once __DIR__ . "/dormakabaLock.php";
require_once __DIR__ . "/iTecLock.php";
require_once __DIR__ . "/TTLock.php";

function getOauthUrl($request, $response)
{
    //第三方类型0=Qrio，1=Yale
    $paramValue = $request->getQueryParams();
    $type = $paramValue['type'];

    // 生成64位的唯一标识（State）和用户关联
    $state = \util\common\randomkeys(64);

    if ($type == QRIO_LOCK_TYPE) {
        $url = QRIO_AUTH_URL."/authorize?state=".$state."&response_type=code&scope=offline_access%20openid&client_id=".QRIO_CLIENT_ID."&redirect_uri=".QRIO_REDIRECT_URL;
    } elseif ($type == YALE_LOCK_TYPE) {
        $url = YALE_AUTH_URL."/authorization?state=".$state."&client_id=".YALE_CLIENT_ID."&redirect_uri=".YALE_REDIRECT_URL;
    }
    
    $data = ["url" => $url];
    return \util\response\setThirdLinkerResponseMessage($response, "success", 0, $data);
}

function authBind($request, $response)
{
    $userConf = \util\container\getUserData();
    $nodeUuid = $userConf['NodeUUID'];

    $postDatas = $request->getParsedBody();
    $type = $postDatas['type'];
    $code = $postDatas['code'];

    $authData = array();

    if ($type == QRIO_LOCK_TYPE) {
        \common\control\getQrioAuthdata($code, $authData);
        if ($authData['Token']) {
            \common\model\bindAuthdata($nodeUuid, $authData, $type);
            \common\control\addQrioLock($userConf, $authData['Token']);
        } else {    
            //请求第三方失败，返回错误
            return \util\response\setThirdLinkerResponseMessage($response, "get authdata from thirdparty failed!", 500);
        }
    } elseif ($type == YALE_LOCK_TYPE) {
        \common\control\getYaleAuthdata($code, $authData);
        if ($authData['Token']) {
            \common\model\bindAuthdata($nodeUuid, $authData, $type);
            \common\control\addYaleLock($userConf, $authData['Token']);
        } else {
            //请求第三方失败，返回错误
            return \util\response\setThirdLinkerResponseMessage($response, "get authdata from thirdparty failed!", 500);
        }
    }

    return \util\response\setThirdLinkerResponseMessage($response, "success", 0);
}

function yaleWebhook($request, $response) {
    $bodyData = $request->getParsedBody();
    $sign = $request->getHeaderLine('X-August-Signature');
    $token = $request->getHeaderLine('x-auth-token');

    \util\log\akcsLog::debug("body data:" . $bodyData);

    if(\common\control\yaleCheckSign($sign, json_encode($bodyData)) == false) {
        \util\log\akcsLog::debug("yale_check_sign error");
        //签名校验错误 直接返回
        return \util\response\setResponseMessage($response, STATE_TOKEN_INVAILD, [], "get authdata from thirdparty failed!" );
    }

    $lockID = $bodyData['LockID'];
    $yaleLockInfo = \common\model\getYaleLockInfoByDeviceUUID($lockID);
    //x-auth-token = md5('ThirdPartyLockDevice.UUID')
    if( !$yaleLockInfo || $token != md5($yaleLockInfo['UUID']) ) {
        \util\log\akcsLog::debug("yale_check_token error");
        //锁不存在或鉴权错误 delete webhook
        if($bodyData['EventType'] == 'operation') {
            \common\control\delYaleWebhook($lockID, $yaleLockInfo['Token']);
        }
        return \util\response\setResponseMessage($response, STATE_TOKEN_INVAILD, [], "error" );
    }
    
    //{"timeStamp":1663144538028,"EventID":"4750127a-8ebc-4c70-ab75-d80f8ecec00e","LockID":"D693DD45336DB8B395FEBAEE6C501CDD","EventType":"operation","Event":"closed","Device":"lock","User":{"UserID":"DoorStateChanged"}}  
    if($bodyData['EventType'] == 'operation' && $bodyData['User']['UserID'] == 'DoorStateChanged') {
        \common\control\handleYaleDoorSensorLog($yaleLockInfo, $bodyData['Event']);
    }
    //{"EventType": "system","LockID": "lock ID of lock with the battery event","Event":"lock_battery_alert","warningLevel":"lock_state_battery_warning_2week","userID": "the user ID who registered the webhook"}
    else if($bodyData['EventType'] == 'system' && $bodyData['Event'] == 'lock_battery_alert') {
        \common\control\handleYaleLowBatteryMsg($yaleLockInfo, $bodyData['warningLevel']);
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function thirdPartyDoorControl($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();

    \util\log\akcsLog::debug(json_encode($postDatas));

    $devUUID = $postDatas['uuid'];
    $type = $postDatas['type'];
    $operate_type = $postDatas['operate_type'];

    if ($devUUID === null || $type === null || $operate_type === null) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "body is null");
    }

    switch ($type) {
        case QRIO_LOCK_TYPE:
            \common\control\openQrioDoor($userConf, $devUUID, $operate_type);
            break;
        case YALE_LOCK_TYPE:
            \common\control\openYaleDoor($userConf, $devUUID, $operate_type);
            break;
        case DORMAKABA_LOCK_TYPE:
                $code = \common\control\openDormakabaDoor($userConf, $devUUID);
                return \util\response\setResponseMessage($response, $code);
            break;
        case SALTO_LOCK_TYPE:
                $code = \common\control\salto\openSaltoDoor($userConf, $devUUID);
                return \util\response\setResponseMessage($response, $code);
            break;
        case ITEC_LOCK_TYPE:
                $code = \common\control\iTec\openDoor($userConf, $devUUID);
                return \util\response\setResponseMessage($response, $code);
            break;
        case TT_LOCK_TYPE:
                $code = \common\control\TTLock\openDoor($userConf, $devUUID);
                return \util\response\setResponseMessage($response, $code);
            break;
        default:
            \util\log\akcsLog::debug("open_third_party_door brand no support");
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function getThirdpartyLock($request, $response)
{
    $userConf = \util\container\getUserData();

    $qrioLockList = array();
    $yaleLockList = array();
    $saltoLockList = array();
    $dormakabaLockList = array();
    
    $ret1 = \common\control\GetAndUpdateQrioLockinfo($userConf, $qrioLockList);
    $ret2 = \common\control\GetAndUpdateYaleLockinfo($userConf, $yaleLockList);
    \common\control\salto\getSaltoLockRealTimeStatus($userConf, $saltoLockList);
    \common\control\getDormakabaLockRealTimeStatus($userConf, $dormakabaLockList);

    if (false == $ret1) {
        return \util\response\setResponseMessage($response, 2001);
    }

    if (false == $ret2) {
        return \util\response\setResponseMessage($response, 2002);
    }

    $thirdPartyDevList = array_merge($qrioLockList, $yaleLockList);
    $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $saltoLockList);
    $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $dormakabaLockList);
    $data = ["third_party_dev_list" => $thirdPartyDevList];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $data);
}