<?php

namespace resident\model;

use PDO;

function updateEnableShowHoldDoor($enableShowHoldDoor, $userAccount)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("UPDATE PersonalAccountCnf SET EnableShowHoldDoor = :enable_show_hold_door WHERE Account = :account");
    $sth->bindParam(':enable_show_hold_door', $enableShowHoldDoor, PDO::PARAM_INT);
    $sth->bindParam(':account', $userAccount, PDO::PARAM_STR);
    $sth->execute();
}

function getEnableShowHoldDoor($account)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ select EnableShowHoldDoor from PersonalAccountCnf where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['EnableShowHoldDoor'];
}