<?php

namespace common\model;
use PDO;

require_once __DIR__ . "/../../../config/define.php";
require_once __DIR__ . "/account.php";
require_once __DIR__ . "/distributorInfo.php";

function getOemNameByAccountInfo($accountInfo) 
{
    if (!$accountInfo || !isset($accountInfo['Grade'])) 
    {
        return DIS_OEM_NAME_AKUVOX; // 默认返回Akuvox
    }
    
    $dis_uuid = '';

   
    if (isset($accountInfo['ID']) && isset($accountInfo['ManageGroup']) && 
        $accountInfo['ID'] == $accountInfo['ManageGroup'] && 
        in_array($accountInfo['Grade'], [GRADE_TYPE_COMM, GRADE_TYPE_INSTALLER])) 
    {
        // INS判断条件：ID = ManageGroup AND Grade IN (21, 22)
        $dis_uuid = $accountInfo['ParentUUID'];
    }
    else if ($accountInfo['Grade'] == GRADE_TYPE_PM) 
    {
        // PM的ParentUUID是INS的UUID，INS的ParentUUID是DIS的UUID
        $insInfo = \common\model\getAccountByUUID($accountInfo['ParentUUID']);
        if ($insInfo) 
        {
            $dis_uuid = $insInfo['ParentUUID'];
        }
    }
    else 
    {
        return DIS_OEM_NAME_AKUVOX;
    }
    
    // 根据distributor UUID获取OEM名称
    if (!empty($dis_uuid)) 
    {
        return getOemNameByDisUuid($dis_uuid);
    }
    
    return DIS_OEM_NAME_AKUVOX; // 默认返回Akuvox
}

function getOemNameByDisUuid($dis_uuid) 
{
    if (empty($dis_uuid)) 
    {
        return DIS_OEM_NAME_AKUVOX;
    }
    
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT di.OemType FROM DistributorInfo di 
                        JOIN Account a ON di.Account = a.Account 
                        WHERE a.UUID = :dis_uuid");
    $sth->bindParam(':dis_uuid', $dis_uuid, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    
    $oemName = "";
    
    if ($result && isset($result['OemType'])) 
    {
        // 根据OemType转换为OEM名称
        switch ($result['OemType']) 
        {
            case DIS_INFO_OEM_DEFAULT: 
                $oemName = DIS_OEM_NAME_AKUVOX;
                break;
            case DIS_INFO_OEM_HAGE: 
                $oemName = DIS_OEM_NAME_HAGER;
                break;
            case DIS_INFO_OEM_PALWINTERCS: // DIS_OEM_TYPE_PALWINTECS
                $oemName = DIS_OEM_NAME_PALWINTECS;
                break;
            case DIS_INFO_OEM_FASTTEL: // DIS_OEM_TYPE_FASTTEL
                $oemName = DIS_OEM_NAME_FASTTEL;
                break;
            default:
                $oemName = DIS_OEM_NAME_AKUVOX; // 默认返回Akuvox
                break;
        }
    } 
    else 
    {
        $oemName = DIS_OEM_NAME_AKUVOX; // 默认返回Akuvox
    }
    
    return $oemName;
} 
