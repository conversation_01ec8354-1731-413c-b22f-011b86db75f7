<?php

namespace office\model;

use PDO;

require_once __DIR__ . "/../../../util/common.php";
require_once __DIR__ . "/../../common/model/version.php";

//获取用户关联的设备列表
//maclist 个人的门口机设备列表
//pubMaclist公共设备列表
function getUserAGDeviceListForOffice($account, &$maclist, &$pubMaclist)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select mac,relay,SecurityRelay from UserAccessGroupDevice  where UserAccessGroupID in (select ID from UserAccessGroup where Account=:Account)");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $ret = $sth->execute();
    $macs = $sth->fetchALL(PDO::FETCH_ASSOC);
    
    foreach ($macs as $key => $value) {
        #array_push($maclist, $value["mac"]);
        \util\common\getAgMacInfo($maclist, $value);
    }
    
    $sth = $db->prepare("select mac,relay,SecurityRelay from AccessGroupDevice where AccessGroupID in (select AccessGroupID From AccountAccess where Account=:Account)");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $ret = $sth->execute();
    $macs = $sth->fetchALL(PDO::FETCH_ASSOC);
    
    foreach ($macs as $key => $value) {
        #array_push($maclist, $value["mac"]);
        \util\common\getAgMacInfo($pubMaclist, $value);
    }

    //默认权限组
    $sth = $db->prepare("select CommunityID, UnitID from AccessGroup G left join AccountAccess  A on A.AccessGroupID=G.ID where A.Account=:Account");
    $sth->bindParam(':Account', $account, PDO::PARAM_STR);
    $ret = $sth->execute();
    $macs = $sth->fetchALL(PDO::FETCH_ASSOC);
    
    $communityID = 0;
    foreach ($macs as $key => $value) {
        $communityID = $value['CommunityID'];
        $unitID = $value['UnitID'];
        if ($unitID > 0) {
            $sth2 = $db->prepare("select mac,relay,SecurityRelay from Devices where (UnitID=:UnitID and Grade=2) or (Grade=1 and MngAccountID=:MngAccountID);");
            $sth2->bindParam(':UnitID', $unitID, PDO::PARAM_STR);
            $sth2->bindParam(':MngAccountID', $communityID, PDO::PARAM_STR);
            $sth2->execute();
            $devList = $sth2->fetchALL(PDO::FETCH_ASSOC);
            foreach ($devList as $row => $value) {
                $pubMaclist[$value["mac"]] = \util\common\relayInfo2RelayInt($value["relay"]);//这里会覆盖普通权限组的设备relay,默认权限组选择所有relay
                $pubMaclist[\util\common\getUserAGSecurityKey($value["mac"])] = \util\common\relayInfo2RelayInt($value["SecurityRelay"]);
            }
        }
    }
}

function getOfficeDevicesList($resultToken, &$macs, &$resultArray)
{
    $db = \util\container\getDb();

    //获取权限组设备列表
    $agMacList = array(); //key is mac,value is relay
    $agPubMacList = array(); //key is mac,value is relay
    \office\model\getUserAGDeviceListForOffice($resultToken['UserAccount'], $agMacList, $agPubMacList);

    $noMonitorList = \common\model\getNoMonitorList();

    $sth2 = $db->prepare("select Status,MAC,SipAccount,Location,Firmware,Type, RtspPwd,SipPwd,Relay,DclientVer,ArmingFunction,Flags,SecurityRelay,DoorRelayStatus,DoorSeRelayStatus,AllowEndUserMonitor from Devices  where Node = :Node AND Grade = 3");
    $sth2->bindParam(':Node', $resultToken['Account'], PDO::PARAM_STR);
    $ret = $sth2->execute();

    $devList = $sth2->fetchALL(PDO::FETCH_ASSOC);
    foreach ($devList as $row => $dev) {
        if (\util\common\deviceIsAGDevType($dev['Type']) && !array_key_exists($dev['MAC'], $agMacList)) {
            \util\log\akcsLog::debug("user devices not in account access. account:" . $resultToken['UserAccount'] . " dev:" . $dev['MAC']);
            continue;
        }
        $agRelay = 127; //设置的比较大，防止后面relay个数大于4时候没有处理到
        $agSecurityRelay = 127;
        if (\util\common\deviceIsAGDevType($dev['Type'])) {
            $agRelay = $agMacList[$dev['MAC']];
            $agSecurityRelay = $agMacList[\util\common\getUserAGSecurityKey($dev['MAC'])];
        }

        if (!$dev['MAC']) {
            continue;
        }
        $curNode = array();
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $curNode['rtsp_nonce'] = \util\redisManage\getNonce($curNode['mac']);
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = $dev['Type'];
        $curNode['is_public'] = '0';
        $curNode['dclient_ver'] = $dev['DclientVer'];
        $curNode['dtmf'] = '#';
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], $agRelay, RELAY_TYPE_LOCAL);
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], $agSecurityRelay, RELAY_TYPE_SE_LOCAL);
        }
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } else {
            $curNode['arming_function'] = 1;
        }
        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }
        array_push($resultArray, $curNode);
        array_push($macs, $dev['MAC']);
    }

    $pubDevListStr = "";
    foreach ($agPubMacList as $mac => $releay) {
        if (strlen($pubDevListStr) == 0) {
            $pubDevListStr = "'".$mac."'";
        } else {
            $pubDevListStr = $pubDevListStr . "," . "'".$mac."'";
        }
    }
    if ($pubDevListStr == "") {
        //空时候下面SQL会报错
        return;
    }

    //公共设备
    $sth2 = $db->prepare("select D.Status,D.MAC,D.SipAccount,D.Location,D.Relay,D.Type, D.RtspPwd,D.DclientVer,D.Firmware,D.Flags,D.ID,D.Grade,D.ArmingFunction,D.SecurityRelay,D.DoorRelayStatus,D.DoorSeRelayStatus,D.AllowEndUserMonitor from Devices D where Mac in($pubDevListStr);");
    $ret = $sth2->execute();
    $devList = $sth2->fetchALL(PDO::FETCH_ASSOC);
    foreach ($devList as $row => $dev) {
        $curNode = array();
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $curNode['rtsp_nonce'] = \util\redisManage\getNonce($curNode['mac']);
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = $dev['Type'];
        $curNode['is_public'] = '1';
        $curNode['dclient_ver'] = $dev['DclientVer'];
        if (\util\common\deviceIsManageBuilding($dev['Type'], $dev['Flags'], $dev['Grade'])) {
            if (!\common\model\isManageBuilding($dev['ID'], $resultToken['UnitID'])) {
                \util\log\akcsLog::debug("public devices not manage building, Account:" . $resultToken['UserAccount'] . " dev:" . $dev['MAC'] . "  unit id:" . $resultToken['UnitID']);
                continue;
            }
        }

        $agRelay = 127; //设置的比较大，防止后面relay个数大于4时候没有处理到
        $agSecurityRelay = 127;
        if (\util\common\deviceIsAGDevType($dev['Type'])) {
            $agRelay = $agPubMacList[$dev['MAC']];
            $agSecurityRelay = $agPubMacList[\util\common\getUserAGSecurityKey($dev['MAC'])];
        }

        $curNode['dtmf'] = '#';
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], $agRelay, RELAY_TYPE_LOCAL);
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], $agSecurityRelay, RELAY_TYPE_SE_LOCAL);
        }
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } else {
            $curNode['arming_function'] = 1;
        }
        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }
        array_push($resultArray, $curNode);
    }
}
