<?php
require_once __DIR__ . "/../common/control/dndHandle.php";
require_once __DIR__ . "/../newoffice/control/userconf.php";
require_once __DIR__ . "/../newoffice/control/bleCode.php";
require_once __DIR__ . "/../newoffice/control/nfcCode.php";
require_once __DIR__ . "/../newoffice/control/callType.php";
require_once __DIR__ . "/../newoffice/control/dealAlarm.php";
require_once __DIR__ . "/../newoffice/control/faceHandle.php";
require_once __DIR__ . "/../newoffice/control/userFeedback.php";
require_once __DIR__ . "/../newoffice/handle/auditLogHandle.php";
require_once __DIR__ . "/../newoffice/control/thirdLockHandle.php";
require_once __DIR__ . "/../newoffice/control/msgHandle.php";
require_once __DIR__ . "/../newoffice/control/officeOpenDoor.php";
require_once __DIR__ . "/../newoffice/control/loginConf.php"; 
$gApp->setProjectType(PROJECT_TYPE_NEW_OFFICE);


$gApp->get('/login_conf', function ($request, $response) {
    $response = \newoffice\control\getLoginConfV70($request, $response);
    return $response; 
});

$gApp->post('/opendoor', function ($request, $response) {
    $response = \newoffice\control\OpenSiteDoorNewOffice($request, $response);
    return $response;
});

$gApp->get('/commconf', function ($request, $response) {
    $response = \newoffice\control\getOfficeCommConf($request, $response);
    return $response;
});

$gApp->get('/userconf', function ($request, $response) {
    $userRole = \util\container\getUserRole();

    if ($userRole == ROLE_TYPE_OFFICE_NEW_PER) {
        $response = \newoffice\control\getNewOfficePerUserConf($request, $response);
    } else if ($userRole == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        $response = \newoffice\control\getNewOfficeAdminUserConf($request, $response);
    }

    return $response;
});

$gApp->post('/setbleconf', function ($request, $response) {
    $userConf = \util\container\getUserData();
    $old_data = \common\model\getBLEConf($userConf['Account']);

    $response = \newoffice\control\setOfficeBleConf($request, $response);

    $new_data = \common\model\getBLEConf($userConf['Account']);
    \newoffice\auditlog\handleSetBle($request, $old_data, $new_data);
    return $response;
});

$gApp->post('/setnfcconf', function ($request, $response) {
    $userConf = \util\container\getUserData();
    $old_data = \common\model\getNFCCode($userConf['Account']);

    $response = \newoffice\control\setOfficeNfcCode($request, $response);

    $new_data = \common\model\getNFCCode($userConf['Account']);
    \newoffice\auditlog\handleSetNfc($request, $old_data, $new_data);
    return $response;
});

// 不同角色处理不同
$gApp->post('/setcalltype', function ($request, $response) {
    $userConf = \util\container\getUserData();
    $old_calltype = \newoffice\control\getNewOfficeCalltype($userConf);

    $response = \newoffice\control\setOfficeCallType($request, $response);

    $new_calltype = \newoffice\control\getNewOfficeCalltype($userConf);
    \newoffice\auditlog\handleSetCallType($request, $old_calltype, $new_calltype);
    return $response;
});

$gApp->post('/upload_face', function ($request, $response) {
    $response = \newoffice\control\uploadNewOfficeFaceV70($request, $response);
    \newoffice\auditlog\handleUploadFace($request, $response);
    return $response;
});

$gApp->get('/delete_face', function ($request, $response) {
    $response = \newoffice\control\deleteFace($request, $response);
    \newoffice\auditlog\handleDeleteFace($request, $response);
    return $response;
});

$gApp->post('/dealpersonalalarm', function ($request, $response) {
    $response = \newoffice\control\dealNewOfficeAlarm($request, $response);
    return $response;
});

// 设置免打扰配置
$gApp->post('/set_dnd', function ($request, $response) {
    $userConf = \util\container\getUserData();
    $personalAccount = $userConf['UserAccount'];
    $old_data = \common\model\queryDND($personalAccount);

    $response = \common\control\setDnd($request, $response);

    $new_data = \common\model\queryDND($personalAccount);
    \newoffice\auditlog\handleSetDnd($request, $old_data, $new_data);
    return $response;
});

$gApp->post('/third_party_door_control', function ($request, $response) {
    $response = \newoffice\control\thirdPartyDoorControl($request, $response);
    return $response;
});

$gApp->get('/get_face_status', function ($request, $response) {
    $response = \newoffice\control\getFaceStatus($request, $response);
    return $response;
});

$gApp->post('/feedback', function ($request, $response) {
    $response = \newoffice\control\NewOfficeAppUserFeedback($request, $response);
    return $response;
});

$gApp->post('/set_msg_read', function ($request, $response) {
    $response = \newoffice\control\setMsgRead($request, $response);
    return $response;
});

// 获取三方锁列表
$gApp->get('/get_thirdparty_lock', function ($request, $response) {
    $response = \newoffice\control\getThirdpartyLock($request, $response);
    return $response;
});