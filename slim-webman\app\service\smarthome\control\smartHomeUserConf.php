<?php

namespace smarthome\control;

require_once __DIR__ . "/../../common/model/account.php";
require_once __DIR__ . "/../../resident/model/communityInfo.php";
require_once __DIR__ . "/../../resident/model/devices.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/devices.php";
require_once __DIR__ . "/../../common/model/thirdPartCamera.php";
require_once __DIR__ . "/../../common/model/manegeFeature.php";

function getSHUserConfV64($request,$response)
{
    $userData = \util\container\getUserData();
    \util\log\akcsLog::debug("getSHUserConfV64 uid:" . $userData['UserAccount']);

    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"PM app not allow");
        return $response;
    }
    
    $transType = $userData['SipType'];
    $mngSipType = \common\model\getUserMngSipType($userData['Role'], $userData['ParentID']);
    if ($mngSipType["SipType"] != 3) {//0udp 1tcp 2tls 3none 管理员不配置
        $transType = $mngSipType["SipType"];
    }
    if ($transType == 0) {
        $transType = 1;
    }
    
    $appSip = $userData['SipAccount'];
    $appSipPwd = $userData['SipPwd'];
    $codec = $userData['Codec'];

    $isFeatureExpire = \resident\model\checkFeaturePlanIsExpire($userData['MngID']);
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        //根据Account查找APPSpecial中是否存在该从账号
        $checkSlave = \common\model\checkFamilyMemberControl($userData['MngID'], $userData['UserAccount'], $isFeatureExpire);
        if ($checkSlave == 0) {
            \util\log\akcsLog::debug("getSHUserConfV64 checkFamilyMemberControl false. uid:" . $userData['UserAccount']);
            \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"checkFamilyMemberControl false");
            return $response;
        }
    }
    $communityInfo = \resident\model\getCommunityInfo($userData['MngID']);
    $resultArray = array();
    $macs = array();
    $allMacs = array();
    $thirdPartyDevList = array();
    //社区主从账号
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        if($communityInfo['IsNew']){
            $resultArray = \resident\model\getNewCommDevicesList($macs, $allMacs);
        }
        else{
            $resultArray = \resident\model\getOldCommDevicesList($macs, $allMacs);
        }
    }
    //单住户主从账号
    if ($userData['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userData['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        $resultArray = \resident\model\getPerDevicesList($macs);
    }
    
    //$is_smarthome_conf = 1;
    //家居的不校验室内机方案，家居那边限制--chenzhx ********
    // $checkDev = \common\model\checkIndoorPayPlan($macs, $userData['Role'], $userData['Account'], $is_smarthome_conf);
    // if ($checkDev == 0) {
    //     \util\log\akcsLog::debug("getSHUserConfV64 checkIndoorPayPlan false. uid:" . $userData['UserAccount']);
    //     \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"checkIndoorPayPlan false");
    //     return $response;
    // }
    \common\model\getThirdPartyCameraList($userData, $thirdPartyDevList);

    $app = array();
    \common\model\checkAccountExpire2($app, $userData);
    if ($app["account_expire"] == APP_ACCOUNT_HAS_EXPIRED) {
        \util\log\akcsLog::debug("getSHUserConfV64 checkAccountExpire2 false. uid:" . $userData['UserAccount']);
        \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"checkAccountExpire2 false");
        return $response;
    }

    $isShowFace = \common\model\checkShowFace($userData, $isFeatureExpire);
    $isShowTmpkey = \common\model\checkShowTmpkey($userData, $isFeatureExpire);
    $enablePinConfig = \common\model\checkEnablePinConfig($userData, $communityInfo['Switch'], $isFeatureExpire);
    
    $appConf = array();
    $appConf['sip'] = (string)$appSip;
    $appConf['display_name'] =  (string)$userData['Name'];
    $appConf['sip_passwd'] =  (string)\util\utility\passwdDecode($appSipPwd);
    $appConf['video_res']  = "2";
    $appConf['video_bitrate'] = "512";
    $appConf['trans_type'] = (string)$transType;//0-udp, 1-tcp, 2-tls
    $appConf['codec'] =  (string)$codec; //0=PCMU, 8 =PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义
    //$appConf['family_member'] = $familyArray; //暂时不需要返回
    $appConf['rtp_confuse'] = (int)$mngSipType["RtpConFuse"];
    $appConf['enable_pin_config'] = (int)$enablePinConfig;
    $appConf['show_tempkey'] = (int)$isShowTmpkey;
    $appConf['show_face'] = (int)$isShowFace;
    $appConf['role'] = (int)$userData['Role'];

    // 主站点的sip账号密码
    $userInfo = \common\model\getUserInfoByUUID($userData['UserInfoUUID']);
    $mainSipInfo = \common\model\getUserSipInfo($userInfo['AppMainUserAccount']);
    $appConf['main_sip'] = (string)$userInfo['AppMainUserAccount'];
    $appMainSipPwd = $mainSipInfo['SipPwd'];
    $appConf['main_sip_passwd'] = (string)\util\utility\passwdDecode($appMainSipPwd);
    $appConf['is_site_link'] = (int)\common\control\checkIsMultiSiteUser();

    $datas = [
        "user" => $appConf,
        "dev_list" => $resultArray,
        "third_party_dev_list" => $thirdPartyDevList
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}
