<?php

namespace  resident\control;

require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";

//setCallType 0=app 1=phone 2=app先 未接听后phone
function setCallType($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();
    $calltype = $postDatas['calltype'];

    \common\model\updateCalltype($userConf, $calltype);
    $auditLog = new \util\auditlog\AuditLog();

    if ($userConf['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userConf['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        webPersonalModifyNotify(WEB_PER_NODE_UPDATE, $userConf['Account']);
        $keyInfo = $userConf['Account'];
    } elseif ($userConf['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userConf['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        webCommunityModifyNotify(WEB_COMM_NODE_UPDATE, $userConf['Account'], $mac = "", $userConf['MngID'], $userConf['UnitID']);
        $keyInfo = $auditLog->getCallTypeKeyInfo($userConf['MngID'], $userConf['UnitID'], $userConf['RoomID']);
        \util\log\akcsLog::debug("roomId=" . $userConf['RoomID'] . ";keyInfo=" . $keyInfo);
    } elseif ($userConf['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        webCommunityModifyNotify(WEB_COMM_MODIFY_PM_APP_ACCOUNT, $userConf['Account'], $mac = "", $userConf['MngID'], $userConf['UnitID']);
        $keyInfo = $userConf['Account'];
    }
    $auditLog->insertAuditLog($request->getRemoteIp(), $userConf['UserAccount'], $calltype + AUDIT_TYPE_SET_CALL_TYPE_SMARTPLUS_INDOOR, $keyInfo, $userConf['Role'], $userConf['MngID']);

    $datas = [
        'calltype' => $calltype
    ];
    
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}