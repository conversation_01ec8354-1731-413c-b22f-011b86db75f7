<?php

namespace common\model;

function addAppFeedback($personalAccountUUID, $content, $contactEmail, $fileList, $uuid)
{
    \util\container\medooDb()->insert("AppFeedback", ["PersonalAccountUUID" => $personalAccountUUID, "ContactEmail" => $contactEmail, "Content" => $content, "FileList" => $fileList, "UUID" => $uuid]);
}

function addAppFeedbackReceiverList($account, $feedbackUUID)
{
    $uuid = \util\common\getMysqlUUID();
    \util\container\medooDb()->insert("AppFeedbackReceiverList", ["ReceiverAccount" => $account, "FeedbackUUID" => $feedbackUUID, "UUID" => $uuid]);
}