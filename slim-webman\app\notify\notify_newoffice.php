<?php

require_once(dirname(__FILE__).'/socket_newoffice.php');

//社区警告被处理通知
const MSG_P2A_NEWOFFICE_ALARM_DEAL      = MSG_P2A + 2020;

/*******************************办公消息枚举*******************************/

function SendNewOfficeAppFeedbackEmail($emailInfo, $oem_name)
{
    //协议通用字段
    $datas['email'] = $emailInfo['email'];
    $datas['send_to_type'] = $emailInfo['send_to_type'];
    $datas['language'] = $emailInfo['language'];
    $datas['content'] = (string)$emailInfo['content'];
    $datas['contact_email'] = (string)$emailInfo['contact_email'];
    $datas['user'] = (string)$emailInfo['sip_account'];
    $datas['ins_account'] = (string)$emailInfo['ins_name'];
    $datas['file_list'] = (string)$emailInfo['file_list'];
    $datas['user_type'] = $emailInfo['user_type'];
    //适配cspush
    $datas['project_type'] = PROJECT_TYPE_NEW_OFFICE;
    $datas['email_type'] = "office_app_feedback";
    //新办公信息字段
    $datas['office_name'] = (string)$emailInfo['project_name'];
    $datas['company_name'] = (string)$emailInfo['company'];
    //发给dis的邮件需要携带dis信息，用于邮件标题，ins的已存在ins_account中了
    if($datas['send_to_type'] == "dis") {
        $datas['dis_account'] = (string)$emailInfo['dis_name'];
    }
    //内部邮件需要设置抄送列表
    if($datas['send_to_type'] == "company") {
        $datas['cc_list'] = (string)$emailInfo['cc_list'];
    }

    $datas['email_type'] = "office_app_feedback";

    \util\log\akcsLog::debug("[sendAppFeedbackEmail] : Send email = {email}", ['email' => $datas['email']]);
    $trace_id = null;
    $jsondata = \util\common\createGeneralMessage(
        "email",
        $datas,
        $trace_id,
        $oem_name
    );

    JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);
}

function JsonMessageNotify($from, $jsondata)
{
    $Socket = new CJsonMessageNotify();

    $Socket->setMsgFrom($from);
    $Socket->setMsgID(0);
    $Socket->copy(json_encode($jsondata));
}

function newOfficeAlarmDealNotify($node, $user, $id, $result)
{
    \util\log\akcsLog::debug("[AlarmDealNotify]node=[" . $node . "] user=[" . $user . "] id=[" . $id . "] result=" . $result);
    $data[] = $node;
    $data[] = $user;
    $data[] = $id;
    $data[] = $result;
    $alarmDealSocket = new CAlarmDealSocket();
    $alarmDealSocket->setMsgID(MSG_P2A_NEWOFFICE_ALARM_DEAL);
    $alarmDealSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $alarmDealSocket->copy($data);
}

function AuditLogNotify($from, $jsondata)
{
    /**
    *  {
    *        "trace_id": "最大长度64",
    *        "timestamp": 0,
    *        "data": {
    *        "user": "用户uuid",
    *        "userType": "super|dis|subDis|ins|pm|admin|endUser",
    *        "action": "当前用户操作数据所属动作，参考全局配置文件中action的定义，例如create",
    *        "model": "当前用户操作数据所属的模块，参考全局配置文件中model的定义，例如officeAdmin",
    *        "ip": "用户id地址",
    *        "list": [
    *            {
    *            "operaType": "当前项的操作动作类型，create，update，delete",
    *            "model": "当前项的所属的模块，参考全局配置文件中model的定义，例如officeAdmin",
    *            "action": "当前项的动作，参考全局配置文件中action的定义，例如add",
    *            "object": {
    *                "uuid": "操作对象uuid",
    *                "name": "操作对象描述名字，一般使用对应的name，非必须，可放空"
    *            },
    *            "item": [
    *                {
    *                "dataType": "数据类型， input(用户输入类型)，
    *                                       enum(系统定义枚举类型，需要让前端显示多语言词条，若需要统一由web后端定义，和前端对齐，不可随便新增类型)，
    *                                       select(其他模块的uuid)",
    *
    *                "key": "描述数据的key，参考全局配置文件中key的定义，不可以随便新增，若需要统一由web后端定义，和前端对齐",
    *                "value": "插入的值，删除前的值，更新后的值",
    *                "valueBefore": "更新前的值，更新时需要",
    *                "model": "数据所属的模块，参考全局配置文件中model的定义，例如officeAdmin",
    *                "isEncrypt": true,
    *                "isHidden": true
    *                }
    *            ]
    *            }
    *        ]
    *        }
    *    }
    */
    $Socket = new CJsonMessageNotify();
    
    // 发送给web的消息不要带MsgHead头
    $Socket->setMsgFrom($from);
    $Socket->setMsgID(0);
    $Socket->copy_without_head(json_encode($jsondata), 'notify_web_auditlog');
}
