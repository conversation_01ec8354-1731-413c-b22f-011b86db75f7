<?php

namespace office\control;

require_once __DIR__ . "/../../../notify/notify_office.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";

function setOfficeBleConf($request, $response)
{
    $postDatas = $request->getParsedBody();
    $userConf = \util\container\getUserData();
    $type = @$postDatas['opendoor_type'];

    $randBLEcode = "";
    if ($postDatas['enable'] == '1') {
        \util\common\getBLECode($randBLEcode);
    }

    \common\model\UpdateBLECode($randBLEcode, $userConf['UserAccount'], $type);

    WebOfficeAccountModifyNotify($userConf['MngID'], $userConf['UserAccount']);

    $datas = [
        'blecode' => $randBLEcode,
        'opendoor_type' => intval($type)
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}