<?php

namespace office\control;

require_once __DIR__ . "/../../common/control/faceHandle.php";
require_once __DIR__ . "/../../common/model/faceMng.php";
require_once __DIR__ . "/../../../util/aesManage.php";


function uploadOfficeFaceV70($request, $response)
{
    $userConf = \util\container\getUserData();

    $files = (array) $request->file();
    if (($file = reset($files)) === false || !$file->isValid()) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Please upload face image");
    }
    $fileName = $file->getUploadName();
    \util\log\akcsLog::debug("filename=" . $fileName);

    $node = $userConf['Account'];
    $account = $userConf['UserAccount'];
    $mngAccountID = $userConf['MngID'];

    $faceEncFileRelativePath = '/' . (int) $mngAccountID % 100 . '/' . $mngAccountID . '/' . (int) $node % 64;
    $extraPath = FACE_FILE_PREFIX . $faceEncFileRelativePath;
    \util\common\mkDirs($extraPath);

    $fileInfo = array();
    $fileInfo[FILE_NAME] = $fileName;
    $fileInfo[FILE_FULL_NAME] = $extraPath . '/' . $fileName;
    $fileInfo[FILE_ENC_NAME] = $extraPath . '/' . $fileName . '.enc';
    $file->move($fileInfo[FILE_FULL_NAME]);

    $detect_result = \util\common\faceUpload($fileInfo[FILE_FULL_NAME], $userConf['UserAccount']);
    if (!$detect_result || $detect_result['code'] != 0) {
        \util\log\akcsLog::debug("Please upload face image");
        unlink($fileInfo[FILE_FULL_NAME]);
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Please upload face image");
    }

    $fileInfo[FACE_FILE_MD5] = $detect_result['data']['face_file_md5'];
    $fileInfo[FACE_FILE_URL] = $detect_result['data']['face_file_url'];

    \util\log\akcsLog::debug("fileInfo=" . json_encode($fileInfo));

    $personalAccountInfo['PersonalAccountID'] = $userConf['UserAccountID'];
    $personalAccountInfo['UnitID'] = $userConf['UnitID'];
    $personalAccountInfo['Node'] = $userConf['Account'];
    $personalAccountInfo['UUID'] = $userConf['UUID'];
    \util\log\akcsLog::debug("personalAccountInfo=" . json_encode($personalAccountInfo));

    if (!\common\model\mergeFaceMngWithoutUpload($personalAccountInfo, $fileInfo, $mngAccountID)) {
        \util\log\akcsLog::debug("mergeFaceMngWithoutUpload falied!");
        unlink($fileInfo[FILE_FULL_NAME]);
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "Failed to write database");
    }

    if (unlink($fileInfo[FILE_FULL_NAME])) {
        \util\log\akcsLog::debug("remove file:" . $fileInfo[FILE_FULL_NAME] . " success!");
    } else {
        \util\log\akcsLog::debug("remove file:" . $fileInfo[FILE_FULL_NAME] . " failed!");
    }

    WebOfficeAccountModifyNotify($userConf['MngID'], $userConf['UserAccount']);

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function deleteFace($request, $response)
{
    $userConf = \util\container\getUserData();
    $personalAccountID = $userConf['UserAccountID'];
    $data = \common\model\queryFaceMng($personalAccountID);
    if ($data) {
        $faceUrl = $data["FaceUrl"];
        if ($faceUrl) {
            if (!\util\fdfs\fdfs_del_pic_by_url($faceUrl)) {
                $fullPath = "/var/www/download/face/" . $faceUrl;
                unlink($fullPath);
            }
        }
    }
    $affected = \common\model\deleteFaceMng($personalAccountID);
    if ($affected > 0) {
        WebOfficeAccountModifyNotify($userConf['MngID'], $userConf['UserAccount']);
    }
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function uploadFaceDynamic($request, $response)
{
    $postDatas = $request->getParsedBody();
    $tag = $postDatas['tag'];
    $all = $postDatas['all'];
    $index = $postDatas['index'];

    if ($index == $all) {
        uploadOfficeFaceV70($request, $response);
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}