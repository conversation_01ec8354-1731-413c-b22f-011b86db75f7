<?php
namespace util\common;
require_once __DIR__ . "/versionCheck.php";
require_once __DIR__ . "/time.php";
require_once __DIR__ . "/../config/define.php";

use PDO;

//特殊请求列表
const SPECIAL_REQ_LIST = ["/", "/welcome", "/office/welcome"];

function getUserAGSecurityKey($mac)
{
    return $mac . "-Security";
}

function getMysqlUUID()
{
    $db = \util\container\getDb();
    $sth = $db->prepare('select uuid() as uuid');
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    $uuid = $data['uuid'];
    return SERVER_LOCATION.'-'.str_replace('-', '', $uuid);
}

function DevSupportOpenDoorAck($func)
{
    return \util\utility\switchHandle($func, DevFunctionSupportOpenDoorAck);
}


function relayInfo2RelayInt($relayinfo)
{
    //6.7relay字段变更为json数组
    if(is_string($relayinfo)){
        $relays = json_decode($relayinfo, true);
    }
    else{
        $relays = $relayinfo;
    }
    if(!is_array($relays)) {
        if(!empty($relayinfo)) {
            \util\log\akcsLog::debug("pass relay wrong, origin relay info:" . $relayinfo);
        }
        return 0;
    }
    $relayValue = 0;
    $relayIndex = 0;
    foreach($relays as $relay) {
        if($relay['enable'] == 1) {
            $relayValue += (1 << $relayIndex);
        }
        $relayIndex++;
    }
    return $relayValue;
}

function getRelayStatusFromFlags($flags)
{
    $relayStatus[0] = \util\utility\switchHandle($flags, DevFlagsRelay1);
    $relayStatus[1] = \util\utility\switchHandle($flags, DevFlagsRelay2);
    $relayStatus[2] = \util\utility\switchHandle($flags, DevFlagsRelay3);
    $relayStatus[3] = \util\utility\switchHandle($flags, DevFlagsRelay4);
    return $relayStatus;
}

function getDoorRelayStatusFromStatus($status)
{
    if($status == "") {
        //字段为空代表全未检测到，每个relay状态都置为2
        return array(2,2,2,2);
    }
    $reportLen = strlen($status);
    $statusNumber = str_split($status);
    //relay status部分字段缺失保护
    for($i = 0; $i < $reportLen; $i++) {
        $doorRelayStatus[$i] = intval($statusNumber[$i]);
    }
    for($i = $reportLen; $i < 4; $i++) {
        $doorRelayStatus[$i] = 2;
    }
    return $doorRelayStatus;
}

function getDoorSeRelayStatusFromStatus($status)
{
    if($status == "") {
        return array(2,2);
    }
    $reportLen = strlen($status);
    $statusNumber = str_split($status);
    //security relay status部分字段缺失保护
    for($i = 0; $i < $reportLen; $i++) {
        $doorSeRelayStatus[$i] = intval($statusNumber[$i]);
    }
    for($i = $reportLen; $i < 2; $i++) {
        $doorSeRelayStatus[$i] = 2;
    }
    return $doorSeRelayStatus;
}

function getSecurityRelayStatusFromFlags($flags)
{
    $securityRelayStatus[0] = 0;
    $securityRelayStatus[1] = 0;
    $securityRelayStatus[2] = 0;
    $securityRelayStatus[3] = 0;
    return $securityRelayStatus;
}

function deviceIsAGDevType($type)
{
    if ($type == DEVICE_TYPE_STAIR || $type == DEVICE_TYPE_DOOR || $type == DEVICE_TYPE_ACCESS) {
        return true;
    } else {
        return false;
    }
}
//relayType: 0=local relay 1=security relay
function relay2DataArray($relay, $relayStatus,$doorRelayStatus, $devType, $agRelay, $relayType)
{
    //6.7relay字段变更为json数组
    $DataArray = array();
    $relays = json_decode($relay, true);
    if(!is_array($relays)) {
        if(!empty($relay)) {
            \util\log\akcsLog::debug("pass relay wrong, origin relay info:" . $relay);
        }
        return $DataArray;
    }
    $relayIndex = 0;
    foreach ($relays as $rel) {
        $relayinfo = array();
        $relayinfo['door_name'] = $rel['name'];
        $relayinfo['dtmf'] = $rel['dtmf'];
        $relayinfo['relay_id'] = "$relayIndex";
        $relayinfo['show_home'] = strval($rel['showHome']);
        $relayinfo['show_talking'] = strval($rel['showTalking']);
        if ($devType == DEVICE_TYPE_INDOOR) {
            $relayinfo['show_home'] = "0";
            $relayinfo['show_talking'] = "0";
        }
        $relayinfo['enable'] = intval($rel['enable']);
        $relayinfo['enable'] = $relayinfo['enable'] << $relayIndex & $agRelay ? "1" : "0";
        $relayinfo['relay_status'] = $relayStatus[$relayIndex]?$relayStatus[$relayIndex] : 0;
        $relayinfo['door_relay_status'] = $doorRelayStatus[$relayIndex];
        //新版本下发relay type
        if($relayType == RELAY_TYPE_LOCAL)
        {
            $relayinfo['type'] = "Local";
        }

        $DataArray[$relayIndex] = $relayinfo;
        $relayIndex += 1;
    }
    return $DataArray;
}

function addExRelayInfoList(&$relayInfoList, $exRelayInfoList)
{
    foreach($exRelayInfoList as $row => $exRelayInfo)
    {
        $tmpExRelayInfo = array();
        $tmpExRelayInfo['door_name'] = $exRelayInfo['name'];
        
        // 根据DeviceIndex调整relay_id的值
        // DeviceIndex 1对应relay_id 0-15，DeviceIndex 2对应relay_id 16-31，DeviceIndex 3对应relay_id 32-47
        $deviceIndex = intval($exRelayInfo['device_index']);
        $baseRelayID = ($deviceIndex - 1) * RELAY_COUNT_PER_EXTERN_DEVICE; // 每个设备16个继电器，从0开始
        
        $relayIndex = intval($exRelayInfo['relay_index']);

        
        $relayID = $baseRelayID + $relayIndex - 1; // RelayIndex从1开始，app从0开始，所以需要-1
        
        $tmpExRelayInfo['relay_id'] = strval($relayID);
        $tmpExRelayInfo['enable'] = strval($exRelayInfo['relay_switch']);
        $tmpExRelayInfo['relay_status'] = intval($exRelayInfo['status']);
        $tmpExRelayInfo['type'] = "Extern";
        
        $relayOutputType = intval($exRelayInfo['relay_output_type']);
        if($relayOutputType == RELAY_OUTPUT_TYPE_DIGITAL_OUTPUT)//该类型的Extern relay发送给app的id范围为8~15，数据库里面是从id是1开始
        {
            // 对于数字输出，调整relay_id的基础上再+7(8-1=7,app从0开始，所以需要-1)
            //RelayIndex=1的数字输出映射到ID 8(1+7=8)
            $digitalRelayID = $baseRelayID + $relayIndex + 7;
            $tmpExRelayInfo['relay_id'] = strval($digitalRelayID);
        }
        array_push($relayInfoList, $tmpExRelayInfo);
    }
}

function createDoorRelayStatusArray($doorRelayStatus)
{
    $relayStatusArray = array();
    foreach($doorRelayStatus as $index => $status) {
        $relayStatusCode['relay_id'] = $index;
        $relayStatusCode['door_relay_status'] = $status;
        array_push($relayStatusArray, $relayStatusCode);
    }
    return $relayStatusArray;
}

//检查是否需要在主页显示relay
function checkRelayDisplay($relayArray, $securityRelayArray)
{
    foreach($relayArray as $rel) {
        if($rel['enable'] == 1 && $rel['show_home'] == '1') {
            return 1;
        }
    }
    foreach($securityRelayArray as $rel) {
        if($rel['enable'] == 1 && $rel['show_home'] == '1') {
            return 1;
        }
    }
    return 0;
}
function getUnitAccessGroupKey($unitId)
{
    return $unitId . "-UnitAccessGroup";
}

function devHavaDefaultAccessGroup($haveDefaultAg, $dev, $agMacList, $removeDefaultAgMacs)
{
    if(in_array($dev['MAC'], $removeDefaultAgMacs))
    {
        return false;
    }
    if ($dev['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC) { //最外围公共设备,判断是否有设置默认权限组
        if ($haveDefaultAg) {
            return true;
        } else {
            return false;
        }
    } else {  //单元楼栋的设备,判断是否有设置单元楼栋的默认权限组
        if (array_key_exists(\util\common\getUnitAccessGroupKey($dev['UnitID']), $agMacList)) {
            return true;
        } else {
            return false;
        }
    }
}

function isNeedMonitor($devType, $firmware, $noMonitorList)
{
    $monitor = 0;
    if ($devType == DEVICE_TYPE_STAIR || $devType == DEVICE_TYPE_DOOR) {
        $monitor = 1;
    } elseif ($devType == DEVICE_TYPE_ACCESS) {
        $model = explode('.', $firmware);
        if (!in_array($model[0], $noMonitorList)) {
            $monitor = 1;
        }
    }

    return $monitor;
}

function isEndUserNeedMonitor($noMonitorList, $devType, $firmware, $isAllowEndUserMonitor)
{
    return intval(isNeedMonitor($devType, $firmware, $noMonitorList) && $isAllowEndUserMonitor);
}

function isEndUserNeedCall($devType, $isAllowUserCallDevice)
{
    if ($devType == DEVICE_TYPE_STAIR || $devType == DEVICE_TYPE_DOOR) {
        return intval($isAllowUserCallDevice);
    }
    return 1;
}

function isPmNeedMonitor($noMonitorList, $devType, $firmware)
{
    return intval(isNeedMonitor($devType, $firmware, $noMonitorList));
}

function isSupportHighResolutionMonitoring($supportHighResolutionList, $firmware)
{
    $model = explode('.', $firmware);
    if (in_array($model[0], $supportHighResolutionList)) {
        return 1;
    }
    return 0;
}

//美国分配落地号码区分洲号，日本用单独的落地号码
function getPbxLandlineNumber($phonecode, $phone, $phone2, $phone3)
{
    if (!$phone && !$phone2 && !$phone3) {
        return [];
    }

    if (SERVER_LOCATION == SERVER_LOCATION_CCLOUND) {
        return PBX_LANDLINE_NUMBER_CCLOUND;
    } else if (SERVER_LOCATION == SERVER_LOCATION_JCLOUND) {
        return PBX_LANDLINE_NUMBER_JCLOUND;
    } else if (SERVER_LOCATION == SERVER_LOCATION_UCLOUND) {
        //拼接phonecode和phone,去除国家号和区号之间的'-',再取前四位
        $phone = substr(str_replace('-', '', $phonecode . $phone), 0, 4);
        $phone2 = substr(str_replace('-', '',  $phonecode . $phone2), 0, 4);
        $phone3 = substr(str_replace('-', '', $phonecode . $phone3), 0, 4);
        //保存匹配到的落地
        $stateLandline = array();
        $phoneArray = array($phone, $phone2, $phone3);
        foreach ($phoneArray as $phone) {
            foreach (PBX_LANDLINE_NUMBER_UCLOUND as $key => $state) {
                if (($index = array_search($phone, $state)) !== false) { 
                    array_push($stateLandline, $key);
                }
            }
        }
        //若phone/phone2/phone3匹配到了相同的落地，则需要进行去重操作
        if (count($stateLandline)){
            $stateLandline = array_values(array_unique($stateLandline));
            return $stateLandline;
        } else {
            //没匹配到区号，分配美国默认落地
            return PBX_LANDLINE_NUMBER_UCLOUND_DEFAULT;
        }
    } else {
        //scloud/ecloud/rucloud
        return PBX_LANDLINE_NUMBER_OTHERS;
    }
}

function deviceIsManageBuilding($type, $flags, $grade)
{
    //只有公共设备才需要根据楼栋管理
    if ($grade != COMMUNITY_DEVICE_TYPE_PUBLIC) {
        return false;
    }

    if ($type == DEVICE_TYPE_STAIR || $type == DEVICE_TYPE_DOOR || $type == DEVICE_TYPE_MANAGEMENT || $type == DEVICE_TYPE_ACCESS) {
        if (\util\utility\switchHandle($flags, DevFlagsManagement)) {
            return false;
        } else {
            return true;
        }
    } else {
        return false;
    }
}

function getAgMacInfo(&$macList, $dbValue)
{
    if (array_key_exists($dbValue["mac"], $macList)) {
        $macList[$dbValue["mac"]] = $macList[$dbValue["mac"]] | intval($dbValue["relay"]);
    } else {
        $macList[$dbValue["mac"]] = intval($dbValue["relay"]);
    }
    
    $mac = $dbValue["mac"];
    if (array_key_exists(\util\common\getUserAGSecurityKey($mac), $macList)) {
        $macList[\util\common\getUserAGSecurityKey($mac)] = $macList[\util\common\getUserAGSecurityKey($mac)] | intval($dbValue["SecurityRelay"]);
    } else {
        $macList[\util\common\getUserAGSecurityKey($mac)] = intval($dbValue["SecurityRelay"]);
    }
}

function getNFCCode(&$randNFCcode)
{
    $str = 'ABCDEF1234567890';
    $randStr = str_shuffle($str); //打乱字符串
    $randNFCcode = "F0" . substr($randStr, 0, 14);
}

function getBLECode(&$randBLEcode)
{
    $str = 'ABCDEF1234567890';
    $randStr = str_shuffle($str);
    $randBLEcode = "B" . substr($randStr, 0, 15);
}


function faceDetect($fileName)
{
    $command = '/usr/local/akcs/csadapt/bin/FaceDetect DetectFile ' . $fileName;
    $result = exec($command);
    \util\log\akcsLog::debug("faceDetect exec command=[" . $command . "],result=" . $result);
    return $fileName . '-detect';
}

function faceDetectDynamic($fileName, $save, $feature)
{
    $command = '/usr/local/akcs/csadapt/bin/FaceDetect DetectDynamic ' . $fileName. " " . $save. " " .$feature ;
    $result = exec($command);
    \util\log\akcsLog::debug("faceDetect exec command=[" . $command . "],result=" . $result);
    if (strstr($result, "error")) {
        return -1;
    }
    return 0;
}

function faceDetectV2($extractPath, $fileName)
{
    $command = '/usr/local/akcs/csadapt/bin/FaceDetectV2 ' ."'".$extractPath . '/' . $fileName."'" . " " .$extractPath;
    exec($command, $output, $result);
    switch ($result) {
        case UPLOAD_FACEPIC_SUCCESS:
            $errMsg = "success";
            break;
        case UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW:
            $errMsg = "Not front view";
            break;
        case UPLOAD_FACEPIC_ERROR_WEAR_MASK:
            $errMsg = "Mask detected";
            break;
        case UPLOAD_FACEPIC_ERROR_NO_FACE:
            $errMsg = "No face dectected";
            break;
        case UPLOAD_FACEPIC_ERROR_FACE_LARGE:
            $errMsg = "The face is too larger";
            break;
        case UPLOAD_FACEPIC_ERROR_FACE_SMALL:
            $errMsg = "The face is too small";
            break;
        case UPLOAD_FACEPIC_ERROR_MULTI_FACES:
            $errMsg = "More than one face";
            break;
        case UPLOAD_FACEPIC_ERROR_NOT_CLEAR:
            $errMsg = "Face not clear enough";
            break;
        default:
            $errMsg = "System Error";
            break;
    }
    \util\log\akcsLog::debug("faceDetect exec command=[" . $command . "],result=" . $result. ",errMsg=" . $errMsg);
    return $result;
}

//是否为社区和单住户
function isResidentUser($role)
{
    if($role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_PERSONNAL_SLAVE
    || $role == ROLE_TYPE_COMMUNITY_MASTER ||$role == ROLE_TYPE_COMMUNITY_SLAVE || $role == ROLE_TYPE_COMMUNITY_PM) {
        return true;
    } else {
        return false;
    }
}

//是否为单住户
function isSingleHouseUser($role)
{
    if($role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_PERSONNAL_SLAVE) {
        return true;
    } else {
        return false;
    }
}

function isOfficeEndUser($role)
{
    if($role == ROLE_TYPE_OFFICE_EMPLOYEE || $role == ROLE_TYPE_OFFICE_ADMIN || $role == ROLE_TYPE_OFFICE_NEW_PER) {
        return true;
    } else {
        return false;
    }
}
function isOfficeUser($role)
{
    if($role == ROLE_TYPE_OFFICE_EMPLOYEE || $role == ROLE_TYPE_OFFICE_ADMIN || $role == ROLE_TYPE_OFFICE_NEW_PER || $role == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        return true;
    } else {
        return false;
    }
}

function isNewOfficeUser($role)
{
    if($role == ROLE_TYPE_OFFICE_NEW_PER || $role == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        return true;
    } else {
        return false;
    }
}

//UUID。第一位为0：代表关联PersonalAccountUserInfo表。第一位为1：代表关联AccountUserInfo表'
function getAccountUserInfoTable($userInfoUUID)
{
    $parts = explode('-', $userInfoUUID);
    $digit = substr($parts[1], 0, 1);
    return $digit; 
}

function relayId2RelayInt($relayid)
{
    return [1, 2, 4, 8][$relayid];
}

function checkAppOpenDoor($agMacInfoList, $mac, $relay, $securityRelay)
{
    $isCanOpendoor = 0;
    if (array_key_exists($mac, $agMacInfoList)) {
        $realRelay = $agMacInfoList[$mac] & $relay;
        if ($realRelay) {
            $isCanOpendoor = 1;
        }
        $realRelay = $agMacInfoList[\util\common\getUserAGSecurityKey($mac)] & $securityRelay;
        if ($realRelay) {
            $isCanOpendoor = 1;
        }
    }
    return $isCanOpendoor;
}

function checkRelay($agRelay, $relay)
{
    return $agRelay & $relay;
}

function httpRequest($method, $url, $headers, $data = '', $isJson = 0, $timeout = 10)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    if ($method == 'post') {
        curl_setopt($curl, CURLOPT_POST, true);

        if ($isJson) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    } elseif ($method == 'put') {
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        if ($isJson) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    } elseif ($method == 'delete') {
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
    }
        
    
    $output = curl_exec($curl);
    if ($output == false) {
        \util\log\akcsLog::debug("Error: ".curl_error($curl));
    }
    curl_close($curl);
    return $output;
}

function randomkeys($length)
{
    $pattern = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLOMNOPQRSTUVWXYZ';
    $key = "";
    for ($i=0; $i < $length; $i++) {
        $key .= $pattern[mt_rand(0, 62)];    //生成php随机数
    }
    return $key;
}
/** 
 * 获取数据库查询中的"IN"条件占位字符串及获取参数绑定键值对
 * @param array  $dataList          需绑定到"IN"条件内占位符的数据
 * @param string $placeholderName   占位符名称,默认值为'placeholder'
 * @param bool   $filter            开关控制是否过滤0/空字符/false,默认值为true
 * @return array $bindParam         'bindString' 占位字符串 'bindArray' 绑定值       
 */
function getImplodeSqlData($dataList, $placeholderName = 'placeholder', $filter = true)
{
    if($filter) {
        //过滤0/空字符/false
        $dataList = array_filter($dataList);
    }
    if(empty($dataList)) {
        return array();
    }
    sort($dataList);
    $bindKeyName = array();
    $bindArray = array();
    foreach($dataList as $key => $data) {
        $keyName = ':' . $placeholderName . $key;
        array_push($bindKeyName, $keyName);
        $bindArray[$keyName] = $data;
    }
    $bindString = implode(',', $bindKeyName);
    $bindParam['bindString'] = $bindString;
    $bindParam['bindArray'] = $bindArray;
    return $bindParam;
}

//根据uuid哈希取模按百分比更改app siptype
function transSipType($uuid, $sipType)
{
    if ($sipType != APP_SIP_TYPE_UDP) {
        return strval($sipType);
    }
    $default_type = APP_SIP_TYPE_TCP;
    return strval($default_type);
 /*   20240530 chenzhx
    $hash = sprintf('%u', crc32($uuid));
    $ret = $hash % 100;
    
    if ($ret < 80 ) {
        return APP_SIP_TYPE_TCP;
    } else {
        return $sipType;
    }
*/
}

//设备首页无操作按钮则不显示设备的需求,APP无法实现,通过userconf不下发进行控制
function checkDevDisplay($devInfo)
{
    $apiVersion = \util\container\getApiVersion();

    // apiVersion大于6.7的下发列表app控制，小于6.7云来控制
    if ($apiVersion < 6.7) {
        //不带摄像头且所有relay都未开启show in homepage的门禁设备不下发
        if($devInfo['dev_type'] == DEVICE_TYPE_ACCESS) {
            if(checkRelayDisplay($devInfo['relay'], $devInfo['security_relay']) == 0 && $devInfo['is_need_monitor'] == 0) {
                \util\log\akcsLog::debug("dev_type :" . $devInfo['dev_type'] . "no display in homepage");
                return false;
            }
        }
    }
    
    return true;
}

function deldir($dir)
{
    //先删除目录下的文件：
    $dh = opendir($dir);
    while ($file = readdir($dh)) {
        if ($file != "." && $file!="..") {
            $fullpath = $dir."/".$file;
            if (!is_dir($fullpath)) {
                unlink($fullpath);
            } else {
                deldir($fullpath);
            }
        }
    }
    closedir($dh);
    rmdir($dir);
}

function mkDirs($dir)
{
    if (!is_dir($dir)) {
        if (!mkDirs(dirname($dir))) {
            return false;
        }
        if (!mkdir($dir, 0777)) {
            return false;
        }
    }
    return true;
}

function isSpecialReq($request)
{
    foreach(SPECIAL_REQ_LIST as $value) {
        if($request->getUri()->getPath() === $value) {
            return true;
        }
    }
    return false;
}

function createGeneralMessage($msg_type, $data,  &$trace_id = null, $oem_name = DIS_OEM_NAME_AKUVOX){
    $microseconds = (int)(microtime(true) * 1000000);
    $tmp_trace_id = substr(md5(strval($microseconds + rand(0, 1000))), 8, 16);
    $jsondata = [
        "msg_type" => $msg_type,
        "trace_id"=> $tmp_trace_id,
        "OEM"=> $oem_name,
        "timestamp"=> $microseconds,
        "data"=> $data
    ];

    if($trace_id !== null){
        $trace_id = $tmp_trace_id;
    }

    return $jsondata;
}

function isDoorType($type)
{
    if($type == DEVICE_TYPE_STAIR || $type == DEVICE_TYPE_DOOR)
    {
        return true;
    }
    return false;
}

function unitCheckError($dev, $unitId)
{
    if($dev['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && $unitId != $dev['UnitID'])
    {
        return true;
    }
    return false;
}

//PM管理的项目是否新社区或新办公，用于PM App新增相关字段时
function isPmManageNewProject($manageProjectType)
{
    if ($manageProjectType == PM_MANAGE_PROJECT_NEW_OFFICE || $manageProjectType == PM_MANAGE_PROJECT_NEW_COMMUNITY)
    {
        return true;
    }
    return false;
}

function faceUpload($filePath, $account)
{
    $curl = curl_init();
    $csFaceCutAddr = CSFACECUT_ADDR;
    $url = "http://$csFaceCutAddr/face/upload";
    $postFields = [
        'account' => $account,
        'face' => new \CURLFile($filePath) 
    ];
    curl_setopt($curl, CURLOPT_TIMEOUT, 6);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $postFields);       
    
    $output = curl_exec($curl);
    if ($output == false) {
        \util\log\akcsLog::debug("Error: ".curl_error($curl));
    }
    else {
        \util\log\akcsLog::debug("faceDetect result=" . $output);
        $output = json_decode($output, true);
    }
    curl_close($curl);
    return $output;    
}