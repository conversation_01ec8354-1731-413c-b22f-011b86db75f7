<?php

namespace resident\model;

use PDO;

require_once __DIR__ . "/devices.php";

// Zigbee设备查询字段列表常量
const ZIGBEE_DEVICE_SELECT_FIELDS = 'UUID, DeviceUUID, ZigbeeDeviceID, Name, DeviceType, Version, OnlineStatus, HoldDelay, Status, ThermostatsMode, Temperature, 
                                    TargetTemperature, MinTemperature, MaxTemperature, TemperatureUnit, HVACMode, CreateTime';

function getZigbeeDevicesByDeviceUUID($deviceUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT " . ZIGBEE_DEVICE_SELECT_FIELDS . " FROM IndoorMonitorZigbeeDevice WHERE DeviceUUID = :deviceUUID");
    $sth->bindParam(':deviceUUID', $deviceUUID, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetchAll(PDO::FETCH_ASSOC);
}

//根据zigbee设备UUID获取设备信息
function getZigbeeDeviceByUUID($zigbeeDeviceUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT " . ZIGBEE_DEVICE_SELECT_FIELDS . " FROM IndoorMonitorZigbeeDevice WHERE UUID = :zigbeeDeviceUUID");
    $sth->bindParam(':zigbeeDeviceUUID', $zigbeeDeviceUUID, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}

function checkUserHasZigbeeDevice()
{
    $devices = \resident\model\getIndoorDevicesList();
    if (empty($devices)) 
    {
        return 0;
    }
    
    // 遍历每个设备，检查是否有Zigbee设备
    foreach ($devices as $device) 
    {
        $zigbeeDevices = \resident\model\getZigbeeDevicesByDeviceUUID($device['UUID']);
        if (!empty($zigbeeDevices)) 
        {
            return 1;
        }
    }
    
    return 0;
} 

