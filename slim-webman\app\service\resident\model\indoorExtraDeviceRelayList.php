<?php

namespace resident\model;

use PDO;

function getExtraDeviceRelayListsByDeviceUUID($deviceUUID)
{
    $db = \util\container\getDb();
    
    $relayListSql = "SELECT UUID, Name, Function, Switch as EnableSwitch, CreateTime 
                     FROM ExtraDeviceRelayList 
                     WHERE ExtraDeviceUUID = :deviceUUID";
    $relayListStmt = $db->prepare($relayListSql);
    $relayListStmt->bindParam(":deviceUUID", $deviceUUID, PDO::PARAM_STR);
    $relayListStmt->execute();
    $relayLists = $relayListStmt->fetchAll(PDO::FETCH_ASSOC);
    
    return $relayLists;
} 