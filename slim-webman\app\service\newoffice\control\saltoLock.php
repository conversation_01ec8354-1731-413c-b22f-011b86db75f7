<?php

namespace newoffice\control\salto;

require_once __DIR__ . '/../../common/model/saltoLock.php';
require_once __DIR__ . '/../../common/model/devices.php';
require_once __DIR__ . '/../../common/model/personalDevices.php';
require_once __DIR__ . '/../../common/model/accessGroupDevices.php';
require_once __DIR__ . '/../../common/model/thirdPartyLockCapture.php';
require_once __DIR__ . '/../../resident/model/communityInfo.php';
require_once __DIR__ . '/../../common/control/saltoLock.php';

function getSaltoLockList($deviceInfoArray)
{
    $saltoLockList = getNewOfficeEnduserSaltoLockList($deviceInfoArray);

    //  link的设备默认在线,兼容ios处理
    \common\control\salto\transferSaltoLinkLockStatus($saltoLockList);
    return $saltoLockList;
}

// 新办公账号下发pub/unit所有有权限的lock
function getNewOfficeEnduserSaltoLockList($deviceInfoArray)
{
    $allLockList = array();
    $userConf = \util\container\getUserData();
    $allLockList = \common\model\getSaltoLockListByAccountUUID($userConf['MngUUID']);

    if (empty($allLockList)) {
        return [];
    }

    // 获取有权限的lock列表
    $accessLockList =  \newoffice\control\salto\getNewOfficeAccessLockList($allLockList, $deviceInfoArray);
    // 适配app api数据格式
    return  \common\control\salto\adaptSaltoApiLockList($accessLockList);
}

function getNewOfficeAccessLockList($allLockList, $deviceInfoArray)
{
    $accessDeviceInfo = array();
    foreach ($deviceInfoArray as $device) {
        $accessDeviceInfo[$device['uuid']]["mac"] = $device['mac'];
        $accessDeviceInfo[$device['uuid']]["Relay"] = \util\common\relayInfo2RelayInt($device['relay']);
    }

    // 获取有权限的设备关联的lock列表
    $accessLockList = array();
    foreach ($allLockList as $lock) {
        $ak_device_uuid = $lock['DeviceUUID'];

        // 没link设备的lock不展示
        if (empty($ak_device_uuid)) {
            \util\log\akcsLog::debug('salto not bind ak device, ThirdUUID=' . $lock['ThirdUUID']);
            continue;
        }

        // 判断mac是否有权限
        if (!array_key_exists($ak_device_uuid, $accessDeviceInfo)) {
            continue;
        }

        // 判断relay是否有权限
        $bindRelay = intval($lock['Relay']);
        $accessRelay = intval($accessDeviceInfo[$ak_device_uuid]['Relay']);
        if (\util\common\checkRelay($accessRelay, $bindRelay)) {
            $lock['mac'] = $accessDeviceInfo[$ak_device_uuid]['mac'];
            array_push($accessLockList, $lock);
        } else {
            \util\log\akcsLog::debug("salto bind relay has no access, bindRelay = $bindRelay, accessRelay = $accessRelay");
        }
    }

    return $accessLockList;
}

function openSaltoDoor($userConf, $thirdLockUUID)
{

    $linkMAC = "";
    $isJson = true;
    $heaader = array();
    $lockInfo = \common\model\getSaltoLockInfo($thirdLockUUID);
    if (isset($lockInfo['DeviceUUID'])) {
        $deviceInfo = \common\model\getDevicesInfoByUUID($lockInfo['DeviceUUID']);
        $linkMAC = $deviceInfo['MAC'];
    }

    $data = array();
    $data['link_mac'] = $linkMAC;
    $data['uuid'] = $thirdLockUUID;
    $data['initiator'] = $userConf['Name'];
    $data['lock_name'] = $lockInfo["Name"];
    $data['lock_type'] = SALTO_LOCK_TYPE;
    $data['personal_account_uuid'] = $userConf['UUID'];
    $data['message_type'] = LINKER_MSG_TYPE_SALTO_OPEN_DOOR;
    $data['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR;

    // 请求到cslinke进行开锁
    $response = \util\common\httpRequest(
        "post",
        "http://" . CSLINKER_HTTP_SERVER . '/open_salto_lock',
        $heaader,
        $data,
        $isJson,
        20
    );

    // 处理返回结果
    $json_data = json_decode($response, true);
    if (empty($json_data) || !isset($json_data['code']) || !isset($json_data['data']) || !isset($json_data['data']['err_code'])) {
        $result = ERR_CODE_OPEN_DOOR_FAILED;
    } elseif ($json_data['code'] == 0 && $json_data['data']['err_code'] == \common\model\FAILED_TYPE_SUCCESS) {
        $result = ERR_CODE_SUCCESS;
    } elseif ($json_data['data']['err_code'] == \common\model\FAILED_TYPE_SALTO_TOKEN_EXPIRED) {
        // ins账号上的salto IQ token过期，返回指定错误码给APP提示错误信息
        $result = ERR_CODE_SALTO_CODE_EXPIRED;
    } else {
        $result = ERR_CODE_OPEN_DOOR_FAILED;
    }

    \util\log\akcsLog::debug('salto open door. response=' . $response);
    return $result;
}

// 获取锁的实时状态
function getSaltoLockRealTimeStatus($userConf, &$saltoLockList)
{
    $saltoLockList = array();

    /* 
    if (!\common\model\getSaltoIQInfo($userConf['MngUUID'])) {
        return;
    }

    // 获取锁列表
    $saltoLockList = \newoffice\control\salto\getSaltoLockList();

    // 提取锁的uuid, 拼接成逗号分隔的字符串
    $thirdUUIDString = "";
    foreach ($saltoLockList as $lock) {
        if (isset($lock['uuid'])) {
            $thirdUUIDString .= $lock['uuid'] . ',';
        }
    }

    $thirdUUIDString = rtrim($thirdUUIDString, ',');
    if (empty($thirdUUIDString)) {
        return;
    }

    // 请求锁实时状态
    $output = \util\common\httpRequest(
        'get',
        'http://' . CSLINKER_HTTP_SERVER . '/salto_lock_status?thirdUUID=' . $thirdUUIDString,
        $heaader = []
    );

    // 更新锁的状态
    $realTimeLockStatus = json_decode($output, true);
    if (isset($realTimeLockStatus['data']['list'])) {
        $realTimeLockStatusList = $realTimeLockStatus['data']['list'];

        // 创建一个映射以便于快速查找
        $uuidToConnectedMap = [];
        foreach ($realTimeLockStatusList as $lockInfo) {
            if (isset($lockInfo['thirdUUID']) && isset($lockInfo['connected'])) {
                $uuidToConnectedMap[$lockInfo['thirdUUID']] = $lockInfo['connected'] ? '1' : '0';
            }
        }

        // 更新 saltoLockList 中的 connected 值
        foreach ($saltoLockList as &$lock) {
            if (isset($lock['uuid']) && isset($uuidToConnectedMap[$lock['uuid']])) {
                $lock['connected'] = $uuidToConnectedMap[$lock['uuid']];
            }
        }
        unset($lock); // 释放引用
    }
    */
}
