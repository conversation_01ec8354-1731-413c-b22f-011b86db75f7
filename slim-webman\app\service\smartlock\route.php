<?php
require_once __DIR__ . "/../smartlock/control/lockReport.php";
require_once __DIR__ . "/../smartlock/control/reportPropertie.php";
require_once __DIR__ . "/../smartlock/control/serverlist.php";
require_once __DIR__ . "/../smartlock/control/credentialList.php";
require_once __DIR__ . "/../smartlock/control/checkDevices.php";

$gApp->setProjectType(PROJECT_TYPE_COMMON);


$gApp->post('/api/global-link-entry/v1.0/invoke/global-link-entry/method/lock-report', function ($request, $response) {
    $response = \smartlock\control\lockReport($request, $response);
    return $response;
});

$gApp->get('/api/global-link-entry/v1.0/invoke/global-link-entry/method/serverlist', function ($request, $response) {
    $response = \smartlock\control\serverlist($request, $response);
    return $response;
});

//重启跟第一次上云上报所有状态
$gApp->post('/api/link-entry/v1.0/invoke/link-entry/method/properties', function ($request, $response) {
    $response = \smartlock\control\lockReportPropertie($request, $response);
    return $response;
});

$gApp->post('/api/global-link-entry/v1.0/invoke/global-link-entry/method/credential-list', function ($request, $response) {
    $response = \smartlock\control\getCredentialList($request, $response);
    return $response;
});

$gApp->post('/api/link-entry/v1.0/invoke/link-entry/method/check-devices', function ($request, $response) {
    $response = \smartlock\control\checkDevices($request, $response);
    return $response;
});

$gApp->post('/api/link-entry/v1.0/invoke/link-entry/method/events', function ($request, $response) {
    //没有用到目前
    $message = array();
    $response_data = [
        'success' => true,
        'timestamp' => time(),
        'result' => $message,
    ];
    $response = json($response_data);
    return $response;
});