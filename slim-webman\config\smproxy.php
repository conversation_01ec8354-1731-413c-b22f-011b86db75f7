<?php

/**
 * SMProxy配置文件
 * 
 * 配置SMProxy数据库中间件的连接参数和行为
 */

return [
    // SMProxy服务配置
    'enabled' => env('SMPROXY_ENABLED', false),
    'host' => env('SMPROXY_HOST', '127.0.0.1'),
    'port' => env('SMPROXY_PORT', 3366),
    'username' => env('SMPROXY_USERNAME', 'root'),
    'password' => env('SMPROXY_PASSWORD', '123456'),
    'charset' => env('SMPROXY_CHARSET', 'utf8mb4'),
    'timeout' => env('SMPROXY_TIMEOUT', 5),
    
    // 健康检查配置
    'health_check' => [
        'enabled' => true,
        'interval' => 30,           // 健康检查间隔(秒)
        'timeout' => 5,             // 健康检查超时(秒)
        'max_failures' => 3,        // 最大失败次数
        'recovery_interval' => 60,  // 故障恢复检查间隔(秒)
    ],
    
    // 故障转移配置
    'failover' => [
        'enabled' => true,
        'fallback_to_direct' => true,   // 故障时是否降级到直连MySQL
        'retry_times' => 3,             // 重试次数
        'retry_interval' => 5,          // 重试间隔(秒)
        'circuit_breaker' => [
            'enabled' => true,
            'failure_threshold' => 5,    // 失败阈值
            'recovery_timeout' => 300,   // 恢复超时(秒)
        ],
    ],
    
    // 监控配置
    'monitoring' => [
        'enabled' => true,
        'stats_interval' => 300,        // 统计信息收集间隔(秒)
        'performance_test' => [
            'enabled' => false,
            'test_count' => 10,         // 性能测试次数
            'test_interval' => 3600,    // 性能测试间隔(秒)
        ],
        'alert_thresholds' => [
            'connection_utilization_percent' => 80,  // 连接使用率告警阈值
            'response_time_ms' => 1000,              // 响应时间告警阈值
            'error_rate_percent' => 5,               // 错误率告警阈值
        ],
    ],
    
    // 日志配置
    'logging' => [
        'enabled' => true,
        'level' => 'info',              // debug, info, warning, error
        'log_queries' => false,         // 是否记录SQL查询
        'log_slow_queries' => true,     // 是否记录慢查询
        'slow_query_threshold' => 1000, // 慢查询阈值(毫秒)
    ],
    
    // 连接池配置（用于直连MySQL的降级方案）
    'fallback_pool' => [
        'enabled' => true,
        'min_connections' => 3,
        'max_connections' => 10,
        'connection_timeout' => 30,
        'idle_timeout' => 300,
    ],
    
    // 数据库配置（用于SMProxy和降级）
    'databases' => [
        'default' => [
            'smproxy' => [
                'host' => env('SMPROXY_HOST', '127.0.0.1'),
                'port' => env('SMPROXY_PORT', 3366),
                'database' => env('DB_DATABASE', 'akcs_db'),
                'username' => env('SMPROXY_USERNAME', 'root'),
                'password' => env('SMPROXY_PASSWORD', '123456'),
                'charset' => env('SMPROXY_CHARSET', 'utf8mb4'),
            ],
            'direct' => [
                'host' => env('DB_HOST', '127.0.0.1'),
                'port' => env('DB_PORT', 3306),
                'database' => env('DB_DATABASE', 'akcs_db'),
                'username' => env('DB_USERNAME', 'root'),
                'password' => env('DB_PASSWORD', '123456'),
                'charset' => env('DB_CHARSET', 'utf8mb4'),
            ],
        ],
    ],
    
    // 环境特定配置
    'environments' => [
        'development' => [
            'enabled' => false,
            'logging' => [
                'level' => 'debug',
                'log_queries' => true,
            ],
            'monitoring' => [
                'performance_test' => [
                    'enabled' => true,
                    'test_interval' => 1800,
                ],
            ],
        ],
        'testing' => [
            'enabled' => true,
            'health_check' => [
                'interval' => 10,
            ],
            'monitoring' => [
                'performance_test' => [
                    'enabled' => true,
                    'test_count' => 5,
                ],
            ],
        ],
        'production' => [
            'enabled' => true,
            'logging' => [
                'level' => 'warning',
                'log_queries' => false,
            ],
            'monitoring' => [
                'performance_test' => [
                    'enabled' => false,
                ],
            ],
            'failover' => [
                'circuit_breaker' => [
                    'failure_threshold' => 3,
                    'recovery_timeout' => 600,
                ],
            ],
        ],
    ],
];
