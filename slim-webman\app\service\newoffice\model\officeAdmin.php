<?php

namespace newoffice\model;

use PDO;

function updateAdminCalltype($userUUID, $calltype)
{
    $db = \util\container\getDb();

    $db->beginTransaction();

    $sthRole = $db->prepare("update OfficeAdmin set CallType=:Calltype WHERE PersonalAccountUUID = :PersonalAccountUUID");
    $sthRole->bindParam(':PersonalAccountUUID', $userUUID, PDO::PARAM_STR);
    $sthRole->bindParam(':Calltype', $calltype, PDO::PARAM_STR);
    $sthRole->execute();

    $db->commit();
}

function getAdminCalltypeByPersonalAccountUUID($personalAccountUUID)
{       
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT CallType FROM OfficeAdmin WHERE PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    if(array_key_exists("CallType", $result) == false) {
        $result["CallType"] = 0;
    }

    return $result;
}

function getOfficeAdminByAccountUUID($accountUUID)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT PhoneCode, PersonalAccountUUID FROM OfficeAdmin WHERE AccountUUID = :AccountUUID");
    $sth->bindParam(':AccountUUID', $accountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    return $result;
}

function getAdminAppStatusByPerUUID($perUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select AppStatus FROM OfficeAdmin where PersonalAccountUUID = :perUUID");
    $sth->bindParam(':perUUID', $perUUID, PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    return $ret['AppStatus'];
}

function getAdminIsEnableLandLineByUUID($PersonalAccountUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT IsEnableLandLine,AppStatus FROM OfficeAdmin WHERE PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $PersonalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    return $result['IsEnableLandLine'];
}
