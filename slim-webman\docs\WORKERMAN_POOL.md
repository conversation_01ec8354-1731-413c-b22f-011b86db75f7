# Workerman Pool 连接池文档

## 概述

本项目已升级为使用Workerman官方的Pool连接池实现，提供更稳定、更高效的数据库和Redis连接管理。

## 特性

- **官方支持**: 基于Workerman官方Pool类实现
- **协程友好**: 自动管理协程上下文，同一协程使用同一连接
- **自动归还**: 协程结束时自动归还连接到池中
- **健康检查**: 内置连接健康检查和心跳机制
- **降级机制**: 连接池不可用时自动降级到直连方式
- **统计监控**: 提供详细的连接池统计信息

## 架构设计

```
应用层
    ↓
Workerman Pool (协程上下文管理)
    ↓
MySQL/Redis 连接池
    ↓
数据库/Redis 服务器
```

## 核心组件

### 1. Db类 (`app/util/Db.php`)

基于Workerman官方文档实现的数据库类，支持自动连接管理：

```php
// 直接使用PDO方法
$result = \util\db\Db::query('SELECT * FROM users')->fetchAll();

// 获取Medoo实例
$medoo = \util\db\Db::medoo();
$users = $medoo->select('users', '*');

// 获取PDO实例
$pdo = \util\db\Db::pdo();
```

### 2. MySQLConnectionPool类 (`app/util/MySQLConnectionPool.php`)

基于Workerman Pool的MySQL连接池：

```php
$pool = \util\mysql\MySQLConnectionPool::getInstance();
$connection = $pool->getConnection(); // 自动管理协程上下文
```

### 3. RedisConnectionPool类 (`app/util/RedisConnectionPool.php`)

基于Workerman Pool的Redis连接池：

```php
$pool = \util\redis\RedisConnectionPool::getInstance();
$redis = $pool->getConnection(); // 自动管理协程上下文
```

## 配置参数

### MySQL连接池配置

```php
[
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'akcs_db',
    'username' => 'root',
    'password' => '123456',
    'charset' => 'utf8mb4',
    'max_connections' => 20,        // 最大连接数
    'min_connections' => 5,         // 最小连接数
    'idle_timeout' => 300,          // 空闲超时(秒)
    'heartbeat_interval' => 60,     // 心跳间隔(秒)
    'wait_timeout' => 10,           // 获取连接等待超时(秒)
]
```

### Redis连接池配置

```php
[
    'host' => '127.0.0.1',
    'port' => 6379,
    'password' => '',
    'database' => 0,
    'timeout' => 5,
    'max_connections' => 20,
    'min_connections' => 5,
    'idle_timeout' => 300,
    'heartbeat_interval' => 60,
    'wait_timeout' => 10,
]
```

## 使用方法

### 1. 基本使用（推荐）

使用Db类，自动管理连接：

```php
// PDO查询
$users = \util\db\Db::query('SELECT * FROM users WHERE id = ?', [1])->fetchAll();

// Medoo查询
$medoo = \util\db\Db::medoo();
$users = $medoo->select('users', '*', ['status' => 1]);

// 事务支持
$pdo = \util\db\Db::pdo();
$pdo->beginTransaction();
try {
    $pdo->exec("INSERT INTO users (name) VALUES ('test')");
    $pdo->commit();
} catch (Exception $e) {
    $pdo->rollback();
    throw $e;
}
```

### 2. 容器方式（兼容现有代码）

现有代码无需修改，自动使用连接池：

```php
// 自动使用Workerman Pool
$db = \util\container\getDb();
$medoo = \util\container\medooDb();
$redis = \util\container\getRedis();
```

### 3. 直接使用连接池

```php
// MySQL连接池
$mysqlPool = \util\mysql\MySQLConnectionPool::getInstance();
$connection = $mysqlPool->getConnection();
$pdo = $connection['pdo'];
$medoo = $connection['medoo'];

// Redis连接池
$redisPool = \util\redis\RedisConnectionPool::getInstance();
$redis = $redisPool->getConnection();
```

## 协程上下文管理

Workerman Pool自动管理协程上下文：

1. **同一协程复用连接**: 同一个协程内多次调用使用同一个连接
2. **自动归还**: 协程结束时自动归还连接到池中
3. **隔离性**: 不同协程使用不同的连接，避免数据混乱

```php
// 协程内的多次调用使用同一个连接
$result1 = \util\db\Db::query('SELECT 1')->fetch();
$result2 = \util\db\Db::query('SELECT 2')->fetch();
// 协程结束时自动归还连接
```

## 监控和统计

### 获取连接池统计信息

```php
// 通过Db类获取
$stats = \util\db\Db::getStats();

// 通过连接池实例获取
$mysqlPool = \util\mysql\MySQLConnectionPool::getInstance();
$stats = $mysqlPool->getStats();

// 通过Bootstrap获取
$stats = \support\bootstrap\DatabasePool::getStats();
```

### 统计信息示例

```json
{
  "mysql": {
    "total_connections": 8,
    "max_connections": 20,
    "min_connections": 5,
    "config": {...}
  },
  "redis": {
    "total_connections": 5,
    "max_connections": 20,
    "min_connections": 5,
    "config": {...}
  }
}
```

## 健康检查

连接池内置健康检查机制：

- **心跳检测**: 定期检查连接是否正常
- **自动重连**: 检测到连接异常时自动重新创建
- **故障隔离**: 异常连接会被移除出池

```php
// 检查连接池健康状态
$isHealthy = $mysqlPool->isHealthy();
$isHealthy = $redisPool->isHealthy();
```

## 故障转移

当Workerman Pool不可用时，系统会自动降级：

1. **Workerman Pool连接失败** → 降级到直连方式
2. **记录降级日志** → 便于问题排查
3. **保持服务可用** → 确保业务不中断

## 性能优化

### 连接池参数调优

- **max_connections**: 根据并发量调整，通常为CPU核数的2-4倍
- **min_connections**: 保持一定数量的预热连接
- **idle_timeout**: 根据业务特点调整空闲超时
- **heartbeat_interval**: 平衡检查频率和性能开销

### 最佳实践

1. **合理设置连接数**: 避免过多连接占用资源
2. **监控连接使用率**: 及时调整连接池参数
3. **使用事务**: 确保数据一致性
4. **避免长时间占用连接**: 及时释放连接资源

## 测试

### 运行测试

```bash
# Workerman Pool功能测试
php tests/WorkermanPoolTest.php
```

### 测试内容

- Db类功能测试
- MySQL连接池测试
- Redis连接池测试
- 并发性能测试
- 容器集成测试

## 故障排除

### 常见问题

1. **连接池初始化失败**
   - 检查数据库/Redis服务是否可访问
   - 确认配置参数是否正确
   - 查看错误日志

2. **获取连接超时**
   - 检查连接池配置的wait_timeout
   - 确认max_connections是否足够
   - 检查是否有连接泄漏

3. **连接健康检查失败**
   - 检查网络连接
   - 确认数据库/Redis服务状态
   - 调整heartbeat_interval参数

### 日志查看

```bash
# 查看连接池日志
tail -f logs/webman.log | grep "Pool\|Db\|Connection"
```

## 与传统方式对比

| 特性 | Workerman Pool | 传统直连 |
|------|----------------|----------|
| 连接复用 | ✅ | ❌ |
| 协程安全 | ✅ | ❌ |
| 自动管理 | ✅ | ❌ |
| 健康检查 | ✅ | ❌ |
| 性能开销 | 低 | 无 |
| 并发支持 | 高 | 低 |
| 资源利用 | 高 | 低 |

## 升级说明

从之前的自定义连接池升级到Workerman Pool：

1. **API兼容**: 现有代码无需修改
2. **性能提升**: 更高效的连接管理
3. **稳定性增强**: 官方支持，更可靠
4. **功能增强**: 协程上下文自动管理

---

**注意**: Workerman Pool需要Workerman 5.1.0+版本支持。在不支持的环境中会自动降级到直连方式。
