<?php

require_once(dirname(__FILE__) . '/kafka.php');
require_once(dirname(__FILE__) . '/socket.php');
require_once(dirname(__FILE__) . '/../service/common/model/personalAccount.php');

/* php <-->csmain的消息枚举 */
const MSG_P2A = 0x00400000;

/** 当界面上增加设备时通知csmain,csmain会将消息ID修改并透传给csmain, PHP <-->csmain
 */
const MSG_P2A_ADD_DEV = MSG_P2A + 1;

/** 当界面上修改设备时通知csmain,csmain会将消息ID修改并透传给csmain, PHP <-->csmain
 */
const MSG_P2A_MODIFY_DEV = MSG_P2A + 2;

/** 当界面上修改设备时通知csmain,csmain会将消息ID修改并透传给csmain, PHP <-->csmain
 */
const MSG_P2A_UPDATE_TO_DEVICE = MSG_P2A + 3;

const MSG_P2A_REBOOT_TO_DEVICE = MSG_P2A + 4;

/** 当界面上请求远程配置设备时通知csmain,csmain会将消息ID修改并透传给csmain, PHP <-->csmain
 */
const MSG_P2A_CONFIGURE_TO_DEVICE = MSG_P2A + 5;

/** 当界面上请求远程设备的配置信息时通知csmain,csmain会将消息ID修改并透传给csmain, PHP <-->csmain
 */
const MSG_P2A_CONFIGURE_FROM_DEVICE = MSG_P2A + 6;
//added by chenyc 2017-05-03,多用户功能开发
/** 当界面上添加用户时通知csadapt, PHP <-->csadapt
 */
const MSG_P2A_ADD_USER = MSG_P2A + 7;

/** 当界面上删除用户时通知csadapt, PHP <-->csadapt
 */
const MSG_P2A_DEL_USER = MSG_P2A + 8;

/** 当界面上修改app的配置文件时通知csadapt, PHP <-->csadapt
 */
const MSG_P2A_MODIFY_APP = MSG_P2A + 9;

/** 当app通过rest api绑定设备成功时通知csadapt, PHP <-->csadapt
 */
const MSG_P2A_BIND_APP = MSG_P2A + 10;
//v4.2
const MSG_P2A_PERSONNAL_UPDATE_NODE_DEV = MSG_P2A + 6;

const MSG_P2A_PERSONNAL_ALARM_DEAL = MSG_P2A + 7;

//社区更新设备操作
const MSG_P2A_COMMUNITY_UPDATE_NODE_DEV = MSG_P2A + 16;

const MSG_P2A_COMMUNITY_ALARM_DEAL = MSG_P2A + 18;

const MSG_P2A_COMMUNITY_NEW_TEXT_MESSAGE = MSG_P2A + 26;

//added by chenyc,2018-09-25,v4.2 app上删除视频
// 用户实时删除视频存储
const MSG_P2A_NOTIFY_DEL_VIDEO_STORAGE = MSG_P2A + 34;
// 远程开门
const MSG_P2A_NOTIFY_REMOTE_OPENDOOR = MSG_P2A + 38;
//远程开门 开Security Relay
const MSG_P2A_NOTIFY_REMOTE_OPEN_SECURITY_RELAY = MSG_P2A + 43;
//发送短信验证码
const MSG_P2A_SEND_SMS_CODE = MSG_P2A + 115;
//发送短信验证码
const MSG_P2A_APP_FEEDBACK = MSG_P2A + 117;

//新社区用户
const MSG_P2A_NOTIFY_COMMUNITY_ACCOUNT_MODIFY = MSG_P2A + 210;

//新版本更新installer下的用户或设备配置
const MSG_P2A_NOTIFY_PERSONAL_MESSAGE = MSG_P2A + 1000;
//新版本更新社区下的用户或设备配置或社区信息
const MSG_P2A_NOTIFY_COMMUNITY_MESSAGE = MSG_P2A + 1001;

const MSG_P2A_NOTIFY_OPERATE_THIRD_PARTYA_LOCK = MSG_P2A + 1008;

// 发送邮件
const MSG_P2A_SEND_EMAIL_NOTIFY =  MSG_P2A + 1019;

const MSG_P2A_NOTIFY_SMARTLOCK_UPDATE = MSG_P2A + 1024;

// SL50 http 上行消息路由
const MSG_P2A_SMARTLOCK_HTTP_UP_MSG_ROUTE = MSG_P2A + 1025;

// SL50开锁
const MSG_P2A_SMARTLOCK_UNLOCK_SL50 = MSG_P2A + 1026;

// SL50锁定
const MSG_P2A_SMARTLOCK_LOCK_SL50 = MSG_P2A + 1027;

//app上删除视频，web上有同样功能的接口
function delVideoNotify($vid)
{
    $data[] = $vid;
    $delVideoSocket = new CDelVideoNotifySocket();
    $delVideoSocket->setMsgID(MSG_P2A_NOTIFY_DEL_VIDEO_STORAGE);
    $delVideoSocket->copy($data);
}

//opendoor
//mac   uid
function openDoorNotify($project_type, $mac, $relay, $uid, $repost, $trace_id)
{
    $data[] = $mac;
    $data[] = $uid;
    $data[] = intval($relay);
    $data[] = intval($repost);
    $data[] = $trace_id;
    $openDoorSocket = new COpenDoorNotifySocket();
    $openDoorSocket->setMsgID(MSG_P2A_NOTIFY_REMOTE_OPENDOOR);
    $openDoorSocket->setMsgProjectType($project_type);
    $openDoorSocket->copy($data);   
}

function openSecurityRelayNotify($project_type,$mac, $security_relay, $uid, $repost, $trace_id)
{
    $data[] = $mac;
    $data[] = $uid;
    $data[] = $security_relay;
    $data[] = $trace_id;
    $data[] = (int)$repost;
    $openDoorSocket = new COpenSecurityRelayNotifySocket();
    $openDoorSocket->setMsgID(MSG_P2A_NOTIFY_REMOTE_OPEN_SECURITY_RELAY);
    $openDoorSocket->setMsgProjectType($project_type);
    $openDoorSocket->copy($data);
}

function webPersonalModifyNotify($changeType, $node = "", $mac = "", $insatllID = 0)
{
    \util\log\akcsLog::debug("[webPersonalModifyNotify]changeType=[" . $changeType . "] node=[" . $node . "] mac=[" . $mac . "] insatllID=" . $insatllID);
    $data[0] = $changeType;
    $data[1] = $node;
    $data[2] = $mac;
    $data[3] = $insatllID;

    $Socket = new CWebPersonalModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_PERSONAL_MESSAGE);
    $Socket->copy($data);
}

function webCommunityModifyNotify($changeType, $node = "", $mac = "", $communitid = 0, $unitid = 0)
{
    \util\log\akcsLog::debug("[webCommunityModifyNotify]changeType=[" . $changeType . "] node=[" . $node . "] mac=[" . $mac . "] communityid=[" . $communitid . "] unitid=" . $unitid);
    $data[0] = $changeType;
    $data[1] = $node;
    $data[2] = $mac;
    $data[3] = $communitid;
    $data[4] = $unitid;

    $Socket = new CWebCommunityModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);
    $Socket->copy($data);
}

function AlarmDealNotify($node, $user, $id, $result)
{
    \util\log\akcsLog::debug("[AlarmDealNotify]node=[" . $node . "] user=[" . $user . "] id=[" . $id . "] result=" . $result);
    $data[] = $node;
    $data[] = $user;
    $data[] = $id;
    $data[] = $result;
    $alarmDealSocket = new CAlarmDealSocket();
    $alarmDealSocket->setMsgID(MSG_P2A_PERSONNAL_ALARM_DEAL);
    $alarmDealSocket->copy($data);
}

function communityAlarmDealNotify($node, $user, $id, $result)
{
    \util\log\akcsLog::debug("[communityAlarmDealNotify]node=[" . $node . "] user=[" . $user . "] id=[" . $id . "] result=" . $result);
    $data[] = $node;
    $data[] = $user;
    $data[] = $id;
    $data[] = $result;
    $alarmDealSocket = new CAlarmDealSocket();
    $alarmDealSocket->setMsgID(MSG_P2A_COMMUNITY_ALARM_DEAL);
    $alarmDealSocket->copy($data);
}

//type: 0-登录 1-忘记密码   code:六位数字验证码 areacode:区号   phone:手机
function sendVerificationCode($type, $code, $areacode, $phone, $userType)
{
    \util\log\akcsLog::debug('[sendVerificationCode] type={type}, code={code}, areacode={areacode}, phone={phone}, userType={userType}', ['type'=>$type, 'code'=>$code, 'areacode'=>$areacode, 'phone'=>$phone, 'userType'=>$userType]);

    $data[0] = $type;
    $data[1] = $code;
    $data[2] = $areacode;
    $data[3] = $phone;
    $data[4] = $userType;

    $Socket = new CSendSmsCodeSocket();
    $Socket->setMsgID(MSG_P2A_SEND_SMS_CODE);
    $Socket->copy($data);
}

function sendAppFeedbackEmail($emailInfo, $oem_name)
{
    //协议通用字段
    $datas['email'] = $emailInfo['email'];
    $datas['send_to_type'] = $emailInfo['send_to_type'];
    $datas['language'] = $emailInfo['language'];
    $datas['content'] = (string)$emailInfo['content'];
    $datas['contact_email'] = (string)$emailInfo['contact_email'];
    $datas['user'] = (string)$emailInfo['sip_account'];
    $datas['ins_account'] = (string)$emailInfo['ins_name'];
    $datas['file_list'] = (string)$emailInfo['file_list'];
    $datas['user_type'] = $emailInfo['user_type'];
    //适配cspush
    $datas['project_type'] = ($emailInfo['user_type'] == (USER_TYPE_OFFICE)? PROJECT_TYPE_OFFICE : PROJECT_TYPE_RESIDENCE);
    $datas['email_type'] = ($emailInfo['user_type'] == (USER_TYPE_OFFICE)? "office_app_feedback" : (string)$emailInfo['email_type']);
    //协议可选字段
    if($datas['user_type'] == USER_TYPE_RESIDENCE) {
        $datas['community_name'] = (string)$emailInfo['project_name'];
        $datas['building_name'] = (string)$emailInfo['building'];
        $datas['apt_name'] = (string)$emailInfo['apt'];
    } else if($datas['user_type'] == USER_TYPE_OFFICE) {
        $datas['office_name'] = (string)$emailInfo['project_name'];
        $datas['department_name'] = (string)$emailInfo['building'];
    } else if($datas['user_type'] == USER_TYPE_COMMUNITY_PM) {
        $datas['community_name'] = (string)$emailInfo['project_name'];        
    }
    //发给dis的邮件需要携带dis信息，用于邮件标题，ins的已存在ins_account中了
    if($datas['send_to_type'] == "dis") {
        $datas['dis_account'] = (string)$emailInfo['dis_name'];
    }
    //内部邮件需要设置抄送列表
    if($datas['send_to_type'] == "company") {
        $datas['cc_list'] = (string)$emailInfo['cc_list'];
    }

    \util\log\akcsLog::debug("[sendAppFeedbackEmail] : Send email = {email}", ['email' => $datas['email']]);

    $payload = ["ver" => "1",
                "OEM" => $oem_name,
                "app_type" => "email",
                "data" => json_encode($datas)
            ];

    $data[] = $emailInfo['email'];
    $data[] = json_encode($payload);

    $Socket = new CSendEmailNotifySocket();
    $Socket->setMsgID(MSG_P2A_SEND_EMAIL_NOTIFY);
    $Socket->copy($data);
}

function WebCommunityAccountModifyNotify($communitid, $Accounts, $AccessGroupIDs)
{
    $Accounts = is_array($Accounts) ? $Accounts : [$Accounts];
    $AccessGroupIDs = is_array($AccessGroupIDs) ? $AccessGroupIDs : [$AccessGroupIDs];

    $accounts_str = implode(",", $Accounts);
    $groupids_str = implode(",", $AccessGroupIDs);
    \util\log\akcsLog::debug("webCommunityPersonalModifyNotify: communitid=$communitid,Account=$accounts_str groupid=$groupids_str");

    $data[0] = $AccessGroupIDs;
    $data[1] = $communitid;
    $data[2] = $Accounts;
    $data[3] = [];//nodes

    \common\model\updateAccountDataVersion($Accounts);

    $socket = new CWebCommunityAccountModifyNotifySocket();
    $socket->setMsgID(MSG_P2A_NOTIFY_COMMUNITY_ACCOUNT_MODIFY);
    $socket->copy($data);
}

function newTextMesage($account_id = 1)
{
    $data[] = "$account_id";
    \util\log\akcsLog::debug('[newTextMesage]begin to send personnal text message');
    $perNewMessageSocket = new CPerNewMessageSocket();
    $perNewMessageSocket->setMsgID(MSG_P2A_COMMUNITY_NEW_TEXT_MESSAGE);
    $perNewMessageSocket->copy($data);
}

function setMotionNotify($userconf)
{
    $mac = "";
    $insatllID = 0;
    $role = $userconf['Role'];
    $node = $userconf['Account'];
    $communitid = $userconf['MngID'];
    $unitid = $userconf['UnitID'];

    if ($role == ROLE_TYPE_PERSONNAL_MASTER) {  
        $changeType = APP_PER_MODIFY_MOTION_CONFIG;
        webPersonalModifyNotify($changeType, $node, $mac, $insatllID);
    } else if ($role == ROLE_TYPE_COMMUNITY_MASTER){
        $changeType = APP_COMM_MODIFY_NODE_MOTION_CONFIG;
        webCommunityModifyNotify($changeType, $node, $mac, $communitid, $unitid);
    }   
}

function openThirdPartyLockNotify($third_lock_data)
{
    $data[] = $third_lock_data['message_type'];
    $data[] = $third_lock_data['capture_type'];
    $data[] = $third_lock_data['lock_type'];
    $data[] = $third_lock_data['uuid'];
    $data[] = $third_lock_data['personal_account_uuid'];
    $data[] = $third_lock_data['initiator'];
    $data[] = $third_lock_data['lock_name'];
    $data[] = isset($third_lock_data['personal_account_uuid_for_operator']) ? $third_lock_data['personal_account_uuid_for_operator'] : '';

    $openDoorSocket = new CThirdPartyLockNotifySocket();
    $openDoorSocket->setMsgID(MSG_P2A_NOTIFY_OPERATE_THIRD_PARTYA_LOCK);
    $openDoorSocket->copy($data);
}

function triggerEmailChainNotify($msg)
{
    $emailInfo['msg'] = $msg;
    $emailInfo['email'] = TRIGGER_EMAIL;
    $emailInfo['email_type'] = "trigger_email_chain";

    $payload = ["ver" => "1",
                "app_type" => "email",
                "data" => json_encode($emailInfo)
            ];

    $data[] = $emailInfo['email'];
    $data[] = json_encode($payload);

    $sendEmailNotifySocket = new CSendEmailNotifySocket();
    $sendEmailNotifySocket->setMsgID(MSG_P2A_SEND_EMAIL_NOTIFY);
    $sendEmailNotifySocket->copy($data);
}

function triggerThirdpartyChainNotify($msg)
{
    $data = array(LINKER_MSG_TYPE_TRIGGER, 0, 0, "0", "0", $msg, "0");

    $openDoorSocket = new CThirdPartyLockNotifySocket();
    $openDoorSocket->setMsgID(MSG_P2A_NOTIFY_OPERATE_THIRD_PARTYA_LOCK);
    $openDoorSocket->copy($data);
}

function sendTwoFactorAuthCodeEmail($code, $email, $name, $language, $oem)
{
    $emailInfo['email'] = $email;
    $emailInfo['email_type'] = "two_factor_auth";
    $emailInfo['language'] = $language;
    $emailInfo['code'] = $code;
    $emailInfo['user'] = $name;

    $payload = ["ver" => "1",
                "app_type" =>"email",
                "OEM" => $oem,
                "data" => json_encode($emailInfo)
            ];
    $data[] = $emailInfo['email'];
    $data[] = json_encode($payload);

    $sendEmailNotifySocket = new CSendEmailNotifySocket();
    $sendEmailNotifySocket->setMsgID(MSG_P2A_SEND_EMAIL_NOTIFY);
    $sendEmailNotifySocket->copy($data);
}


function setDetectionNotify($userconf)
{
    $mac = "";
    $insatllID = 0;
    $role = $userconf['Role'];
    $node = $userconf['Account'];
    $communitid = $userconf['MngID'];
    $unitid = $userconf['UnitID'];

    if ($role == ROLE_TYPE_PERSONNAL_MASTER) {  
        $changeType = APP_PER_MODIFY_MOTION_CONFIG;
        webPersonalModifyNotify($changeType, $node, $mac, $insatllID);
    } else if ($role == ROLE_TYPE_COMMUNITY_MASTER) {
        $changeType = APP_COMM_MODIFY_NODE_DETECTION_CONFIG;
        webCommunityModifyNotify($changeType, $node, $mac, $communitid, $unitid);
    } elseif ($role == ROLE_TYPE_COMMUNITY_PM) {
        $changeType = APP_PM_MODIFY_COMM_DETECTION_CONFIG;
        webCommunityModifyNotify($changeType, $node, $mac, $communitid, $unitid);
    } 
}

function smartlockUpdateNotify($lockUUID, $lockType)
{
    if ($lockType == SMARTLOCK_MODEL_SL20 || $lockType == SMARTLOCK_MODEL_SL21) {
        $notifyLockType = NOTIFY_SMARTLOCK_TYPE_SL20;
    }
    else {
        \util\log\akcsLog::debug("[smartlockUpdateNotify] lockType is not supported");
        return;
    }

    $data[] = $lockUUID;
    $data[] = $notifyLockType;
    
    $socket = new CSmartlockUpdateNotifySocket();
    $socket->setMsgID(MSG_P2A_NOTIFY_SMARTLOCK_UPDATE);
    $socket->copy($data);
}

function smartlockHttpUpMessageRoute($clientID, $msg, $traceId)
{
    $data[] = $clientID;
    $data[] = $msg;
    $data[] = $traceId;
    
    $socket = new CSmartlockHttpUpRouteMessageSocket();
    $socket->setMsgID(MSG_P2A_SMARTLOCK_HTTP_UP_MSG_ROUTE);
    $socket->copy($data);
}

function unlockSL50SmartLockNotify($lockUUID, $traceID)
{
    $data[] = $lockUUID;
    $data[] = $traceID;
    
    $socket = new CUnlockSL50SmartLockSocket();
    $socket->setMsgID(MSG_P2A_SMARTLOCK_UNLOCK_SL50);
    $socket->copy($data);
}

function lockSL50SmartLockNotify($lockUUID, $traceID)
{
    $data[] = $lockUUID;
    $data[] = $traceID;
    
    $socket = new CLockSL50SmartLockSocket();
    $socket->setMsgID(MSG_P2A_SMARTLOCK_LOCK_SL50);
    $socket->copy($data);
}
