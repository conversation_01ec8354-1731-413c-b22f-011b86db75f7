<?php

namespace common\model;

const DB_DORMABAKALOCK_TABLE = "DormakabaLock";
const DB_DORMAKABALOCK_FIELD = ["Name", "ThirdUUID", "IsBind", "ProjectType", "Grade", "AccountUUID", "CommunityUnitUUID", "PersonalAccountUUID", "DeviceUUID", "Relay", "Version","UUID"];

// 获取位于社区public area的所有lock
function getCommunityPublicDormakabaLockList($projectUUID)
{
    return \util\container\medooDb()->select(DB_DORMABAKALOCK_TABLE, DB_DORMAKABALOCK_FIELD, ["AND" => ["AccountUUID" => $projectUUID, "Grade" => COMMUNITY_DEVICE_TYPE_PUBLIC]]);
}

// 获取位于社区unit area所有lock
function getCommunityUnitDormakabaLockList($projectUUID)
{
    return \util\container\medooDb()->select(DB_DORMABAKALOCK_TABLE, DB_DORMAKABALOCK_FIELD, ["AND" => ["AccountUUID" => $projectUUID, "Grade" => COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT]]);
}

// 获取家庭独占的所有lock
function getAptDormakabaLockList($nodeUUID)
{
    return \util\container\medooDb()->select(DB_DORMABAKALOCK_TABLE, DB_DORMAKABALOCK_FIELD, ["AND" => ["PersonalAccountUUID" => $nodeUUID, "Grade" => COMMUNITY_DEVICE_TYPE_PERSONAL]]);
}

// 获取lock信息
function getDormakabaLockInfo($thirdUUID)
{
    return \util\container\medooDb()->get(DB_DORMABAKALOCK_TABLE, DB_DORMAKABALOCK_FIELD, ["ThirdUUID" => $thirdUUID]);
}

function RelatedDormakabaLock($projectUUID)
{
    return \util\container\medooDb()->get(DB_DORMABAKALOCK_TABLE, DB_DORMAKABALOCK_FIELD, ["AccountUUID" => $projectUUID]);
}