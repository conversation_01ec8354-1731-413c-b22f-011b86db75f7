<?php

namespace common\model;

use PDO;

function getMessagesNumV65()
{
    //通过UserInfoUUID获取所有的site信息
    $userData = \util\container\getUserData();
    $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($userData['UserInfoUUID']);
    if(count($personalAccountList) == 0) {
        return 0;
    }
    $accountDataList = array_column($personalAccountList, 'Account');
    $sqlParam = \util\common\getImplodeSqlData($accountDataList);
    $bindStr = $sqlParam['bindString'];
    $bindParam = $sqlParam['bindArray'];
    $db =  \util\container\getDb();
    $messageNum = 0;

    $sth = $db->prepare("/*master*/ SELECT COUNT(*) AS num FROM MessageAccountList L LEFT JOIN Message M ON L.MessageID = M.ID WHERE L.Account IN ($bindStr) AND L.Status = 0 AND L.ClientType = 2 AND M.CreateTime > :createTime");
    foreach($bindParam as $key => $value) {
        $sth->bindValue($key,$value, PDO::PARAM_STR);//这里用bindParam会重复绑定
    }
    $sth->bindValue(':createTime', $userData['CreateTime'], PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    if ($ret) {
        $messageNum += $ret['num'];
    }

    //6.5兼容语音留言
    $uuidDataList = array_column($personalAccountList, 'UUID');
    $sqlParam = \util\common\getImplodeSqlData($uuidDataList);
    $bindStr = $sqlParam['bindString'];
    $bindParam = $sqlParam['bindArray'];
    $sth = $db->prepare("/*master*/ select count(*) as num from PersonalVoiceMsgList WHERE PersonalAccountUUID in ($bindStr) and Status = 0 and CreateTime > :createTime");
    foreach($bindParam as $key => $value) {
        $sth->bindValue($key,$value, PDO::PARAM_STR);
    }
    $sth->bindValue(':createTime', $userData['CreateTime'], PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $messageNum += $result['num'];
    }
    return intval($messageNum);
}
//6.7将Message状态置为已读
function setMessageRead($personalAccountList)
{
    if(count($personalAccountList) == 0) {
        return 0;
    }
    $userData = \util\container\getUserData();
    $db = \util\container\getDb();
    $accountDataList = array_column($personalAccountList, 'Account');
    $sqlParam = \util\common\getImplodeSqlData($accountDataList);
    $bindStr = $sqlParam['bindString'];
    $bindParam = $sqlParam['bindArray'];
    $sth = $db->prepare("update MessageAccountList L left join Message M on L.MessageID = M.ID set L.Status = 1 where L.Account in ($bindStr) and L.ClientType = 2 and L.Status = 0 and M.CreateTime > :createTime");
    foreach($bindParam as $key => $value) {
        $sth->bindValue($key, $value, PDO::PARAM_STR);
    }
    $sth->bindValue(':createTime', $userData['CreateTime'], PDO::PARAM_STR);
    if(!$sth->execute()) {
        \util\log\akcsLog::debug("update MessageAccountList failed");
        return -1;
    }

    $uuidDataList = array_column($personalAccountList, 'UUID');
    $sqlParam = \util\common\getImplodeSqlData($uuidDataList);
    $bindStr = $sqlParam['bindString'];
    $bindParam = $sqlParam['bindArray'];
    $sth = $db->prepare("update PersonalVoiceMsgList set Status = 1 WHERE PersonalAccountUUID in ($bindStr) and Status = 0 and CreateTime > :createTime");
    foreach($bindParam as $key => $value) {
        $sth->bindValue($key, $value, PDO::PARAM_STR);
    }
    $sth->bindValue(':createTime', $userData['CreateTime'], PDO::PARAM_STR);
    if(!$sth->execute()) {
        \util\log\akcsLog::debug("update PersonalVoiceMsgList failed");
        return -1;
    }

    return 0;
}
// 当前原生通过MessageAccountListID设置已读没有语音留言的场景
function setMessageReadByMessageAccountListID($messageAccountListID)
{
    if (!$messageAccountListID) {
        return -1;
    }
    $db = \util\container\getDb();
    
    $userData = \util\container\getUserData();

    $sth = $db->prepare("update MessageAccountList L left join Message M on L.MessageID = M.ID set L.Status = 1 where L.ID = :messageAccountListID and L.Status = 0 and L.ClientType = 2 and M.CreateTime > :createTime");
    $sth->bindValue(':createTime', $userData['CreateTime'], PDO::PARAM_STR);
    $sth->bindValue(':messageAccountListID', $messageAccountListID, PDO::PARAM_INT);
    if (!$sth->execute())
    {
        \util\log\akcsLog::debug("update MessageAccountList failed. messageAccountListID: $messageAccountListID");
        return -1;
    }

    return 0;
}

function addYaleBatteryMsg($lockName, $messageType, $personalAccountUUID)
{
    $db = \util\container\getDb();
    $db->beginTransaction();
    //低电量msg没有account，所以默认设置成super
    $sth = $db->prepare("INSERT INTO Message (Title,Content,Type,AccountID) VALUES ('Yale Battery Warning',:content,:type,1)");
    $sth->bindParam(':content', $lockName, PDO::PARAM_STR);
    $sth->bindParam(':type', $messageType, PDO::PARAM_INT);
    $sth->execute();
    $msgId = $db->lastInsertId();

    $sth = $db->prepare("select Account from PersonalAccount where ParentUUID = :uid or UUID = :uid");
    $sth->bindParam(':uid', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $family = $sth->fetchAll(PDO::FETCH_ASSOC);

    foreach ($family as $value) {
        //只发送给app，所以ClientType写死为2
        $sth = $db->prepare("INSERT INTO MessageAccountList (ClientType,Account,MessageID) VALUES (2,:account,:id)");
        $sth->bindParam(':account', $value['Account'], PDO::PARAM_STR);
        $sth->bindParam(':id', $msgId, PDO::PARAM_INT);
        $sth->execute();
    }
    $db->commit();
}

function getMessageAccountListIDByMainMessageID($messageID, $personal_account)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select ID from MessageAccountList where MessageID=:MessageID and Account=:Account and Status=0");
    $sth->bindParam(':Account', $personal_account, PDO::PARAM_STR);
    $sth->bindParam(':MessageID', $messageID, PDO::PARAM_INT);

    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    if ($ret) {
        return $ret['ID'];
    }
    return "";
}