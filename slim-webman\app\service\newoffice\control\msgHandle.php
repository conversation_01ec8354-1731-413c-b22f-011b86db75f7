<?php

namespace  newoffice\control;
require_once __DIR__ . "/../../common/model/officeMessageReceiver.php";

function setMsgRead($request, $response) 
{
    $bodyValue = $request->getParsedBody();
    $noticeSwitch = $bodyValue["notice"];

    $clientType = MESSAGE_RECEIVER_CLIENT_TYPE_APP;

    if($noticeSwitch) {
        if(!\common\model\setMessageReadV70($clientType)) {
            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        }
    } else if(!$noticeSwitch) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    }
    return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "set msg read failed");
}
