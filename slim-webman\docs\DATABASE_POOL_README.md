# MySQL连接池实现文档

## 概述

本项目实现了一个高效的MySQL连接池，用于优化数据库连接管理，提高应用程序性能和资源利用率。连接池基于webman框架，支持连接复用、健康检查、监控统计等功能。

## 主要特性

- **连接复用**: 避免频繁创建/销毁数据库连接
- **自动管理**: 支持最小/最大连接数控制
- **健康检查**: 定期检查连接有效性，自动重连
- **监控统计**: 提供详细的性能指标和使用统计
- **故障恢复**: 连接失败时自动重试和降级处理
- **向后兼容**: 保持现有代码接口不变

## 架构组件

### 1. 核心类

- **MySQLConnectionPool**: 连接池核心管理类
- **DatabasePool**: Bootstrap启动器，管理连接池生命周期
- **DatabasePoolMonitor**: 监控和统计组件
- **DatabasePoolController**: 管理API控制器

### 2. 配置文件

- **config/database_pool.php**: 连接池配置参数
- **config/bootstrap.php**: 启动器注册

### 3. 中间件集成

- **GlobalResourceMiddle**: 修改为使用连接池获取连接

## 安装和配置

### 1. 配置参数

编辑 `config/database_pool.php` 文件：

```php
return [
    'min_connections' => 5,        // 最小连接数
    'max_connections' => 20,       // 最大连接数
    'connection_timeout' => 30,    // 连接超时(秒)
    'idle_timeout' => 300,         // 空闲超时(秒)
    'health_check_interval' => 60, // 健康检查间隔(秒)
    'debug' => false,              // 调试模式
];
```

### 2. 环境配置

根据不同环境调整配置：

- **开发环境**: 较少连接数，开启调试
- **测试环境**: 中等连接数，开启调试
- **生产环境**: 较多连接数，关闭调试

### 3. 启动验证

确保 `config/bootstrap.php` 包含：

```php
return [
    support\bootstrap\Session::class,
    support\bootstrap\LaravelDb::class,
    support\bootstrap\DatabasePool::class,  // 连接池启动器
];
```

## 使用方法

### 1. 基本使用

现有代码无需修改，继续使用原有接口：

```php
// 获取PDO连接
$pdo = \util\container\getDb();

// 获取Medoo连接
$medoo = \util\container\medooDb();

// 正常使用数据库操作
$result = $medoo->select('table_name', ['field1', 'field2'], ['id' => 1]);
```

### 2. 直接使用连接池

如需直接操作连接池：

```php
use support\bootstrap\DatabasePool;

// 获取连接
$connection = DatabasePool::getConnection();

// 使用连接
$result = $connection['pdo']->query('SELECT 1')->fetch();

// 归还连接
DatabasePool::releaseConnection($connection);
```

### 3. 监控和统计

```php
use support\bootstrap\DatabasePool;
use util\monitor\DatabasePoolMonitor;

// 获取连接池状态
$stats = DatabasePool::getStats();

// 获取监控指标
$monitor = DatabasePoolMonitor::getInstance();
$metrics = $monitor->getMetrics();
```

## API接口

### 连接池管理API

- `GET /admin/database_pool/status` - 获取连接池状态
- `GET /admin/database_pool/metrics` - 获取监控指标
- `GET /admin/database_pool/info` - 获取详细信息
- `GET /admin/database_pool/test` - 测试数据库连接
- `GET /admin/database_pool/config` - 获取配置信息
- `POST /admin/database_pool/reset_metrics` - 重置监控指标

### 响应示例

```json
{
  "success": true,
  "data": {
    "healthy": true,
    "pool_stats": {
      "current_active": 2,
      "current_idle": 3,
      "current_total": 5,
      "peak_active": 8,
      "total_created": 15,
      "total_destroyed": 10
    },
    "monitoring_metrics": {
      "connection_requests": 1250,
      "connection_successes": 1248,
      "connection_failures": 2,
      "avg_wait_time": 12.5
    }
  }
}
```

## 测试

### 运行测试

```bash
cd slim-webman
php tests/DatabasePoolTest.php
```

### 测试内容

- 连接池初始化测试
- 连接获取和释放测试
- 并发访问测试
- 连接池耗尽测试
- 健康检查测试
- 监控功能测试

## 监控和告警

### 关键指标

- **连接池使用率**: 当前活跃连接数 / 总连接数
- **连接成功率**: 成功连接数 / 总请求数
- **平均等待时间**: 获取连接的平均等待时间
- **连接错误数**: 连接失败的次数

### 告警阈值

在配置文件中设置告警阈值：

```php
'monitoring' => [
    'alert_thresholds' => [
        'pool_utilization_percent' => 90,      // 连接池使用率告警
        'connection_errors_per_minute' => 10,  // 连接错误率告警
        'avg_wait_time_ms' => 1000,            // 平均等待时间告警
    ]
]
```

## 性能优化建议

### 1. 连接数配置

- **最小连接数**: 根据应用基础负载设置
- **最大连接数**: 考虑数据库服务器连接限制
- **建议比例**: 最大连接数 = 最小连接数 × 3-4

### 2. 超时配置

- **连接超时**: 通常设置为10-30秒
- **空闲超时**: 根据应用访问模式设置，建议5-10分钟
- **健康检查**: 建议1-2分钟间隔

### 3. 环境调优

- **开发环境**: min=2, max=10
- **测试环境**: min=3, max=15  
- **生产环境**: min=10, max=50

## 故障排除

### 常见问题

1. **连接池启动失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否可访问
   - 查看错误日志获取详细信息

2. **连接获取超时**
   - 检查连接池配置是否合理
   - 监控连接池使用情况
   - 考虑增加最大连接数

3. **连接泄漏**
   - 检查代码是否正确释放连接
   - 启用连接泄漏检测
   - 查看监控指标中的泄漏统计

### 日志查看

连接池相关日志会输出到系统错误日志中，可以通过以下方式查看：

```bash
# 查看连接池日志
tail -f /var/log/php/error.log | grep "DatabasePool\|MySQLConnectionPool"

# 查看监控日志
tail -f /var/log/php/error.log | grep "DatabasePoolMonitor"
```

## 升级和维护

### 版本兼容性

- 当前版本与现有代码完全兼容
- 如连接池不可用会自动降级到原有方式
- 支持热重启和平滑升级

### 维护建议

- 定期检查连接池状态和指标
- 根据业务增长调整连接数配置
- 监控数据库服务器连接数使用情况
- 定期更新和优化配置参数

## 技术支持

如遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志和错误信息
3. 使用API接口获取详细状态信息
4. 运行测试用例验证功能

---

**注意**: 连接池功能已集成到现有系统中，无需修改业务代码即可享受性能提升。
