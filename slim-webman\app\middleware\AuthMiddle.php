<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

require_once(__DIR__ . '/../service/common/model/personalAccount.php');
require_once(__DIR__ . '/../service/common/control/twoFactorAuthCode.php');
require_once(__DIR__ . '/../util/common.php');

class AuthMiddle implements MiddlewareInterface
{
    private static $authWhitelist = ['/send_mobile_checkcode',
     '/trigger_monitor_chain', 
     '/yale/webhook',
     "/api/global-link-entry/v1.0/invoke/global-link-entry/method/lock-report",
    "/api/link-entry/v1.0/invoke/link-entry/method/properties",
    "/api/global-link-entry/v1.0/invoke/global-link-entry/method/serverlist",
    "/api/global-link-entry/v1.0/invoke/global-link-entry/method/credential-list",
    "/api/link-entry/v1.0/invoke/link-entry/method/check-devices",
    "/api/link-entry/v1.0/invoke/link-entry/method/events"];

    private $initOptionslist = ['/third_party_lock/get_oauth_url', '/third_party_lock/auth_bind', '/tiandy_register_user'];

    /**
     * 检查URI是否在认证白名单中
     * 
     * @param string $uri 请求URI
     * @return bool 如果在白名单中返回true，否则返回false
     */
    public static function isAuthWhiteList($uri)
    {
        if (in_array($uri, self::$authWhitelist)) {
            return true;
        }

        return false;
    }
    public function process(Request $request, callable $next): Response
    {
        //特殊请求过滤
        if(\util\common\isSpecialReq($request))
        {
            return $next($request);
        }

        \util\log\akcsLog::request($request);
        \util\log\akcsLog::requestReg($request);

        //鉴权白名单
        $uri = $request->path();
        if (in_array($uri, self::$authWhitelist)) {
            \util\container\setUserData(array());
            $response = $next($request);
            return $response;
        }

        //发送双重认证校验码接口 鉴权
        //使用app请求带上登录生成的check_digit_code进行鉴权，如果check_digit_code和Redis记录的一样，则鉴权成功
        if ($uri == '/apprest/v2/send_two_factor_authentication_code') 
        {
            $authResult = \common\control\handleTwoFactorAuthentication($request, $next);
            if ($authResult == true) 
            {
                \util\container\setUserData(array());
                $response = $next($request);
                return $response;
            } 
            else 
            {
                $response = '';
                return \util\response\setResponseMessage($response, ERR_CODE_TWO_FACTOR_AUTH_TEMP_TOKEN_INVALID);
            }
        }

        if (in_array($uri, $this->initOptionslist)) {
            $method = $request->method();
            if ($method === "OPTIONS") {
                $response = new Response();
                $response = \util\response\setThirdLinkerResponseMessage($response,"success", 0);
                $response = \util\response\addCorsHeaders($response);
                return $response;
            }
        }
        

        //校验身份
        $result = \common\model\checkIdentity();
        if ($result == ERR_TOKEN_INVALID) {
            $response = '';
            return \util\response\setResponseMessage($response, ERR_CODE_TOKEN_INVALID);
        }
        if ($result == ERR_TOKEN_EXPIRE) {
            $response = '';
            return \util\response\setResponseMessage($response, ERR_CODE_TOKEN_EXPIRE);
        }

        //若带site，则进行权限判断，若属于自己的多套房，则进行切换
        //GET请求site放param中
        $site = '';
        $paramValue = $request->getQueryParams();
        if(array_key_exists('site', $paramValue)){
            $site = $paramValue['site'];
        }
        //POST请求site放body中
        $postDatas = $request->getParsedBody();
        if(array_key_exists('site', $postDatas)){
            $site = $postDatas['site'];
        }
        if($site) {
            $siteInfo = \common\model\getMultiSiteRoomInfo($result['UserInfoUUID']);
            foreach($siteInfo as $info) {
                //site可能是account也可能是node，后续csmain整改后 统一可改为account
                if($info['Account'] == $site || $info['Node'] == $site) {
                    $result['Role'] = $info['Role'];
                    $result['Account'] = $info['Account'];
                    $result['ProjectType'] = $info['ProjectType'];
                    \util\log\akcsLog::debug("param with site, account change to site {$site}");
                    break;
                }
            }
        }

        \util\container\setUserData($result);
        
        $response = $next($request);

        return $response;
    }
}

