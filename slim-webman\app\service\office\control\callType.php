<?php

namespace office\control;

require_once __DIR__ . "/../../../notify/notify_office.php";
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";

//setCallType 0=app 1=phone 2=app先 未接听后phone
function setOfficeCallType($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();
    $calltype = $postDatas['calltype'];

    \common\model\updateCalltype($userConf, $calltype);

    officeWebCommunityModifyNotify(WEB_COMM_NODE_UPDATE, $userConf['Account'], $mac = "", $userConf['MngID'], $userConf['UnitID']);

    $datas = [
        'calltype' => $calltype
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}