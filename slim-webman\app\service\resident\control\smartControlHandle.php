<?php

namespace resident\control;

use PDO;

require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/../model/communityInfo.php";
require_once __DIR__ . "/../model/indoorMonitorConfig.php";
require_once __DIR__ . "/../model/indoorExtraDeviceRelayAction.php";
require_once __DIR__ . "/smartControlZigbeeDevice.php";


/**
 * 处理单个设备的所有继电器信息
 */
function getDeviceRelaysInfo($device)
{
    $relaysInfo = [];
    
    // 处理本地继电器
    $localRelays = \resident\control\getLocalRelaysInfo($device);
    $relaysInfo = array_merge($relaysInfo, $localRelays);
    
    // 处理外接继电器
    $externRelays = \resident\control\getExternRelaysInfo($device);
    $relaysInfo = array_merge($relaysInfo, $externRelays);
    
    return $relaysInfo;
}

/**
 * 处理本地继电器 (Local relays)
 */
function getLocalRelaysInfo($device)
{
    $relaysInfo = [];
    $relayData = json_decode($device['Relay'], true);
    
    if (!is_array($relayData)) 
    {
        return $relaysInfo;
    }
    
    $relayStatus = \util\common\getRelayStatusFromFlags($device['Flags']);
    
    foreach($relayData as $index => $relay) 
    {
        $relayDataItem = \resident\control\buildLocalRelayDataItem($device, $index, $relay, $relayStatus);
        $relaysInfo[] = $relayDataItem;
    }
    
    return $relaysInfo;
}

function buildLocalRelayDataItem($device, $index, $relay, $relayStatus)
{
    $relayDataItem = [];
    $relayDataItem['function'] = intval(isset($relay['function']) ? $relay['function'] : SMART_CONTROL_FUNCTION_DOOR);
    $relayDataItem['create_time'] = strval($device['CreateTime']);
    $relayDataItem['mac'] = strval($device['MAC']);
    $relayDataItem['smart_control_online_status'] = intval($device['Status']);
    $relayDataItem['relay_info'] = [];
    
    $relayInfo = [];
    $relayInfo['relay_id'] = strval($index);
    $relayInfo['relay_uuid'] = strval($device['UUID']);
    $relayInfo['door_name'] = strval(isset($relay['name']) ? $relay['name'] : '');
    $relayInfo['enable'] = strval(isset($relay['enable']) ? $relay['enable'] : 1);
    $relayInfo['relay_status'] = intval(isset($relayStatus[$index]) ? $relayStatus[$index] : 0);
    $relayInfo['type'] = "Local";
    $relayInfo['action_type'] = 0;
    $relayInfo['hold_delay'] = intval(isset($relay['hold_delay']) ? $relay['hold_delay'] : -1);
    $relayInfo['external_device_type'] = 0;
    $relayDataItem['relay_info'][] = $relayInfo;
    
    return $relayDataItem;
}



function getExternRelaysInfo($device)
{
    $relaysInfo = [];
    
    $indoorConfig = \resident\model\getIndoorMonitorConfig($device['UUID']);
    if (!$indoorConfig || $indoorConfig['ExtraRelaySwitch'] != 1) 
    {
        return $relaysInfo;
    }
    
    $extraDevices = \resident\model\getExtraDevicesByConfigUUID($indoorConfig['UUID']);
    
    foreach ($extraDevices as $extraDevice) 
    {
        if ($extraDevice['EnableSwitch'] != 1) 
        {
            continue;
        }
        
        $deviceRelays = \resident\control\getExtraDeviceRelaysInfo($extraDevice, $indoorConfig, $device);
        $relaysInfo = array_merge($relaysInfo, $deviceRelays);
    }
    
    return $relaysInfo;
}


//处理单个外接设备的继电器

function getExtraDeviceRelaysInfo($extraDevice, $indoorConfig, $device)
{
    $relaysInfo = [];
    $relayLists = \resident\model\getExtraDeviceRelayListsByDeviceUUID($extraDevice['UUID']);
    
    foreach ($relayLists as $relayList) 
    {
        if ($relayList['EnableSwitch'] != 1) 
        {
            continue;
        }
        
        $relayDataItem = \resident\control\getRelayListInfo($relayList, $extraDevice, $indoorConfig, $device);
        if ($relayDataItem) 
        {
            $relaysInfo[] = $relayDataItem;
        }
    }
    
    return $relaysInfo;
}

function getRelayListInfo($relayList, $extraDevice, $indoorConfig, $device)
{
    $function = intval($relayList['Function']);
    $relayInfoList = [];
    
    $actions = \resident\model\getExtraDeviceRelayActionsByRelayUUID($relayList['UUID']);
    
    foreach ($actions as $action) 
    {
        $relayID = \resident\control\calculateRelayID($action['Output'], $extraDevice['DeviceIndex']);
        $relayInfo = \resident\control\buildExternRelayInfo(
            $relayID,
            $action['UUID'],
            $relayList['Name'],
            $action['Status'],
            $action['ActionType'],
            $action['HoldDelay'],
            $indoorConfig['ExtraRelayType']
        );
        $relayInfoList[] = $relayInfo;
    }
    
    if (empty($relayInfoList)) 
    {
        return null;
    }
    
    return \resident\control\buildExternRelayDataItem($function, $relayInfoList, $relayList, $device);
}

function calculateRelayID($output, $deviceIndex)
{
    $relayIndex = \resident\model\getOutputValue($output);
    $deviceIndex = intval($deviceIndex);
    $baseRelayID = ($deviceIndex - 1) * RELAY_COUNT_PER_EXTERN_DEVICE;
    $relayID = $baseRelayID + $relayIndex - 1;
    
    if (strpos($output, RELAY_OUTPUT_PREFIX_OT) === 0) 
    {
        //对于数字输出，调整relay_id的基础上再+7(8-1=7,app从0开始，所以需要-1)
        //RelayIndex=1的数字输出映射到ID 8(1+7=8)
        $relayID = $baseRelayID + $relayIndex + 7;
    }
    
    return $relayID;
}


//构建外接继电器信息
function buildExternRelayInfo($relayID, $relayUUID, $doorName, $relayStatus, $actionType, $holdDelay, $externalDeviceType)
{
    $relayInfo = [];
    $relayInfo['relay_id'] = strval($relayID);
    $relayInfo['relay_uuid'] = strval($relayUUID);
    $relayInfo['door_name'] = strval($doorName);
    $relayInfo['enable'] = "1";
    $relayInfo['relay_status'] = intval($relayStatus);
    $relayInfo['type'] = "Extern";
    $relayInfo['action_type'] = intval($actionType);
    $relayInfo['hold_delay'] = intval($holdDelay !== null ? $holdDelay : -1);
    $relayInfo['external_device_type'] = intval($externalDeviceType);
    
    return $relayInfo;
}

function buildExternRelayDataItem($function, $relayInfoList, $relayList, $device)
{
    $relayDataItem = [];
    $relayDataItem['function'] = intval($function);
    $relayDataItem['relay_info'] = $relayInfoList;
    $relayDataItem['create_time'] = strval($relayList['CreateTime']);
    $relayDataItem['mac'] = strval($device['MAC']);
    $relayDataItem['smart_control_online_status'] = intval($device['Status']);
    
    return $relayDataItem;
}


function getLocalRelayInfo($userData, $relayUUID, $relayID)
{
    $deviceInfo = \common\model\getDeviceInfoByRoleAndUUID($userData['Role'], $relayUUID);
    if (!$deviceInfo) 
    {
        \util\log\akcsLog::debug("getLocalRelayInfo: deviceInfo not found, relayUUID: " . $relayUUID);
        return null;
    }
    
    $relayJsonData = json_decode($deviceInfo['Relay'], true);

    if (!is_array($relayJsonData) || !isset($relayJsonData[$relayID])) 
    {
        \util\log\akcsLog::debug("getLocalRelayInfo: relayJsonData not found, relayUUID: " . $relayUUID);
        return null;
    }
    
    $relay = $relayJsonData[$relayID];
    $relayStatus = \util\common\getRelayStatusFromFlags($deviceInfo['Flags']);
    $relayData = \resident\control\buildLocalRelayDataItem($deviceInfo, $relayID, $relay, $relayStatus);
    
    return $relayData;
}

/**
 * 处理外接继电器信息查询
 */
function getExternRelayInfo($relayUUID)
{
    $relayInfoData = \resident\model\getExtraDeviceRelayActionInfoByUUID($relayUUID);
    if (!$relayInfoData) 
    {
        return null;
    }
    
    $relayID = \resident\control\calculateRelayID($relayInfoData['Output'], $relayInfoData['DeviceIndex']);
    
    $relayData = [];
    $relayData['function'] = intval($relayInfoData['Function']);
    $relayData['create_time'] = strval($relayInfoData['CreateTime']);
    $relayData['mac'] = strval($relayInfoData['MAC']);
    $relayData['smart_control_online_status'] = intval($relayInfoData['DeviceStatus']);
    $relayData['relay_info'] = [];
    
    $relayInfo = \resident\control\buildExternRelayInfo(
        $relayID,
        $relayUUID,
        $relayInfoData['Name'],
        $relayInfoData['Status'],
        $relayInfoData['ActionType'],
        $relayInfoData['HoldDelay'],
        $relayInfoData['ExtraRelayType']
    );
    $relayData['relay_info'][] = $relayInfo;
    
    return $relayData;
}

//获取所有smart control设备信息
function getAllSmartControlInfo($request, $response)
{
    $indoorDevices = \resident\model\getIndoorDevicesList();
    $smartControlDevices = [];
    
    foreach($indoorDevices as $device) 
    {
        // 获取传统relay设备信息
        $deviceRelays = \resident\control\getDeviceRelaysInfo($device);
        $smartControlDevices = array_merge($smartControlDevices, $deviceRelays);
        
        // 获取zigbee设备信息
        $deviceZigbeeDevices = \resident\control\getDeviceZigbeeDevicesInfo($device);
        $smartControlDevices = array_merge($smartControlDevices, $deviceZigbeeDevices);
    }
    
    $result = ['smart_control_devices' => $smartControlDevices];
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $result);
}

function getSmartControlDeviceInfo($request, $response)
{
    $userData = \util\container\getUserData();
    $params = $request->getQueryParams();
    
    $type = isset($params['type']) ? $params['type'] : '';
    $smartControlDeviceID = isset($params['smart_control_device_id']) ? $params['smart_control_device_id'] : '';
    $smartControlDeviceUUID = isset($params['smart_control_device_uuid']) ? $params['smart_control_device_uuid'] : '';
    
    if ($smartControlDeviceUUID === '' || $type === '') 
    {
        return \util\response\setResponseMessage($response, ERR_CODE_PARAM_INVALID);
    }
    
    $smartControlDeviceData = null;
    
    if ($type == "Local") 
    {
        $smartControlDeviceData = \resident\control\getLocalRelayInfo($userData, $smartControlDeviceUUID, $smartControlDeviceID);
    } 
    elseif ($type == "Extern") 
    {
        $smartControlDeviceData = \resident\control\getExternRelayInfo($smartControlDeviceUUID);
    }
    elseif ($type == "Zigbee")
    {
        $smartControlDeviceData = \resident\control\getZigbeeDeviceInfo($smartControlDeviceUUID);
    }
    else 
    {
        return \util\response\setResponseMessage($response, ERR_CODE_PARAM_INVALID);
    }
    
    if (!$smartControlDeviceData) 
    {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED);
    }
    
    $result = ['smart_control_devices' => [$smartControlDeviceData]];
    
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $result);
}