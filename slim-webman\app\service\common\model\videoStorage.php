<?php

namespace common\model;

function getVideoStorageDeviceInfo($devicesUUID)
{
    return \util\container\medooDb()->get("VideoStorageDevice", [
        "[>]VideoStorage" => ["VideoStorageDevice.VideoStorageUUID" => "UUID"]
    ],[
        "VideoStorage.IsEnable",
        "Expire" => \util\medoo\Medoo::raw("VideoStorage.ExpireTime < NOW()"),
    ],[
        "VideoStorageDevice.DevicesUUID" => $devicesUUID
    ]);
}