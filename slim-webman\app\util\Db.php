<?php

namespace util\db;

use Workerman\Coroutine\Pool;
use Workerman\Coroutine\Context;
use Workerman\Coroutine;

require_once __DIR__ . '/log.php';
require_once __DIR__ . '/medoo.php';

/**
 * 基于Workerman官方Pool的数据库类
 * 
 * 参考官方文档实现，支持自动获取和归还连接
 * 使用方法：Db::query('SELECT * FROM users')->fetchAll()
 */
class Db
{
    private static ?Pool $pool = null;
    private static $config = null;

    /**
     * 魔术方法，代理PDO操作
     */
    public static function __callStatic($name, $arguments)
    {
        if (self::$pool === null) {
            self::initializePool();
        }
        
        // 从协程上下文中获取连接，保证同一个协程使用同一个连接
        $connection = Context::get('db_connection');
        if (!$connection) {
            // 从连接池中获取连接
            $connection = self::$pool->get();
            Context::set('db_connection', $connection);
            
            // 当协程结束时，自动归还连接
            Coroutine::defer(function () use ($connection) {
                self::$pool->put($connection);
                Context::set('db_connection', null);
                \util\log\akcsLog::debug("[Db][DEBUG] 协程结束，自动归还数据库连接");
            });
            
            \util\log\akcsLog::debug("[Db][DEBUG] 从连接池获取数据库连接");
        }
        
        // 调用PDO方法
        $pdo = $connection['pdo'];
        return call_user_func_array([$pdo, $name], $arguments);
    }
    
    /**
     * 获取Medoo实例
     */
    public static function medoo()
    {
        if (self::$pool === null) {
            self::initializePool();
        }
        
        // 从协程上下文中获取连接
        $connection = Context::get('db_connection');
        if (!$connection) {
            // 从连接池中获取连接
            $connection = self::$pool->get();
            Context::set('db_connection', $connection);
            
            // 当协程结束时，自动归还连接
            Coroutine::defer(function () use ($connection) {
                self::$pool->put($connection);
                Context::set('db_connection', null);
                \util\log\akcsLog::debug("[Db][DEBUG] 协程结束，自动归还数据库连接");
            });
            
            \util\log\akcsLog::debug("[Db][DEBUG] 从连接池获取数据库连接");
        }
        
        return $connection['medoo'];
    }
    
    /**
     * 获取PDO实例
     */
    public static function pdo()
    {
        if (self::$pool === null) {
            self::initializePool();
        }
        
        // 从协程上下文中获取连接
        $connection = Context::get('db_connection');
        if (!$connection) {
            // 从连接池中获取连接
            $connection = self::$pool->get();
            Context::set('db_connection', $connection);
            
            // 当协程结束时，自动归还连接
            Coroutine::defer(function () use ($connection) {
                self::$pool->put($connection);
                Context::set('db_connection', null);
                \util\log\akcsLog::debug("[Db][DEBUG] 协程结束，自动归还数据库连接");
            });
            
            \util\log\akcsLog::debug("[Db][DEBUG] 从连接池获取数据库连接");
        }
        
        return $connection['pdo'];
    }

    /**
     * 初始化连接池
     */
    private static function initializePool(): void
    {
        self::$config = self::getDefaultConfig();
        
        $poolConfig = [
            'min_connections' => self::$config['min_connections'],
            'idle_timeout' => self::$config['idle_timeout'],
            'heartbeat_interval' => self::$config['heartbeat_interval'],
            'wait_timeout' => self::$config['wait_timeout'],
        ];
        
        \util\log\akcsLog::debug("[Db][INFO] 初始化Workerman数据库连接池，最大连接数: " . self::$config['max_connections']);
        
        self::$pool = new Pool(self::$config['max_connections'], $poolConfig);
        
        // 设置连接创建器
        self::$pool->setConnectionCreator(function () {
            return self::createConnection();
        });
        
        // 设置连接销毁器
        self::$pool->setConnectionCloser(function ($connection) {
            self::closeConnection($connection);
        });
        
        // 设置心跳检测器
        self::$pool->setHeartbeatChecker(function ($connection) {
            return self::checkConnection($connection);
        });
        
        \util\log\akcsLog::debug("[Db][INFO] Workerman数据库连接池初始化完成");
    }
    
    /**
     * 获取默认配置
     */
    private static function getDefaultConfig()
    {
        return [
            'host' => '127.0.0.1',
            'port' => 3306,
            'database' => 'akcs_db',
            'username' => 'root',
            'password' => '123456',
            'charset' => 'utf8mb4',
            'max_connections' => 20,
            'min_connections' => 5,
            'idle_timeout' => 300,
            'heartbeat_interval' => 60,
            'wait_timeout' => 10,
        ];
    }
    
    /**
     * 创建数据库连接
     */
    private static function createConnection()
    {
        try {
            $config = self::$config;
            
            // 创建Medoo实例
            $medooConfig = [
                'type' => 'mysql',
                'host' => $config['host'],
                'port' => $config['port'],
                'database' => $config['database'],
                'username' => $config['username'],
                'password' => $config['password'],
                'charset' => $config['charset'],
                'option' => [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                    \PDO::ATTR_EMULATE_PREPARES => false,
                ]
            ];
            
            $medoo = new \util\medoo\Medoo($medooConfig);
            
            // 从Medoo中获取PDO连接
            $pdo = $medoo->pdo;
            
            \util\log\akcsLog::debug("[Db][DEBUG] 创建新的数据库连接");
            
            return [
                'pdo' => $pdo,
                'medoo' => $medoo,
                'created_at' => time(),
            ];
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[Db][ERROR] 创建数据库连接失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 关闭数据库连接
     */
    private static function closeConnection($connection)
    {
        try {
            if (isset($connection['pdo'])) {
                $connection['pdo'] = null;
            }
            if (isset($connection['medoo'])) {
                $connection['medoo'] = null;
            }
            \util\log\akcsLog::debug("[Db][DEBUG] 关闭数据库连接");
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[Db][ERROR] 关闭数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查连接健康状态
     */
    private static function checkConnection($connection)
    {
        try {
            if (!isset($connection['pdo'])) {
                return false;
            }
            
            $pdo = $connection['pdo'];
            $result = $pdo->query('SELECT 1')->fetch();
            
            return $result !== false;
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[Db][ERROR] 连接健康检查失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取连接池统计信息
     */
    public static function getStats()
    {
        if (self::$pool === null) {
            return [];
        }
        
        try {
            $connectionCount = self::$pool->getConnectionCount();
            
            return [
                'total_connections' => $connectionCount,
                'max_connections' => self::$config['max_connections'],
                'min_connections' => self::$config['min_connections'],
                'config' => self::$config,
            ];
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[Db][ERROR] 获取统计信息失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 关闭连接池
     */
    public static function close()
    {
        try {
            if (self::$pool) {
                self::$pool->closeConnections();
                \util\log\akcsLog::debug("[Db][INFO] 数据库连接池已关闭");
            }
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[Db][ERROR] 关闭连接池失败: " . $e->getMessage());
        }
    }
}
