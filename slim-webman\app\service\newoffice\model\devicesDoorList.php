<?php

namespace newoffice\model;

use PDO;

require_once __DIR__ . "/../../../util/common.php";
require_once __DIR__ . "/../../common/model/version.php";

const DB_DEVICESDOORLIST_TABLE = "DevicesDoorList";

// 获取设备的所有Door
function getNewOfficeDevicesDoorListByDevUUID($devUUID)
{
    return \util\container\medooDb()->select(DB_DEVICESDOORLIST_TABLE,  
        ["UUID", "Enable", "Index", "Name", "RelayType", "Dtmf", "ControlledRelay", "ScheduleEnable", "ScheduleAccess", "ShowHome", "ShowTalking",
        "PinEnable", "FaceEnable", "RfCardEnable", "BleEnable", "NfcEnable", "PlateEnable", "IsEmergencyDoor", "IsLocked", "ExitButton", "DoorStatus", 
        "HoldOpenAlarmEnable", "HoldOpenAlarmTimeOut", "BreakInAlarmEnable", "Active", "ActiveTime", "ExpireTime","LockStatus", 
        "Expired" => \util\medoo\Medoo::raw("ExpireTime < now()")],
        ["DevicesUUID" => $devUUID]
    );
}

// 获取新办公下的所有Door Map
function getNewOfficeDevicesDoorListMap($devList)
{
    $devDoorListMap = array();
    foreach ($devList as $row => $dev) {
        if (\util\common\deviceIsAGDevType($dev["Type"])) {
            $devDoorList = getNewOfficeDevicesDoorListByDevUUID($dev["UUID"]);
            $devDoorListMap[$dev["UUID"]] = $devDoorList;
        }
    }
    return $devDoorListMap;
}
