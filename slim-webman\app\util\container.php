<?php

namespace util\container;
use PDO;
require_once(dirname(__FILE__).'/globalApp.php');
require_once(dirname(__FILE__).'/redisManage.php');
require_once __DIR__ . "/../util/log.php";
require_once __DIR__ . "/../util/consistentHash.php";
require_once __DIR__ . '/../../support/bootstrap/DatabasePool.php';

function initLogDb($dbId) {
    $dbuser = DATABASEUSER;
    $dbpass = DATABASEPWD;
    //LOG_DATABASEIP可能是多个地址集合 如logdb.akcs.inner;logdb1.akcs.inner
    $dbip = explode(";",LOG_DATABASEIP)[$dbId];
    $dbport = LOG_DATABASEPORT;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=LOG";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function initRedis() {
    $redisManage = new \util\redisManage\RedisManage();
    return $redisManage->getRedisInstance();
}

function getDb(){
    $gContainer = GlobalApp::getInstance();
    $db = $gContainer->get('akcs_db');

    // 如果从全局容器获取不到，尝试从连接池获取（向后兼容）
    if (!$db) {
        try {
            $connection = \support\bootstrap\DatabasePool::getConnection();
            if ($connection && isset($connection['pdo'])) {
                return $connection['pdo'];
            }
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[container] 从连接池获取PDO连接失败: " . $e->getMessage());
        }
    }

    return $db;
}

function medooDb(){
    $gContainer = GlobalApp::getInstance();
    $medoo = $gContainer->get('akcs_medoo_db');

    // 如果从全局容器获取不到，尝试从连接池获取（向后兼容）
    if (!$medoo) {
        try {
            $connection = \support\bootstrap\DatabasePool::getConnection();
            if ($connection && isset($connection['medoo'])) {
                return $connection['medoo'];
            }
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[container] 从连接池获取Medoo连接失败: " . $e->getMessage());
        }
    }

    return $medoo;
}

function mappingDb(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_mapping_db');
}

function getLOGDb(){
    $gContainer = GlobalApp::getInstance();
    //判断是否已经设置，若未设置进行设置
    if(!$gContainer->get('akcs_log_db')){
        $dbId = 0;
        //根据LOG_DATABASEIP来确定db总数量
        $dbNum = count(explode(";",LOG_DATABASEIP));
        if($dbNum > 1)
        {
            //通过一致性hash计算
            $ch = new \util\consistentHash\DbConsistentHash();
            $ch->initDbNumList($dbNum); 
            $delivery_uuid = getDeliveryUUID();
            $dbId = $ch->getDbNumByKey($delivery_uuid);
        }
        $gContainer->set('akcs_log_db', initLogDb($dbId));
    }

    return $gContainer->get('akcs_log_db');
}

function getRedis(){
    $gContainer = GlobalApp::getInstance();
    $redis = $gContainer->get('akcs_redis');

    if(!$redis) {
        // 尝试从Redis连接池获取连接
        try {
            $connection = \support\bootstrap\DatabasePool::getRedisConnection();
            if ($connection && isset($connection['redis'])) {
                \util\container\GlobalApp::getInstance()->set('akcs_redis', $connection['redis']);
                \util\container\GlobalApp::getInstance()->set('akcs_redis_connection', $connection);
                \util\log\akcsLog::debug("[container] 从Redis连接池获取连接成功");
                return $connection['redis'];
            }
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[container] 从Redis连接池获取连接失败: " . $e->getMessage());
        }

        // 降级到传统方式
        \util\container\GlobalApp::getInstance()->set('akcs_redis', initRedis());
    }
    return $gContainer->get('akcs_redis');
}

function getAuditLog(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_audit_log');
}

function getToken(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_token');
}

function getApiVersion(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_api_version');
}

function setUserData($data){
    $tmp_arr = [];
    $gContainer = GlobalApp::getInstance();
    $tmp_arr = $gContainer->get('akcs_user_data');
    if(!$tmp_arr) {
        $tmp_arr = array();
    }
    foreach($data as $key => $value){
        $tmp_arr[$key] = $value;
    }
    $gContainer->set('akcs_user_data', $tmp_arr);
}

function setDeliveryUUID($uuid){
    $gContainer = GlobalApp::getInstance();
    $gContainer->set('akcs_logdb_delivery_uuid', $uuid);
}

function getDeliveryUUID(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_logdb_delivery_uuid');
}

function getUserData(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_user_data');
}

function getResponseData(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_response_data');
}

function setResponseData($data){
    $gContainer = GlobalApp::getInstance();
    $gContainer['akcs_response_data'] = $data;
}

function getProjectType(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_user_data')['ProjectType'];
}

function getUserRole(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_user_data')['Role'];
}

function getTraceID(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_trace_id');
}