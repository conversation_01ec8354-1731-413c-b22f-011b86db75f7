<?php

namespace util\container;
use PDO;
require_once(dirname(__FILE__).'/globalApp.php');
require_once(dirname(__FILE__).'/redisManage.php');
require_once __DIR__ . "/../util/consistentHash.php";

function initLogDb($dbId) {
    $dbuser = DATABASEUSER;
    $dbpass = DATABASEPWD;
    //LOG_DATABASEIP可能是多个地址集合 如logdb.akcs.inner;logdb1.akcs.inner
    $dbip = explode(";",LOG_DATABASEIP)[$dbId];
    $dbport = LOG_DATABASEPORT;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=LOG";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function initDb() {
    $dbConnection = new PDO('mysql:host=127.0.0.1;dbname=akcs_db;charset=utf8mb4', 'root', '123456');
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function initMedooDb() {
    $config = [
        'type' => 'mysql',
        'host' => '127.0.0.1',
        'database' => 'akcs_db',
        'username' => 'root',
        'password' => '123456',
        'charset' => 'utf8mb4',
    ];
    return new \util\medoo\Medoo($config);
}

function initRedis() {
    $redisManage = new \util\redisManage\RedisManage();
    return $redisManage->getRedisInstance();
}

function getDb(){
    $gContainer = GlobalApp::getInstance();
    $db = $gContainer->get('akcs_db');
    if(!$db) {
        // 优先使用Workerman Pool连接池
        try {
            require_once __DIR__ . '/Db.php';
            $pdo = \util\db\Db::pdo();
            \util\container\GlobalApp::getInstance()->set('akcs_db', $pdo);
            \util\log\akcsLog::debug("[container] 使用Workerman Pool连接");
            return $pdo;
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[container] Workerman Pool连接失败: " . $e->getMessage());
        }

        // 降级到传统方式
        \util\container\GlobalApp::getInstance()->set('akcs_db', initDb());
        \util\log\akcsLog::debug("[container] 使用直连方式");
    }
    return $gContainer->get('akcs_db');
}

function medooDb(){
    $gContainer = GlobalApp::getInstance();
    $medoo = $gContainer->get('akcs_medoo_db');
    if(!$medoo) {
        // 优先使用Workerman Pool连接池
        try {
            require_once __DIR__ . '/Db.php';
            $medoo = \util\db\Db::medoo();
            \util\container\GlobalApp::getInstance()->set('akcs_medoo_db', $medoo);
            \util\log\akcsLog::debug("[container] 使用Workerman Pool Medoo连接");
            return $medoo;
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[container] Workerman Pool Medoo连接失败: " . $e->getMessage());
        }

        // 降级到传统方式
        \util\container\GlobalApp::getInstance()->set('akcs_medoo_db', initMedooDb());
        \util\log\akcsLog::debug("[container] 使用直连Medoo方式");
    }
    return $gContainer->get('akcs_medoo_db');
}

function mappingDb(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_mapping_db');
}

function getLOGDb(){
    $gContainer = GlobalApp::getInstance();
    //判断是否已经设置，若未设置进行设置
    if(!$gContainer->get('akcs_log_db')){
        $dbId = 0;
        //根据LOG_DATABASEIP来确定db总数量
        $dbNum = count(explode(";",LOG_DATABASEIP));
        if($dbNum > 1)
        {
            //通过一致性hash计算
            $ch = new \util\consistentHash\DbConsistentHash();
            $ch->initDbNumList($dbNum); 
            $delivery_uuid = getDeliveryUUID();
            $dbId = $ch->getDbNumByKey($delivery_uuid);
        }
        $gContainer->set('akcs_log_db', initLogDb($dbId));
    }

    return $gContainer->get('akcs_log_db');
}

function getRedis(){
    $gContainer = GlobalApp::getInstance();
    $redis = $gContainer->get('akcs_redis');
    if(!$redis) {
        // 优先使用Workerman Pool Redis连接池
        try {
            require_once __DIR__ . '/RedisConnectionPool.php';
            $redisPool = \util\redis\RedisConnectionPool::getInstance();
            $redis = $redisPool->getConnection();
            \util\container\GlobalApp::getInstance()->set('akcs_redis', $redis);
            \util\log\akcsLog::debug("[container] 使用Workerman Pool Redis连接");
            return $redis;
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[container] Workerman Pool Redis连接失败: " . $e->getMessage());
        }

        // 降级到传统方式
        \util\container\GlobalApp::getInstance()->set('akcs_redis', initRedis());
        \util\log\akcsLog::debug("[container] 使用直连Redis方式");
    }
    return $gContainer->get('akcs_redis');
}

function getAuditLog(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_audit_log');
}

function getToken(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_token');
}

function getApiVersion(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_api_version');
}

function setUserData($data){
    $tmp_arr = [];
    $gContainer = GlobalApp::getInstance();
    $tmp_arr = $gContainer->get('akcs_user_data');
    if(!$tmp_arr) {
        $tmp_arr = array();
    }
    foreach($data as $key => $value){
        $tmp_arr[$key] = $value;
    }
    $gContainer->set('akcs_user_data', $tmp_arr);
}

function setDeliveryUUID($uuid){
    $gContainer = GlobalApp::getInstance();
    $gContainer->set('akcs_logdb_delivery_uuid', $uuid);
}

function getDeliveryUUID(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_logdb_delivery_uuid');
}

function getUserData(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_user_data');
}

function getResponseData(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_response_data');
}

function setResponseData($data){
    $gContainer = GlobalApp::getInstance();
    $gContainer['akcs_response_data'] = $data;
}

function getProjectType(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_user_data')['ProjectType'];
}

function getUserRole(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_user_data')['Role'];
}

function getTraceID(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_trace_id');
}