<?php

namespace newoffice\control;

require_once __DIR__ . "/common.php";
require_once __DIR__ . "/../model/officePersonnel.php";
require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/../../office/model/officeInfo.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";
require_once __DIR__ . "/../../common/model/appPushToken.php";
require_once __DIR__ . "/../../common/model/token.php";
require_once __DIR__ . "/../../common/control/userconf.php";
require_once __DIR__ . "/../../common/model/personalAccountUserInfo.php";
require_once __DIR__ . "/../../common/model/officeMessageReceiver.php";
require_once __DIR__ . "/../../common/model/account.php";
require_once __DIR__ . "/../../common/model/callHistory.php";
require_once __DIR__ . "/saltoLock.php";

function getNewOfficePerUserConf($request, $response)
{
    //检查token是否过期
    $resultToken = \util\container\getUserData();
    if($resultToken['ProjectType'] == PROJECT_TYPE_NEW_OFFICE && array_key_exists("UUID", $resultToken))
    {
        if(\newoffice\control\IsOfficePersonnelValid($resultToken["MngUUID"], $resultToken["UUID"]) == false){
            return \util\response\setResponseMessage($response, ERR_CODE_INVALID_TIME);
        }
    }

    $transType = $resultToken['SipType'];
    $mngSipType = \office\model\getOfficeUserMngSipType($resultToken['ParentID']);
    // 0=udp, 1=tcp, 2=tls, 3=none 管理员不配置
    if ($mngSipType["SipType"] != 3) {
        $transType = $mngSipType["SipType"];
    }

    $transType = \util\common\transSipType($resultToken['UUID'], $transType);

    $officeInfo = null;
    $isShowLandLine = 1;
    $enablePinConfig = 1;
    $codec = $resultToken['Codec'];
    $appSipPwd = $resultToken['SipPwd'];
    $switch = intval($resultToken['UserSwitch']);   //旧版本兼容的标识
    $isShowTmpkey = intval($resultToken['TempKeyPermission']);
    $accountInfo = \common\model\getAccountByParentId($resultToken['MngID']);
    $enableConfirmFlag = \util\utility\switchHandle($switch, PersonlAccountSwitchConfirm);

    if ($accountInfo) {
        $isShowTmpkey = \office\model\getIsShowTempKey($resultToken, $accountInfo);

        $officeInfo = \office\model\getOfficeInfo($accountInfo['UUID']);
        if (!\office\model\officeFeaturePlanIsExpire($accountInfo['UUID']) && \common\model\checkCommunityFeaturePlan($resultToken['MngID'], FeatureItemPin)) {
            $enablePinConfig = \util\utility\switchHandle($officeInfo['Switch'], DevSwitchEnablePinConfig);
        } else {
            $enablePinConfig = 1;
        }
        $isShowLandLine = \util\utility\switchHandle($officeInfo['Switch'], DevSwitchEnableLandline);
        if($isShowLandLine == 1)
        {
            $isShowLandLine = \newoffice\model\getIsEnableLandLineByUUID($resultToken['UUID']);
        }
    }

    $appConf = array();
    $devListArray = array();

    // 获取设备列表
    \newoffice\model\getNewOfficeDevicesList($resultToken, $devListArray);

    // 是否开启APP对讲
    $appIntercomStatus = \newoffice\control\getAppIntercomStatus($resultToken);
    $callEnable = $appIntercomStatus['callEnable'];
    $eligibleIndoorOnline = $appIntercomStatus['eligibleIndoorOnline'];
    $smartIntercomStatus = $appIntercomStatus['smartIntercomStatus'];

    // 获取appconf
    $appConf['pin_init'] = 1; //pin初始化标记
    $appConf['show_payment'] = 0;
    $appConf['show_subscription'] = 0;
    $appConf['sip'] = $resultToken['SipAccount'];
    $appConf['display_name'] = $resultToken['Name'];
    $appConf['sip_passwd'] = \util\utility\passwdDecode($appSipPwd);
    $appConf['motion_alert'] = \util\redisManage\getAppMotionStatus($resultToken['UserAccount']); //app-uid的motion接收开发
    $appConf['video_res'] = "2";
    $appConf['video_bitrate'] = "512";
    $appConf['video_storage_time'] = strval(\common\model\getVideoStorageTime($resultToken['UserAccount']));
    //是否有公共设备
    $appConf['have_public_dev'] = "1";
    $appConf['show_landline'] = intval($isShowLandLine);
    $appConf['enable_pin_config'] = intval($enablePinConfig);
    $appConf['uid'] = $resultToken['UserAccount'];
    $appConf['node'] = $resultToken['Account'];
    $appConf['trans_type'] = strval($transType); //0-udp, 1-tcp, 2-tls
    $appConf['codec'] = strval($codec); //0=PCMU, 8 =PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义
    $appConf['data_collection'] = 0; //app数据收集到网管系统 后续如果用户较多可采样收集
    $appConf['landline'] = \util\common\getPbxLandlineNumber($resultToken['PhoneCode'], $resultToken['Phone'], $resultToken['Phone2'], $resultToken['Phone3']);
    $appConf['rtp_confuse'] = intval($mngSipType["RtpConFuse"]);
    $appConf['show_tempkey'] = intval($isShowTmpkey);
    $appConf['role'] = intval($resultToken['Role']);
    $appConf['check_dev'] = 1; //检查室内机的收费方案flag
    $appConf['check_slave'] = 1;
    $appConf['call_enable'] = intval($callEnable);
    $appConf['eligible_indoor_online'] = intval($eligibleIndoorOnline);
    $appConf['smart_intercom_status'] = intval($smartIntercomStatus);
    $appConf['enable_confirm_flag'] = intval($enableConfirmFlag);  //高级设置开关

    // 获取主站点的sip信息
    $userInfo = \common\model\getUserInfoByUUID($resultToken['UserInfoUUID']);
    $mainSipInfo = \common\model\getUserSipInfo($userInfo['AppMainUserAccount']);
    $appConf['main_sip'] = $userInfo['AppMainUserAccount'];

    $appSipPwd = $resultToken['SipPwd'];
    $appMainSipPwd = $mainSipInfo['SipPwd'];
    \common\model\checkAccountExpire($appSipPwd, $appMainSipPwd, $appConf, $resultToken);
    $appConf['sip_passwd'] = \util\utility\passwdDecode($appSipPwd);
    $appConf['main_sip_passwd'] = \util\utility\passwdDecode($appMainSipPwd);

    // 是否为多套房用户
    $appConf['is_site_link'] = \common\model\isMultiSiteUser($resultToken['UserInfoUUID']);
    if ($appConf['is_site_link']) {
        $allLandlines = array();
        $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($resultToken['UserInfoUUID']);
        foreach ($personalAccountList as $siteInfo) {
            $siteLandline = \util\common\getPbxLandlineNumber($siteInfo['PhoneCode'], $siteInfo['Phone'], $siteInfo['Phone2'], $siteInfo['Phone3']);
            $allLandlines = array_merge($allLandlines, $siteLandline);
        }
        //app根据all_sites_landline添加app联系人，根据show_landline和landline字段判断是否弹窗，如果all_sites_landline有变化也需要弹窗
        $appConf['all_sites_landline'] = array_values(array_unique($allLandlines));
    } else {
        $appConf['all_sites_landline'] = $appConf['landline'];
    }

    $siteinfo = array();
    \common\control\getOfficeSiteInfo($resultToken, $siteinfo);
    $appConf['room_name'] = $siteinfo['room_name'];
    $appConf['project_name'] = $siteinfo['project_name'];
    $appConf['room_title'] = $siteinfo['room_title'];
    $appConf['enable_smarthome'] = 0; // 是否启用智能家居
    $appConf['is_old_community'] = 0;

    $unreadArr['activities_num'] = intval(\common\model\getActivitiesNum($resultToken));
    //要新增msgnum
    $unreadArr['messages_num'] = intval(\common\model\getMessagesNumV70(MESSAGE_RECEIVER_CLIENT_TYPE_APP));

    // 获取三方锁(新办公目前只支持SALTO锁)
    $lockDevList = \newoffice\control\salto\getSaltoLockList($devListArray);
    $thirdPartyDevList = array();
    $thirdPartyDevList['lock_dev_list'] = $lockDevList;

    $datas = [
        "app_conf" => $appConf,
        "dev_list" => $devListArray,
        "unread_msg" => $unreadArr,
        "third_party_dev_list" => $thirdPartyDevList
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function getAppIntercomStatus($resultToken)
{
    $callEnable = 0;

    $officePersonnelInfo = \newoffice\model\getOfficePersonnelByUUID($resultToken['UUID']);
    $isHighEndDevOnline = \newoffice\model\checkHighEndDevOnline($resultToken);

    $eligibleIndoorOnline = 1;
    $smartIntercomStatus = 1;
    //7.1.3版本之前逻辑
    if (!\util\version\isNeedEligibleIndoorOnlineCheck()) {
        if ($officePersonnelInfo && $officePersonnelInfo["IsSmartPlusIntercom"] == 1 && $officePersonnelInfo["AppIntercomeActive"] == 1 && $officePersonnelInfo["CallNotExpire"] == 1) {
            $callEnable = 1;
        }
        if ($resultToken["IsFreeAppIntercome"] == 1) {
            $callEnable = $isHighEndDevOnline;  
        }
    } else {// 大于等于7.1.3版本逻辑
        if ($officePersonnelInfo["IsSmartPlusIntercom"] == 1) {
            $callEnable = 1; // 只要网页开启app对讲，则callEnable=1
            if ($resultToken["IsFreeAppIntercome"] == 1 && !$isHighEndDevOnline) {
                $eligibleIndoorOnline = 0; // 免费的app对讲，但高端机型未上线过
                \util\log\akcsLog::debug('Free app intercom, but high-end device has never been online');
            } elseif ($resultToken["IsFreeAppIntercome"] == 0 && ($officePersonnelInfo["AppIntercomeActive"] == 0 || $officePersonnelInfo["CallNotExpire"] == 0)) {
                \util\log\akcsLog::debug('Paid app intercom, but not activated or expired');
                $smartIntercomStatus = 0;
            }
        }
    }

    return [
        'callEnable' => $callEnable,
        'eligibleIndoorOnline' => $eligibleIndoorOnline,
        'smartIntercomStatus' => $smartIntercomStatus
    ];
}
