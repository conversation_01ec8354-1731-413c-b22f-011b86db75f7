<?php

namespace common\model;

use PDO;

function checkRedirectAndUpdateCode($code, $userConf)
{
    $parentid = $userConf["ParentID"];
    $role = $userConf["Role"];
    $account = $userConf["UserAccount"];
    if (SERVER_LOCATION == "au") {
       updateCode2InnerServer(UPDATE_SCLOUD_AUTH_URL, $account, $code);
       return;
    }

    if (SERVER_LOCATION == "as" && checkAuRedirect($account, $role, $parentid)) {
        updateCode2InnerServer(UPDATE_AUCLOUD_AUTH_URL, $account, $code);
        return;
    }
    return;
}

function updateCode2InnerServer($url, $account, $code)
{
    $jsonarr = [
        'account' => $account,
        'code' => strval($code),
    ];
    $jsonstr = json_encode($jsonarr);
    $headers = array(
      "authtype: 1",
      "Content-Type: application/json",
      "Accept: application/json"
    );

    $rt = \util\utility\curlRequest($url, $jsonstr, $headers);
    if ($rt) {
        \util\log\akcsLog::debug("update code to $url server error!");
        return -1;
    }
    \util\log\akcsLog::debug("update code to $url server is ok!");
    return 0;
}

function checkAuRedirect($account, $role, $parent_id)
{
    $db = \util\container\getDb();
    $community_id = 0;
    if ($role == ROLE_TYPE_COMMUNITY_MASTER
        || \util\common\isOfficeUser($role)
        || $role == ROLE_TYPE_PERSONNAL_MASTER
        || $role == ROLE_TYPE_COMMUNITY_PM
        || $role == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        $community_id = $parent_id;
    } else {
        $sth = $db->prepare("select A.ID FROM Account A join PersonalAccount P ON P.ParentID = A.ID WHERE P.ID =:parent_id");
        $sth->bindParam(':parent_id', $parent_id, PDO::PARAM_STR);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        if (!$result) {
            return false;
        }
        $community_id = $result['ID'];
    }

    $sth = $db->prepare("select ParentID FROM Account WHERE ID =:ID");
    $sth->bindParam(':ID', $community_id, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if (!$result) {
        return false;
    }

    $dis_id = $result['ParentID'];
    //Cloud = 2 代表redirect到aucloud， 1代表redirect到jcloud
    $sth = $db->prepare("select ID from AwsRedirect where AccountID =:ID and Cloud = 2");
    $sth->bindParam(':ID', $dis_id, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if (!$result) {
        return false;
    }
    \util\log\akcsLog::debug("checkAuRedirect yes account:" . $account);
    return true;
}

function checkAzerbjRedirect()
{
    $userData = \util\container\getUserData();
    $mngUUID = $userData['MngUUID'];
    $db = \util\container\getDb();
    $sth = $db->prepare("select ParentID FROM Account WHERE UUID =:UUID");
    $sth->bindParam(':UUID', $mngUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    $disID = 0;
    if ($result) {
        $disID = $result['ParentID'];
    }

    //Cloud=3 代表redirect到AzerbjCloud
    $sth = $db->prepare("select ID from AwsRedirect where AccountID =:ID and Cloud = 3");
    $sth->bindParam(':ID', $disID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if (!$result) {
        return false;
    }

    return true;    
}