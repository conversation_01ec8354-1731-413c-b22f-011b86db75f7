<?php

namespace office\control;

require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/../../resident/control/residentOpenDoor.php";

function OpenSiteDoor($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();
    $isCanOpendoor = 0;
    $mac = $postDatas['mac'];
    $relay = $postDatas['relay'];
    $securityRelay=$postDatas['security_relay'];
    $account = $userConf['Account'];
    $traceID = $postDatas['trace_id'];

    $agMacList = array(); //key is mac,value is relay
    $agPubMacList = array(); //key is mac,value is relay
    \office\model\getUserAGDeviceListForOffice($account, $agMacList, $agPubMacList);
    $relay1 = \util\common\relayId2RelayInt($relay);
    $securityRelay1 = \util\common\relayId2RelayInt($securityRelay);
    if (!($isCanOpendoor = \util\common\checkAppOpenDoor($agPubMacList, $mac, $relay1, $securityRelay1))) {
        $isCanOpendoor  = \util\common\checkAppOpenDoor($agMacList, $mac, $relay1, $securityRelay1);
    }

    if ($isCanOpendoor) {
        $datas = array();
        if ($traceID) {
            $datas['is_need_wait_response'] = 0;
            if (\util\common\DevSupportOpenDoorAck(\common\model\getDevicesFunctionByMac($mac)))
            {
                $datas['is_need_wait_response'] = 1;
            }
        } else {
            $traceID = \util\utility\createTraceID(6); //随机生成一个，避免traceID为空影响后续流程
        }
        if (!is_null($relay)) {
            \util\log\akcsLog::debug("OpenDoorNotify:mac=$mac,relay=$relay,uid=" . $userConf['UserAccount']);
            openDoorNotify(PROJECT_TYPE_OFFICE,$mac, $relay, $userConf['UserAccount'], DEIVCE_DISABLE_REPOST, $traceID);
        }

        if (!is_null($securityRelay)) {
            \util\log\akcsLog::debug("OpenSecurityNotify:mac=$mac,relay=$securityRelay,uid=" . $userConf['UserAccount']);
            openSecurityRelayNotify(PROJECT_TYPE_OFFICE,$mac, $securityRelay, $userConf['UserAccount'], DEIVCE_DISABLE_REPOST, $traceID);
        }

        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
        
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_NO_PERMISSION);
    }
}
