<?php

namespace common\model;

use PDO;

function getPolicySignByPersonalAccountUUID($personalAccountUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("
        SELECT 
            ID,
            UUID,
            PersonalAccountUUID,
            ProviderAccountUUID,
            PrivacyPolicyVersion,
            UserPolicyVersion,
            IsNeedReSign,
            CreateTime,
            UpdateTime
        FROM AppPolicySign 
        WHERE PersonalAccountUUID = :personalAccountUUID
    ");
    $sth->bindParam(':personalAccountUUID', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    
    return $result; 
}

