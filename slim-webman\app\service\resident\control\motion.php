<?php

namespace  resident\control;
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";
require_once __DIR__ . "/../../common/model/communityInfo.php";
require_once __DIR__ . "/../../common/model/version.php";
require_once __DIR__ . "/../../common/control/deviceModel.php";

function getMotionCnf($request, $response)
{
    $resultToken = \util\container\getUserData();

    $motionTime = 10;
    $role = $resultToken['Role'];
    if ($role == ROLE_TYPE_COMMUNITY_PM) {
        $detectionInfo = \common\model\getCommunityDetectionInfo($resultToken['MngID']);
    } else {
        $detectionInfo = \common\model\getDetectionInfo($resultToken['Account']);
    }
    $enableMotion = intval($detectionInfo['EnableMotion']);
    $motionTime = intval($detectionInfo['MotionTime']);
    
    $enablePackageDetection = intval($detectionInfo['EnablePackageDetection']);
    // $enableSoundDetection = intval($detectionInfo['EnableSoundDetection']);
    // $soundType = intval($detectionInfo['SoundType']);
    $supportPackageDetectionNameList = \common\control\getSupportPackageDetectionModelNameList();
    // $supportSoundDetection = \common\model\getSupportSoundDetectionList();


    $datas = [
        'enable_motion' => $enableMotion,
        'motion_time'  => $motionTime,
        'enable_package_detection' => $enablePackageDetection,
        'package_detection_support_devices' => $supportPackageDetectionNameList,
        // 'enable_sound_detection' => $enableSoundDetection,
        // 'sound_detection_support_devices' => array_column($supportSoundDetection, 'VersionName'),
        // 'sound_type' => [
        //     'enable_gun_shot' => \util\utility\switchHandle($soundType, 0),
        //     'enable_dog_barking' => \util\utility\switchHandle($soundType, 1),
        //     'enable_baby_crying' => \util\utility\switchHandle($soundType, 2),
        //     'enable_glass_breaking' => \util\utility\switchHandle($soundType, 3),
        //     'enable_siren' => \util\utility\switchHandle($soundType, 4),
        // ]
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function setMotionCnf($request, $response)
{
    $resultToken = \util\container\getUserData();
    $postDatas = $request->getParsedBody();
    $motionTime = $postDatas['motion_time'];
    $enableMotion = $postDatas['enable_motion'];

    \common\model\updateMotionStatus($motionTime, $enableMotion, $resultToken['Account']);

    setMotionNotify($resultToken);

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}