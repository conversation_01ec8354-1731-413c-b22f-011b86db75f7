<?php
namespace  newoffice\control;

require_once __DIR__ . "/../../common/model/thirdPartyLockDevice.php";
require_once __DIR__ . "/../../common/model/thirdPartyLockAccount.php";
require_once __DIR__ . "/saltoLock.php";

function thirdPartyDoorControl($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();

    \util\log\akcsLog::debug(json_encode($postDatas));

    $devUUID = $postDatas['uuid'];
    $type = $postDatas['type'];
    $operate_type = $postDatas['operate_type'];

    if ($devUUID === null || $type === null || $operate_type === null) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "body is null");
    }

    switch ($type) {
        case SALTO_LOCK_TYPE:
            $code = \newoffice\control\salto\openSaltoDoor($userConf, $devUUID);
            return \util\response\setResponseMessage($response, $code);
            break;
        default:
            \util\log\akcsLog::debug("open_third_party_door brand no support");
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function getThirdpartyLock($request, $response)
{
    $userConf = \util\container\getUserData();

    $lockDevList = array();
    $saltoLockList = array();
    \newoffice\control\salto\getSaltoLockRealTimeStatus($userConf, $saltoLockList);

    $lockDevList = array_merge($lockDevList, $saltoLockList);
    $data = ["third_party_dev_list" => [
        "lock_dev_list" => $lockDevList
    ]];
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $data);
}
