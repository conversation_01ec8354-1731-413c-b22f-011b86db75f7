<?php

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/define.php';
require_once __DIR__ . '/../app/config/dynamic_config.php';
require_once __DIR__ . '/../app/util/Db.php';
require_once __DIR__ . '/../app/util/MySQLConnectionPool.php';
require_once __DIR__ . '/../app/util/RedisConnectionPool.php';

/**
 * Workerman Pool连接池测试
 * 
 * 测试基于Workerman官方Pool的连接池功能
 */
class WorkermanPoolTest
{
    public function __construct()
    {
        echo "=== Workerman Pool连接池测试开始 ===\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        try {
            $this->testDbClass();
            $this->testMySQLPool();
            $this->testRedisPool();
            $this->testConcurrency();
            
            echo "\n=== 所有Workerman Pool测试通过 ===\n";
            
        } catch (Exception $e) {
            echo "\n=== Workerman Pool测试失败: " . $e->getMessage() . " ===\n";
            throw $e;
        }
    }
    
    /**
     * 测试Db类
     */
    public function testDbClass()
    {
        echo "\n1. 测试Workerman Db类...\n";
        
        try {
            // 测试PDO方法
            $result = \util\db\Db::query('SELECT 1 as test, NOW() as current_time')->fetch();
            $this->assert($result['test'] == 1, "Db::query应该返回正确结果");
            
            // 测试Medoo方法
            $medoo = \util\db\Db::medoo();
            $this->assert($medoo instanceof \util\medoo\Medoo, "Db::medoo应该返回Medoo实例");
            
            // 测试PDO实例
            $pdo = \util\db\Db::pdo();
            $this->assert($pdo instanceof PDO, "Db::pdo应该返回PDO实例");
            
            // 测试统计信息
            $stats = \util\db\Db::getStats();
            $this->assert(isset($stats['total_connections']), "应该返回连接统计信息");
            
            echo "   ✓ Workerman Db类测试通过\n";
            
        } catch (Exception $e) {
            echo "   ✗ Workerman Db类测试失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 测试MySQL连接池
     */
    public function testMySQLPool()
    {
        echo "\n2. 测试Workerman MySQL连接池...\n";
        
        try {
            $pool = \util\mysql\MySQLConnectionPool::getInstance();
            $this->assert($pool !== null, "MySQL连接池应该成功初始化");
            
            // 测试获取连接
            $connection = $pool->getConnection();
            $this->assert(isset($connection['pdo']), "应该返回包含PDO的连接");
            $this->assert(isset($connection['medoo']), "应该返回包含Medoo的连接");
            
            // 测试连接功能
            $pdo = $connection['pdo'];
            $result = $pdo->query('SELECT 1 as test')->fetch();
            $this->assert($result['test'] == 1, "连接应该正常工作");
            
            // 测试Medoo功能
            $medoo = $connection['medoo'];
            $result = $medoo->query('SELECT 1 as test')->fetchAll();
            $this->assert(!empty($result), "Medoo连接应该正常工作");
            
            // 测试统计信息
            $stats = $pool->getStats();
            $this->assert(isset($stats['total_connections']), "应该返回连接统计信息");
            $this->assert($stats['max_connections'] > 0, "最大连接数应该大于0");
            
            // 测试健康检查
            $isHealthy = $pool->isHealthy();
            $this->assert($isHealthy === true, "连接池应该是健康的");
            
            echo "   ✓ Workerman MySQL连接池测试通过\n";
            echo "   连接统计: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "\n";
            
        } catch (Exception $e) {
            echo "   ✗ Workerman MySQL连接池测试失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 测试Redis连接池
     */
    public function testRedisPool()
    {
        echo "\n3. 测试Workerman Redis连接池...\n";
        
        try {
            $pool = \util\redis\RedisConnectionPool::getInstance();
            $this->assert($pool !== null, "Redis连接池应该成功初始化");
            
            // 测试获取连接
            $redis = $pool->getConnection();
            $this->assert($redis instanceof Redis, "应该返回Redis实例");
            
            // 测试Redis功能
            $redis->set('test_key', 'test_value');
            $value = $redis->get('test_key');
            $this->assert($value === 'test_value', "Redis读写应该正常工作");
            
            // 测试PING
            $pong = $redis->ping();
            $this->assert($pong === 'PONG' || $pong === '+PONG' || $pong === true, "Redis PING应该返回PONG");
            
            // 测试统计信息
            $stats = $pool->getStats();
            $this->assert(isset($stats['total_connections']), "应该返回连接统计信息");
            $this->assert($stats['max_connections'] > 0, "最大连接数应该大于0");
            
            // 测试健康检查
            $isHealthy = $pool->isHealthy();
            $this->assert($isHealthy === true, "Redis连接池应该是健康的");
            
            // 清理测试数据
            $redis->del('test_key');
            
            echo "   ✓ Workerman Redis连接池测试通过\n";
            echo "   连接统计: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "\n";
            
        } catch (Exception $e) {
            echo "   ✗ Workerman Redis连接池测试失败: " . $e->getMessage() . "\n";
            echo "   这可能是因为Redis服务未启动\n";
            // Redis测试失败不中断整个测试
        }
    }
    
    /**
     * 测试并发性能
     */
    public function testConcurrency()
    {
        echo "\n4. 测试Workerman Pool并发性能...\n";
        
        try {
            $startTime = microtime(true);
            $testCount = 10;
            $successCount = 0;
            
            // 模拟并发数据库查询
            for ($i = 0; $i < $testCount; $i++) {
                try {
                    $result = \util\db\Db::query('SELECT ? as test_id, NOW() as current_time', [$i])->fetch();
                    if ($result && $result['test_id'] == $i) {
                        $successCount++;
                    }
                } catch (Exception $e) {
                    echo "   查询 $i 失败: " . $e->getMessage() . "\n";
                }
            }
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            $successRate = round(($successCount / $testCount) * 100, 2);
            
            $this->assert($successCount > 0, "至少应该有一些查询成功");
            
            echo "   ✓ 并发性能测试完成\n";
            echo "   测试次数: $testCount\n";
            echo "   成功次数: $successCount\n";
            echo "   成功率: $successRate%\n";
            echo "   总耗时: {$duration}ms\n";
            echo "   平均耗时: " . round($duration / $testCount, 2) . "ms\n";
            
        } catch (Exception $e) {
            echo "   ✗ 并发性能测试失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 断言方法
     */
    private function assert($condition, $message)
    {
        if (!$condition) {
            throw new Exception("断言失败: {$message}");
        }
    }
}

/**
 * 测试容器集成
 */
function testContainerIntegration()
{
    echo "\n=== 测试容器集成 ===\n";
    
    try {
        // 模拟容器函数
        require_once __DIR__ . '/../app/util/container.php';
        
        // 测试getDb函数
        echo "测试getDb函数...\n";
        // 注意：这里可能会因为缺少GlobalApp而失败，这是正常的
        
        echo "容器集成测试完成\n";
        
    } catch (Exception $e) {
        echo "容器集成测试失败: " . $e->getMessage() . "\n";
        echo "这可能是因为缺少webman运行环境，属于正常情况\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    try {
        // 基础功能测试
        $test = new WorkermanPoolTest();
        $test->runAllTests();
        
        // 集成测试
        testContainerIntegration();
        
    } catch (Exception $e) {
        echo "Workerman Pool测试异常: " . $e->getMessage() . "\n";
        echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
        exit(1);
    }
}
