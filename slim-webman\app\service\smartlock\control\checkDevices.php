<?php

namespace smartlock\control;

require_once __DIR__ . "/../../smartlock/model/smartLock.php";
require_once __DIR__ . "/../../../util/smartlockAdapt.php";
require_once __DIR__ . "/../../common/model/smartLock.php";

function checkDevices($request, $response)
{
    // 获取请求数据
    $json = $request->rawBody();
    $json = rtrim($json, "\0"); //锁上报上来后面多了\0导致json解析失败
    $postDatas = json_decode($json, true);

    $deviceId = $postDatas['device_id'] ?? '';
    $deviceMac = $postDatas['device_mac'] ?? '';
    $deviceStyle = $postDatas['device_style'] ?? '';
    $residenceId = $postDatas['residence_id'] ?? '';

    // 验证必要参数
    if (empty($deviceId) || empty($deviceMac)|| empty($residenceId)) {
        \util\log\akcsLog::debug("checkDevices: missing required parameters");
        return json([
            'success' => false,
            'timestamp' => time() * 1000,
            'result' => [
                'reset' => false
            ]
        ]);
    }
    
    // 记录请求信息
    \util\log\akcsLog::debug("checkDevices request: device_id=$deviceId, device_mac=$deviceMac, device_style=$deviceStyle, residence_id=$residenceId");
    // 检查设备是否存在
    $lockInfo = \smartlock\model\getLockInfo($deviceId);
    
    $needReset = false; 
    if ($lockInfo) {
        if ($lockInfo['MAC'] !== $deviceMac) {
            $needReset = true;
            \util\log\akcsLog::debug("checkDevices: device MAC changed, reset required. Expected: {$deviceMac}, Found: {$lockInfo['MAC']}");
        }
        //\util\log\akcsLog::debug("checkDevices: device found, no reset required");
    } else {
        //锁只是不会链接云 不会进行锁的重置
        $needReset = true;
        // \util\log\akcsLog::debug("checkDevices: device not found, reset required");
    }
    
    $response_data = [
        'success' => true,
        'timestamp' => time() * 1000,
        'result' => [
            'reset' => $needReset
        ]
    ];
    
    \util\log\akcsLog::debug("checkDevices response: " . json_encode($response_data));
    
    return json($response_data);
}