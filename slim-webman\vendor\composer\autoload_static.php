<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitc599ea6f191a93a44a97371ebe41d22f
{
    public static $files = array (
        '253c157292f75eb38082b5acb06f3f01' => __DIR__ . '/..' . '/nikic/fast-route/src/functions.php',
        'da5b71a9ad8465d48da441e2f36823b6' => __DIR__ . '/../..' . '/support/helpers.php',
    );

    public static $prefixLengthsPsr4 = array (
        's' => 
        array (
            'support\\' => 8,
        ),
        'W' => 
        array (
            'Workerman\\' => 10,
            'Webman\\' => 7,
        ),
        'S' => 
        array (
            'Support\\View\\' => 13,
            'Support\\Exception\\' => 18,
            'Support\\Bootstrap\\' => 18,
            'Support\\' => 8,
        ),
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'Psr\\Container\\' => 14,
        ),
        'M' => 
        array (
            'Monolog\\' => 8,
        ),
        'F' => 
        array (
            'FastRoute\\' => 10,
        ),
        'A' => 
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'support\\' => 
        array (
            0 => __DIR__ . '/..' . '/workerman/webman-framework/src/support',
        ),
        'Workerman\\' => 
        array (
            0 => __DIR__ . '/..' . '/workerman/workerman',
        ),
        'Webman\\' => 
        array (
            0 => __DIR__ . '/..' . '/workerman/webman-framework/src',
        ),
        'Support\\View\\' => 
        array (
            0 => __DIR__ . '/..' . '/workerman/webman-framework/src/support/view',
        ),
        'Support\\Exception\\' => 
        array (
            0 => __DIR__ . '/..' . '/workerman/webman-framework/src/support/exception',
        ),
        'Support\\Bootstrap\\' => 
        array (
            0 => __DIR__ . '/..' . '/workerman/webman-framework/src/support/bootstrap',
        ),
        'Support\\' => 
        array (
            0 => __DIR__ . '/..' . '/workerman/webman-framework/src/support',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/Psr/Log',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
        'Monolog\\' => 
        array (
            0 => __DIR__ . '/..' . '/monolog/monolog/src/Monolog',
        ),
        'FastRoute\\' => 
        array (
            0 => __DIR__ . '/..' . '/nikic/fast-route/src',
        ),
        'App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $fallbackDirsPsr4 = array (
        0 => __DIR__ . '/../..' . '/',
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitc599ea6f191a93a44a97371ebe41d22f::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitc599ea6f191a93a44a97371ebe41d22f::$prefixDirsPsr4;
            $loader->fallbackDirsPsr4 = ComposerStaticInitc599ea6f191a93a44a97371ebe41d22f::$fallbackDirsPsr4;
            $loader->classMap = ComposerStaticInitc599ea6f191a93a44a97371ebe41d22f::$classMap;

        }, null, ClassLoader::class);
    }
}
