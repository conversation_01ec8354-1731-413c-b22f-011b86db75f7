<?php

namespace resident\model;

const SmartLockSqlField = ["UUID", "PersonalAccountUUID", "LastConnectedTime", "DeviceUUID", "AccountUUID", "Relay", "Name", "BatteryLevel", "WifiStatus","KeepAlive", "Model", "Status", "LockStatus"];

// 获取所有lock
function getSmartLockList($nodeUUID)
{
    return \util\container\medooDb()->select(
        "SmartLock",
        SmartLockSqlField,
        ["PersonalAccountUUID" => $nodeUUID]
    );
}

// 获取单个lock
function getSmartLockInfo($uuid)
{
    return \util\container\medooDb()->get(
        "SmartLock",
        SmartLockSqlField,
        ["UUID" => $uuid]
    );
}

// 根据UUID数组获取锁状态
function getLockStatusByUuids($lockUuids)
{
    return \util\container\medooDb()->select(
        "SmartLock",
        ["UUID", "LockStatus"],
        ["UUID" => $lockUuids]
    );
}