#!/bin/bash

# SMProxy启动脚本
# 用于启动、停止、重启SMProxy服务

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
SMPROXY_DIR="$PROJECT_DIR/smproxy"
LOG_DIR="$SMPROXY_DIR/logs"
PID_FILE="$LOG_DIR/pid/server.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查PHP
    if ! command -v php &> /dev/null; then
        log_error "PHP未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Swoole扩展
    if ! php -m | grep -q swoole; then
        log_error "Swoole扩展未安装"
        exit 1
    fi
    
    # 检查Docker（如果使用Docker模式）
    if [ "$USE_DOCKER" = "true" ]; then
        if ! command -v docker &> /dev/null; then
            log_error "Docker未安装或不在PATH中"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            log_error "Docker Compose未安装或不在PATH中"
            exit 1
        fi
    fi
    
    log_info "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p "$LOG_DIR/pid"
    mkdir -p "$LOG_DIR"
    
    # 设置权限
    chmod -R 755 "$LOG_DIR"
    
    log_info "目录创建完成"
}

# 检查SMProxy状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0  # 运行中
        else
            rm -f "$PID_FILE"
            return 1  # 未运行
        fi
    else
        return 1  # 未运行
    fi
}

# 启动SMProxy
start_smproxy() {
    log_info "启动SMProxy..."
    
    if check_status; then
        log_warn "SMProxy已经在运行中"
        return 0
    fi
    
    # 检查配置文件
    if [ ! -f "$SMPROXY_DIR/conf/database.json" ]; then
        log_error "数据库配置文件不存在: $SMPROXY_DIR/conf/database.json"
        exit 1
    fi
    
    if [ ! -f "$SMPROXY_DIR/conf/server.json" ]; then
        log_error "服务器配置文件不存在: $SMPROXY_DIR/conf/server.json"
        exit 1
    fi
    
    if [ "$USE_DOCKER" = "true" ]; then
        # Docker模式启动
        log_info "使用Docker模式启动SMProxy..."
        cd "$SMPROXY_DIR"
        docker-compose up -d
        
        # 等待容器启动
        sleep 5
        
        if docker-compose ps | grep -q "Up"; then
            log_info "SMProxy Docker容器启动成功"
        else
            log_error "SMProxy Docker容器启动失败"
            exit 1
        fi
    else
        # 直接启动
        log_info "使用直接模式启动SMProxy..."
        
        # 检查SMProxy可执行文件
        if [ ! -f "$SMPROXY_DIR/bin/SMProxy" ]; then
            log_error "SMProxy可执行文件不存在: $SMPROXY_DIR/bin/SMProxy"
            exit 1
        fi
        
        cd "$SMPROXY_DIR"
        php bin/SMProxy start
        
        # 等待启动
        sleep 3
        
        if check_status; then
            log_info "SMProxy启动成功，PID: $(cat $PID_FILE)"
        else
            log_error "SMProxy启动失败"
            exit 1
        fi
    fi
}

# 停止SMProxy
stop_smproxy() {
    log_info "停止SMProxy..."
    
    if [ "$USE_DOCKER" = "true" ]; then
        # Docker模式停止
        cd "$SMPROXY_DIR"
        docker-compose down
        log_info "SMProxy Docker容器已停止"
    else
        # 直接停止
        if check_status; then
            cd "$SMPROXY_DIR"
            php bin/SMProxy stop
            
            # 等待停止
            sleep 3
            
            if ! check_status; then
                log_info "SMProxy已停止"
            else
                log_warn "SMProxy可能未完全停止，尝试强制终止..."
                PID=$(cat "$PID_FILE")
                kill -9 "$PID" 2>/dev/null
                rm -f "$PID_FILE"
                log_info "SMProxy已强制停止"
            fi
        else
            log_warn "SMProxy未在运行"
        fi
    fi
}

# 重启SMProxy
restart_smproxy() {
    log_info "重启SMProxy..."
    stop_smproxy
    sleep 2
    start_smproxy
}

# 查看SMProxy状态
status_smproxy() {
    if [ "$USE_DOCKER" = "true" ]; then
        log_info "SMProxy Docker状态:"
        cd "$SMPROXY_DIR"
        docker-compose ps
    else
        if check_status; then
            PID=$(cat "$PID_FILE")
            log_info "SMProxy正在运行，PID: $PID"
            
            # 显示进程信息
            ps -p "$PID" -o pid,ppid,cmd
            
            # 显示端口监听情况
            if command -v netstat &> /dev/null; then
                log_info "端口监听情况:"
                netstat -tlnp | grep ":3366"
            fi
        else
            log_warn "SMProxy未在运行"
        fi
    fi
}

# 查看日志
show_logs() {
    log_info "显示SMProxy日志..."
    
    if [ "$USE_DOCKER" = "true" ]; then
        cd "$SMPROXY_DIR"
        docker-compose logs -f smproxy
    else
        if [ -f "$LOG_DIR/system.log" ]; then
            tail -f "$LOG_DIR/system.log"
        else
            log_warn "日志文件不存在: $LOG_DIR/system.log"
        fi
    fi
}

# 测试SMProxy连接
test_connection() {
    log_info "测试SMProxy连接..."
    
    # 使用PHP测试连接
    php -r "
    try {
        \$pdo = new PDO('mysql:host=127.0.0.1;port=3366', 'root', '123456');
        \$result = \$pdo->query('SELECT 1 as test')->fetch();
        if (\$result && \$result['test'] == 1) {
            echo \"连接测试成功\n\";
            exit(0);
        } else {
            echo \"连接测试失败：查询结果异常\n\";
            exit(1);
        }
    } catch (Exception \$e) {
        echo \"连接测试失败：\" . \$e->getMessage() . \"\n\";
        exit(1);
    }
    "
    
    if [ $? -eq 0 ]; then
        log_info "SMProxy连接测试通过"
    else
        log_error "SMProxy连接测试失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "SMProxy管理脚本"
    echo ""
    echo "用法: $0 [选项] <命令>"
    echo ""
    echo "选项:"
    echo "  -d, --docker    使用Docker模式"
    echo "  -h, --help      显示帮助信息"
    echo ""
    echo "命令:"
    echo "  start           启动SMProxy"
    echo "  stop            停止SMProxy"
    echo "  restart         重启SMProxy"
    echo "  status          查看SMProxy状态"
    echo "  logs            查看SMProxy日志"
    echo "  test            测试SMProxy连接"
    echo ""
    echo "示例:"
    echo "  $0 start                # 直接启动SMProxy"
    echo "  $0 -d start            # 使用Docker启动SMProxy"
    echo "  $0 status              # 查看SMProxy状态"
    echo "  $0 test                # 测试SMProxy连接"
}

# 主函数
main() {
    # 解析参数
    USE_DOCKER=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--docker)
                USE_DOCKER=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            start|stop|restart|status|logs|test)
                COMMAND=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查命令
    if [ -z "$COMMAND" ]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi
    
    # 检查依赖
    check_dependencies
    
    # 创建目录
    create_directories
    
    # 执行命令
    case $COMMAND in
        start)
            start_smproxy
            ;;
        stop)
            stop_smproxy
            ;;
        restart)
            restart_smproxy
            ;;
        status)
            status_smproxy
            ;;
        logs)
            show_logs
            ;;
        test)
            test_connection
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
