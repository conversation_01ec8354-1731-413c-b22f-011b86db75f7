<?php

namespace resident\control;

use PDO;

require_once __DIR__ . "/../model/devices.php";

//获取设备的zigbee设备信息
function getDeviceZigbeeDevicesInfo($device)
{
    $zigbeeDevicesInfo = [];
    $zigbeeDevices = \resident\model\getZigbeeDevicesByDeviceUUID($device['UUID']);
    
    foreach ($zigbeeDevices as $zigbeeDevice) 
    {
        $zigbeeDeviceDataItem = \resident\control\buildZigbeeDeviceDataItem($device, $zigbeeDevice);
        $zigbeeDevicesInfo[] = $zigbeeDeviceDataItem;
    }
    
    return $zigbeeDevicesInfo;
}

//构建zigbee设备数据项
function buildZigbeeDeviceDataItem($device, $zigbeeDevice)
{
    $zigbeeDeviceDataItem = [];
    
    // 根据zigbee设备类型映射function值
    $function = \resident\control\mapZigbeeDeviceTypeToFunction($zigbeeDevice['DeviceType']);
    
    $zigbeeDeviceDataItem['function'] = intval($function);
    $zigbeeDeviceDataItem['create_time'] = strval($zigbeeDevice['CreateTime']);
    $zigbeeDeviceDataItem['mac'] = strval($device['MAC']);
    
    // zigbee设备在线状态需要同时考虑室内机设备状态和zigbee设备自身状态，只有两个状态都在线时，才算在线
    $deviceOnlineStatus = intval($device['Status']);
    $zigbeeOnlineStatus = intval($zigbeeDevice['OnlineStatus']);
    $zigbeeDeviceDataItem['smart_control_online_status'] = ($deviceOnlineStatus && $zigbeeOnlineStatus) ? 1 : 0;
    
    // 构建zigbee设备信息数组
    $zigbeeDeviceInfo = \resident\control\buildZigbeeDeviceInfo($zigbeeDevice);
    $zigbeeDeviceDataItem['zigbee_devices_info'] = array($zigbeeDeviceInfo);
    
    return $zigbeeDeviceDataItem;
}

//将81室内机上报的zigbee设备类型映射到function值
function mapZigbeeDeviceTypeToFunction($deviceType)
{
    switch (intval($deviceType)) 
    {
        case ZIGBEE_DEVICE_TYPE_LIGHT: 
            return SMART_CONTROL_FUNCTION_LIGHT; 
        case ZIGBEE_DEVICE_TYPE_THERMOSTAT: 
            return SMART_CONTROL_FUNCTION_THERMOSTAT; 
        case ZIGBEE_DEVICE_TYPE_SHADE: 
            return SMART_CONTROL_FUNCTION_SHADE; 
        default:
            return SMART_CONTROL_FUNCTION_OTHERS; 
    }
}
  
//构建zigbee设备信息
function buildZigbeeDeviceInfo($zigbeeDevice)
{
    $zigbeeDeviceInfo = [];
    
    $zigbeeDeviceInfo['zigbee_devices_id'] = strval($zigbeeDevice['ZigbeeDeviceID']);
    $zigbeeDeviceInfo['zigbee_devices_uuid'] = strval($zigbeeDevice['UUID']);
    $zigbeeDeviceInfo['name'] = strval($zigbeeDevice['Name']);
    $zigbeeDeviceInfo['status'] = intval($zigbeeDevice['Status']);
    
    $deviceType = intval($zigbeeDevice['DeviceType']);
    
    if ($deviceType == ZIGBEE_DEVICE_TYPE_LIGHT) // 灯控设备
    {
        // 灯控设备下发hold_delay字段
        $zigbeeDeviceInfo['hold_delay'] = intval($zigbeeDevice['HoldDelay'] !== null ? $zigbeeDevice['HoldDelay'] : -1);
    }
    elseif ($deviceType == ZIGBEE_DEVICE_TYPE_THERMOSTAT) // 温控设备
    {
        // 温控设备下发温控相关字段
        $thermostatsModeValue = intval($zigbeeDevice['ThermostatsMode'] !== null ? $zigbeeDevice['ThermostatsMode'] : 0);
        $zigbeeDeviceInfo['thermostats_mode'] = $thermostatsModeValue;
        $zigbeeDeviceInfo['temperature'] = strval($zigbeeDevice['Temperature']);
        $zigbeeDeviceInfo['target_temperature'] = strval($zigbeeDevice['TargetTemperature']);
        $zigbeeDeviceInfo['min_temperature'] = strval($zigbeeDevice['MinTemperature']);
        $zigbeeDeviceInfo['max_temperature'] = strval($zigbeeDevice['MaxTemperature']);
        $zigbeeDeviceInfo['temperature_unit'] = intval($zigbeeDevice['TemperatureUnit'] !== null ? $zigbeeDevice['TemperatureUnit'] : 0);
        
        // 根据thermostats_mode的值来决定hvac_mode
        if ($thermostatsModeValue == 0) 
        {
            $zigbeeDeviceInfo['hvac_mode'] = "0"; // thermostats_mode为0，hvac_mode固定为0
        } 
        elseif ($thermostatsModeValue == 1) 
        {
            $zigbeeDeviceInfo['hvac_mode'] = "1"; // thermostats_mode为1，hvac_mode固定为1
        } 
        else 
        {
            // 只有thermostats_mode为2或其他值时才取数据库里面的值
            $zigbeeDeviceInfo['hvac_mode'] = strval($zigbeeDevice['HVACMode'] !== null ? $zigbeeDevice['HVACMode'] : "0");
        }
    }
    return $zigbeeDeviceInfo;
}

//处理zigbee设备信息查询
function getZigbeeDeviceInfo($zigbeeDeviceUUID)
{
    $userData = \util\container\getUserData();
    $zigbeeDevice = \resident\model\getZigbeeDeviceByUUID($zigbeeDeviceUUID);
    if (!$zigbeeDevice) 
    {
        return null;
    }
    
    // 获取关联的室内机设备信息
    $deviceInfo = \common\model\getDeviceInfoByRoleAndUUID($userData['Role'], $zigbeeDevice['DeviceUUID']);
    if (!$deviceInfo) 
    {
        return null;
    }
    
    $deviceData = [];
    $deviceData['function'] = intval(\resident\control\mapZigbeeDeviceTypeToFunction($zigbeeDevice['DeviceType']));
    $deviceData['create_time'] = strval($zigbeeDevice['CreateTime']);
    $deviceData['mac'] = strval($deviceInfo['MAC']);
    
    // zigbee设备在线状态需要同时考虑室内机设备状态和zigbee设备自身状态，只有两个状态都在线时，才算在线
    $deviceOnlineStatus = intval($deviceInfo['Status']);
    $zigbeeOnlineStatus = intval($zigbeeDevice['OnlineStatus']);
    $deviceData['smart_control_online_status'] = ($deviceOnlineStatus && $zigbeeOnlineStatus) ? 1 : 0;
    
    // 构建zigbee设备信息数组
    $zigbeeDeviceInfo = \resident\control\buildZigbeeDeviceInfo($zigbeeDevice);
    $deviceData['zigbee_devices_info'] = array($zigbeeDeviceInfo);
    
    return $deviceData;
} 