<?php

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/define.php';
require_once __DIR__ . '/../app/config/dynamic_config.php';
require_once __DIR__ . '/../app/util/SMProxyManager.php';

use util\smproxy\SMProxyManager;

/**
 * SMProxy测试类
 * 
 * 验证SMProxy集成的各项功能是否正常工作
 */
class SMProxyTest
{
    private $config;
    private $manager;
    
    public function __construct()
    {
        // 测试配置
        $this->config = [
            'enabled' => true,
            'host' => '127.0.0.1',
            'port' => 3366,
            'username' => 'root',
            'password' => '123456',
            'charset' => 'utf8mb4',
            'timeout' => 5,
            'health_check' => [
                'enabled' => true,
                'interval' => 30,
            ],
            'failover' => [
                'enabled' => true,
                'fallback_to_direct' => true,
            ],
        ];
        
        echo "=== SMProxy集成测试开始 ===\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        try {
            $this->testManagerInitialization();
            $this->testHealthCheck();
            $this->testConnection();
            $this->testSQLQueries();
            $this->testPerformance();
            $this->testStats();
            $this->testConfigReload();
            $this->testFailover();
            
            echo "\n=== 所有SMProxy集成测试通过 ===\n";
            
        } catch (Exception $e) {
            echo "\n=== SMProxy集成测试失败: " . $e->getMessage() . " ===\n";
            throw $e;
        } finally {
            $this->cleanup();
        }
    }
    
    /**
     * 测试管理器初始化
     */
    public function testManagerInitialization()
    {
        echo "\n1. 测试SMProxy管理器初始化...\n";
        
        $this->manager = SMProxyManager::getInstance($this->config);
        $this->assert($this->manager !== null, "SMProxy管理器应该成功初始化");
        
        $config = $this->manager->getConfig();
        $this->assert($config['host'] === '127.0.0.1', "配置应该正确加载");
        $this->assert($config['port'] === 3366, "端口配置应该正确");
        
        echo "   ✓ SMProxy管理器初始化成功\n";
    }
    
    /**
     * 测试健康检查
     */
    public function testHealthCheck()
    {
        echo "\n2. 测试SMProxy健康检查...\n";
        
        // 注意：这个测试需要SMProxy服务实际运行
        try {
            $isHealthy = $this->manager->checkHealth();
            
            if ($isHealthy) {
                echo "   ✓ SMProxy健康检查通过\n";
            } else {
                echo "   ⚠ SMProxy健康检查失败（可能SMProxy服务未启动）\n";
            }
        } catch (Exception $e) {
            echo "   ⚠ SMProxy健康检查异常: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试连接获取
     */
    public function testConnection()
    {
        echo "\n3. 测试SMProxy连接获取...\n";
        
        try {
            $connection = $this->manager->getConnection();
            $this->assert($connection instanceof PDO, "应该返回PDO连接对象");
            
            echo "   ✓ SMProxy连接获取成功\n";
            
            // 保存连接用于后续测试
            $this->testConnection = $connection;
            
        } catch (Exception $e) {
            echo "   ⚠ SMProxy连接获取失败: " . $e->getMessage() . "\n";
            echo "   这可能是因为SMProxy服务未启动或配置错误\n";
            
            // 跳过后续需要连接的测试
            $this->testConnection = null;
        }
    }
    
    /**
     * 测试SQL查询
     */
    public function testSQLQueries()
    {
        echo "\n4. 测试SQL查询执行...\n";
        
        if (!$this->testConnection) {
            echo "   ⚠ 跳过SQL查询测试（无可用连接）\n";
            return;
        }
        
        try {
            // 测试简单查询
            $result = $this->testConnection->query('SELECT 1 as test, NOW() as current_time')->fetch();
            $this->assert($result['test'] == 1, "简单查询应该返回正确结果");
            
            // 测试版本查询
            $result = $this->testConnection->query('SELECT VERSION() as version')->fetch();
            $this->assert(!empty($result['version']), "版本查询应该返回结果");
            
            // 测试状态查询
            $result = $this->testConnection->query("SHOW STATUS LIKE 'Threads_connected'")->fetch();
            $this->assert($result !== false, "状态查询应该返回结果");
            
            echo "   ✓ SQL查询执行测试通过\n";
            
        } catch (Exception $e) {
            echo "   ✗ SQL查询执行失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 测试性能
     */
    public function testPerformance()
    {
        echo "\n5. 测试SMProxy性能...\n";
        
        if (!$this->testConnection) {
            echo "   ⚠ 跳过性能测试（无可用连接）\n";
            return;
        }
        
        try {
            $results = $this->manager->performanceTest(5);
            
            $this->assert(isset($results['test_count']), "性能测试应该返回测试次数");
            $this->assert(isset($results['success_count']), "性能测试应该返回成功次数");
            $this->assert(isset($results['avg_duration_ms']), "性能测试应该返回平均响应时间");
            
            echo "   ✓ 性能测试完成\n";
            echo "   测试次数: {$results['test_count']}\n";
            echo "   成功次数: {$results['success_count']}\n";
            echo "   成功率: {$results['success_rate']}%\n";
            echo "   平均响应时间: {$results['avg_duration_ms']}ms\n";
            
        } catch (Exception $e) {
            echo "   ✗ 性能测试失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 测试统计信息
     */
    public function testStats()
    {
        echo "\n6. 测试SMProxy统计信息...\n";
        
        try {
            $stats = $this->manager->getStats();
            
            $this->assert(isset($stats['healthy']), "统计信息应该包含健康状态");
            
            if ($stats['healthy']) {
                $this->assert(isset($stats['threads_connected']), "统计信息应该包含连接数");
                echo "   ✓ 统计信息获取成功\n";
                echo "   当前连接数: " . ($stats['threads_connected'] ?? 'N/A') . "\n";
                echo "   最大连接数: " . ($stats['max_connections'] ?? 'N/A') . "\n";
                echo "   连接使用率: " . ($stats['connection_utilization'] ?? 'N/A') . "%\n";
            } else {
                echo "   ⚠ SMProxy不健康，统计信息可能不完整\n";
            }
            
        } catch (Exception $e) {
            echo "   ✗ 统计信息获取失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 测试配置重新加载
     */
    public function testConfigReload()
    {
        echo "\n7. 测试配置重新加载...\n";
        
        try {
            $newConfig = [
                'timeout' => 10,
                'health_check' => [
                    'interval' => 60,
                ]
            ];
            
            $result = $this->manager->reloadConfig($newConfig);
            
            $updatedConfig = $this->manager->getConfig();
            $this->assert($updatedConfig['timeout'] === 10, "配置应该成功更新");
            
            echo "   ✓ 配置重新加载成功\n";
            
        } catch (Exception $e) {
            echo "   ✗ 配置重新加载失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 测试故障转移
     */
    public function testFailover()
    {
        echo "\n8. 测试故障转移机制...\n";
        
        try {
            // 模拟连接失败的情况
            $badConfig = [
                'host' => '127.0.0.1',
                'port' => 9999, // 不存在的端口
                'username' => 'root',
                'password' => '123456',
                'timeout' => 1,
            ];
            
            $badManager = SMProxyManager::getInstance($badConfig);
            
            try {
                $connection = $badManager->getConnection();
                echo "   ⚠ 预期连接失败，但实际成功了\n";
            } catch (Exception $e) {
                echo "   ✓ 正确处理连接失败情况\n";
                echo "   错误信息: " . $e->getMessage() . "\n";
            }
            
        } catch (Exception $e) {
            echo "   ✗ 故障转移测试失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * 清理资源
     */
    public function cleanup()
    {
        echo "\n9. 清理测试资源...\n";
        
        if ($this->testConnection) {
            $this->testConnection = null;
        }
        
        echo "   ✓ 测试资源清理完成\n";
    }
    
    /**
     * 断言方法
     */
    private function assert($condition, $message)
    {
        if (!$condition) {
            throw new Exception("断言失败: {$message}");
        }
    }
}

/**
 * 测试slim-webman集成
 */
function testSlimWebmanIntegration()
{
    echo "\n=== 测试slim-webman集成 ===\n";
    
    try {
        // 模拟配置加载
        if (!function_exists('config')) {
            function config($key, $default = null) {
                $configs = [
                    'smproxy' => [
                        'enabled' => false, // 默认禁用，避免测试时连接真实服务
                        'host' => '127.0.0.1',
                        'port' => 3366,
                        'username' => 'root',
                        'password' => '123456',
                    ]
                ];
                
                return $configs[$key] ?? $default;
            }
        }
        
        // 测试配置加载
        $config = config('smproxy', []);
        echo "配置加载测试: " . (empty($config) ? "失败" : "成功") . "\n";
        
        // 测试管理器创建
        $manager = SMProxyManager::getInstance($config);
        echo "管理器创建测试: " . ($manager ? "成功" : "失败") . "\n";
        
        echo "slim-webman集成测试完成\n";
        
    } catch (Exception $e) {
        echo "slim-webman集成测试失败: " . $e->getMessage() . "\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    try {
        // 基础功能测试
        $test = new SMProxyTest();
        $test->runAllTests();
        
        // 集成测试
        testSlimWebmanIntegration();
        
    } catch (Exception $e) {
        echo "SMProxy测试异常: " . $e->getMessage() . "\n";
        echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
        exit(1);
    }
}
