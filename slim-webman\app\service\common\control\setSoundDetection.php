<?php

namespace  common\control;
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";
require_once __DIR__ . "/../../common/model/communityInfo.php";

function setSoundDetection($request, $response)
{
    $userData = \util\container\getUserData();
    $postData = $request->getParsedBody();
    $enableSoundDetection = $postData['enable_sound_detection'];
    $soundTypeJson = $postData['sound_type'];
    $soundTypeArr = json_decode($soundTypeJson, true);

    // {"enable_gun_shot": 1, "enable_dog_barking": 1, "enable_baby_crying": 1, "enable_glass_breaking": 1, "enable_siren": 1}
    $soundTypeMap = [
        'enable_gun_shot' => 0,
        'enable_dog_barking' => 1,
        'enable_baby_crying' => 2,
        'enable_glass_breaking' => 3,
        'enable_siren' => 4
    ];
    $bitArr = [];
    foreach ($soundTypeMap as $key => $val) {
        if (intval($soundTypeArr[$key]) === 1) {
            array_push($bitArr, $val);
        }
    }
    $soundType = \util\utility\setSwitchHandle($bitArr);
    $role = $userData['Role'];
    if ($role == ROLE_TYPE_COMMUNITY_PM) {
        \common\model\updateCommunitySoundDetectionStatus($enableSoundDetection, $soundType, $userData['MngID']);
    } else {
        \common\model\updatePersonalSoundDetectionStatus($enableSoundDetection, $soundType, $userData['Account']);
    }

    setDetectionNotify($userData);

    return \util\response\setResponseMessage($response);
}