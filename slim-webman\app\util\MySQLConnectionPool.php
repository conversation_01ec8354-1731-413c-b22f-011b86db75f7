<?php

namespace util\pool;

use PDO;
use PDOException;
use SplQueue;
use Workerman\Timer;
use Exception;

require_once __DIR__ . '/medoo.php';
require_once __DIR__ . '/log.php';

/**
 * MySQL连接池管理器
 * 提供高效的数据库连接复用机制
 */
class MySQLConnectionPool
{
    /**
     * 连接池实例（单例）
     */
    private static $instance = null;

    /**
     * 空闲连接队列
     */
    private $idleConnections;

    /**
     * 活跃连接数组 [connection_id => connection_info]
     */
    private $activeConnections = [];

    /**
     * 连接池配置
     */
    private $config;

    /**
     * 健康检查定时器ID
     */
    private $healthCheckTimer = null;

    /**
     * 连接ID计数器
     */
    private $connectionIdCounter = 0;

    /**
     * 连接池统计信息
     */
    private $stats = [
        'total_created' => 0,
        'total_destroyed' => 0,
        'current_active' => 0,
        'current_idle' => 0,
        'peak_active' => 0,
        'connection_errors' => 0,
        'health_check_failures' => 0
    ];

    private function __construct($config)
    {
        $this->config = $config;
        $this->idleConnections = new SplQueue();
        $this->initializePool();
        $this->startHealthCheck();
    }

    /**
     * 获取连接池实例
     */
    public static function getInstance($config = null)
    {
        if (self::$instance === null) {
            if ($config === null) {
                throw new Exception('连接池配置不能为空');
            }
            self::$instance = new self($config);
        }
        return self::$instance;
    }

    /**
     * 初始化连接池
     */
    private function initializePool()
    {
        $minConnections = $this->config['min_connections'] ?? 5;
        
        for ($i = 0; $i < $minConnections; $i++) {
            try {
                $connection = $this->createConnection();
                if ($connection) {
                    $this->idleConnections->enqueue($connection);
                    $this->stats['current_idle']++;
                }
            } catch (Exception $e) {
                $this->logError("初始化连接池失败: " . $e->getMessage());
                $this->stats['connection_errors']++;
            }
        }
        
        $this->logInfo("连接池初始化完成，创建了 {$this->stats['current_idle']} 个连接");
    }

    /**
     * 创建新的数据库连接
     */
    private function createConnection()
    {
        $connectionId = ++$this->connectionIdCounter;
        
        try {
            // 创建Medoo实例（内部会创建PDO连接）
            $medoo = new \util\medoo\Medoo([
                'database_name' => 'AKCS',
                'server' => AKCS_DATABASEIP,
                'port' => AKCS_DATABASEPORT,
                'username' => DATABASEUSER,
                'password' => DATABASEPWD
            ]);

            // 从Medoo实例中获取PDO连接
            $pdo = $medoo->pdo;

            $connectionInfo = [
                'id' => $connectionId,
                'pdo' => $pdo,
                'medoo' => $medoo,
                'created_at' => time(),
                'last_used' => time(),
                'is_healthy' => true
            ];

            $this->stats['total_created']++;
            $this->logDebug("创建新连接 ID: {$connectionId}");
            
            return $connectionInfo;
            
        } catch (PDOException $e) {
            $this->logError("创建数据库连接失败: " . $e->getMessage());
            $this->stats['connection_errors']++;
            throw $e;
        }
    }

    /**
     * 获取连接
     */
    public function getConnection()
    {
        // 首先尝试从空闲连接池获取
        if (!$this->idleConnections->isEmpty()) {
            $connection = $this->idleConnections->dequeue();
            $this->stats['current_idle']--;
            
            // 检查连接是否仍然有效
            if ($this->isConnectionHealthy($connection)) {
                $connection['last_used'] = time();
                $this->activeConnections[$connection['id']] = $connection;
                $this->stats['current_active']++;
                $this->stats['peak_active'] = max($this->stats['peak_active'], $this->stats['current_active']);
                
                $this->logDebug("从连接池获取连接 ID: {$connection['id']}");
                return $connection;
            } else {
                // 连接无效，销毁并创建新连接
                $this->destroyConnection($connection);
            }
        }

        // 如果没有空闲连接，检查是否可以创建新连接
        $totalConnections = $this->stats['current_active'] + $this->stats['current_idle'];
        $maxConnections = $this->config['max_connections'] ?? 20;
        
        if ($totalConnections < $maxConnections) {
            try {
                $connection = $this->createConnection();
                $connection['last_used'] = time();
                $this->activeConnections[$connection['id']] = $connection;
                $this->stats['current_active']++;
                $this->stats['peak_active'] = max($this->stats['peak_active'], $this->stats['current_active']);
                
                $this->logDebug("创建新连接 ID: {$connection['id']}");
                return $connection;
                
            } catch (Exception $e) {
                $this->logError("无法创建新连接: " . $e->getMessage());
                throw new Exception("连接池无法提供连接: " . $e->getMessage());
            }
        }

        throw new Exception("连接池已满，无法获取新连接");
    }

    /**
     * 归还连接到连接池
     */
    public function releaseConnection($connection)
    {
        if (!isset($connection['id'])) {
            $this->logError("尝试归还无效连接");
            return false;
        }

        $connectionId = $connection['id'];
        
        if (!isset($this->activeConnections[$connectionId])) {
            $this->logError("尝试归还未知连接 ID: {$connectionId}");
            return false;
        }

        // 从活跃连接中移除
        unset($this->activeConnections[$connectionId]);
        $this->stats['current_active']--;

        // 检查连接是否健康
        if ($this->isConnectionHealthy($connection)) {
            // 检查空闲超时
            $idleTimeout = $this->config['idle_timeout'] ?? 300;
            $connectionAge = time() - $connection['created_at'];
            
            if ($connectionAge < $idleTimeout) {
                // 连接仍然有效，归还到空闲池
                $this->idleConnections->enqueue($connection);
                $this->stats['current_idle']++;
                $this->logDebug("归还连接到连接池 ID: {$connectionId}");
                return true;
            }
        }

        // 连接无效或超时，销毁连接
        $this->destroyConnection($connection);
        return true;
    }

    /**
     * 检查连接健康状态
     */
    private function isConnectionHealthy($connection)
    {
        if (!isset($connection['pdo']) || !($connection['pdo'] instanceof PDO)) {
            return false;
        }

        try {
            $connection['pdo']->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            $this->logDebug("连接健康检查失败 ID: {$connection['id']}, 错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 销毁连接
     */
    private function destroyConnection($connection)
    {
        if (isset($connection['id'])) {
            $this->logDebug("销毁连接 ID: {$connection['id']}");
        }
        
        // 关闭PDO连接
        if (isset($connection['pdo'])) {
            $connection['pdo'] = null;
        }
        
        // 清理Medoo实例
        if (isset($connection['medoo'])) {
            $connection['medoo'] = null;
        }
        
        $this->stats['total_destroyed']++;
    }

    /**
     * 启动健康检查定时器
     */
    private function startHealthCheck()
    {
        $interval = $this->config['health_check_interval'] ?? 60;
        
        $this->healthCheckTimer = Timer::add($interval, function() {
            $this->performHealthCheck();
        });
        
        $this->logInfo("启动连接池健康检查，间隔: {$interval}秒");
    }

    /**
     * 执行健康检查
     */
    private function performHealthCheck()
    {
        // 检查空闲连接
        $healthyConnections = new SplQueue();
        $unhealthyCount = 0;
        
        while (!$this->idleConnections->isEmpty()) {
            $connection = $this->idleConnections->dequeue();
            $this->stats['current_idle']--;
            
            if ($this->isConnectionHealthy($connection)) {
                $healthyConnections->enqueue($connection);
                $this->stats['current_idle']++;
            } else {
                $this->destroyConnection($connection);
                $unhealthyCount++;
                $this->stats['health_check_failures']++;
            }
        }
        
        $this->idleConnections = $healthyConnections;
        
        // 确保最小连接数
        $minConnections = $this->config['min_connections'] ?? 5;
        $currentTotal = $this->stats['current_active'] + $this->stats['current_idle'];
        
        if ($currentTotal < $minConnections) {
            $needCreate = $minConnections - $currentTotal;
            for ($i = 0; $i < $needCreate; $i++) {
                try {
                    $connection = $this->createConnection();
                    $this->idleConnections->enqueue($connection);
                    $this->stats['current_idle']++;
                } catch (Exception $e) {
                    $this->logError("健康检查时创建连接失败: " . $e->getMessage());
                    break;
                }
            }
        }
        
        if ($unhealthyCount > 0) {
            $this->logInfo("健康检查完成，清理了 {$unhealthyCount} 个无效连接");
        }
    }

    /**
     * 获取连接池统计信息
     */
    public function getStats()
    {
        return array_merge($this->stats, [
            'current_total' => $this->stats['current_active'] + $this->stats['current_idle']
        ]);
    }

    /**
     * 关闭连接池
     */
    public function close()
    {
        // 停止健康检查定时器
        if ($this->healthCheckTimer) {
            Timer::del($this->healthCheckTimer);
            $this->healthCheckTimer = null;
        }

        // 关闭所有空闲连接
        while (!$this->idleConnections->isEmpty()) {
            $connection = $this->idleConnections->dequeue();
            $this->destroyConnection($connection);
        }

        // 关闭所有活跃连接
        foreach ($this->activeConnections as $connection) {
            $this->destroyConnection($connection);
        }

        $this->activeConnections = [];
        $this->stats['current_active'] = 0;
        $this->stats['current_idle'] = 0;
        
        $this->logInfo("连接池已关闭");
    }

    /**
     * 日志记录方法
     */
    private function logInfo($message)
    {
        \util\log\akcsLog::debug("[MySQLConnectionPool][INFO] " . $message);
    }

    private function logDebug($message)
    {
        \util\log\akcsLog::debug("[MySQLConnectionPool][DEBUG] " . $message);
    }

    private function logError($message)
    {
        \util\log\akcsLog::debug("[MySQLConnectionPool][ERROR] " . $message);
    }

    public function __destruct()
    {
        $this->close();
    }
}