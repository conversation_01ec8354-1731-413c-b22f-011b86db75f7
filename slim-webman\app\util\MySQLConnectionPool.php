<?php

namespace util\mysql;

use Worker<PERSON>\Coroutine\Pool;
use Workerman\Coroutine\Context;
use Worker<PERSON>\Coroutine;

require_once __DIR__ . '/log.php';
require_once __DIR__ . '/medoo.php';

/**
 * 基于Workerman官方Pool的MySQL连接池
 * 
 * 使用Workerman官方连接池实现，支持协程自动获取和归还连接
 */
class MySQLConnectionPool
{
    private static $instance = null;
    private static $pool = null;
    private $config;
    
    private function __construct($config = null)
    {
        $this->config = $config ?: $this->getDefaultConfig();
        $this->initializePool();
    }
    
    public static function getInstance($config = null)
    {
        if (self::$instance === null) {
            self::$instance = new self($config);
        }
        return self::$instance;
    }
    
    private function getDefaultConfig()
    {
        return [
            'host' => '127.0.0.1',
            'port' => 3306,
            'database' => 'akcs_db',
            'username' => 'root',
            'password' => '123456',
            'charset' => 'utf8mb4',
            'max_connections' => 20,
            'min_connections' => 5,
            'idle_timeout' => 300,
            'heartbeat_interval' => 60,
            'wait_timeout' => 10,
        ];
    }
    
    private function initializePool()
    {
        if (self::$pool !== null) {
            return;
        }
        
        $poolConfig = [
            'min_connections' => $this->config['min_connections'],
            'idle_timeout' => $this->config['idle_timeout'],
            'heartbeat_interval' => $this->config['heartbeat_interval'],
            'wait_timeout' => $this->config['wait_timeout'],
        ];
        
        \util\log\akcsLog::debug("[MySQLConnectionPool][INFO] 初始化Workerman连接池，最大连接数: {$this->config['max_connections']}");
        
        self::$pool = new Pool($this->config['max_connections'], $poolConfig);
        
        // 设置连接创建器
        self::$pool->setConnectionCreator(function () {
            return $this->createConnection();
        });
        
        // 设置连接销毁器
        self::$pool->setConnectionCloser(function ($connection) {
            $this->closeConnection($connection);
        });
        
        // 设置心跳检测器
        self::$pool->setHeartbeatChecker(function ($connection) {
            return $this->checkConnection($connection);
        });
        
        \util\log\akcsLog::debug("[MySQLConnectionPool][INFO] Workerman连接池初始化完成");
    }
    
    /**
     * 创建数据库连接
     */
    private function createConnection()
    {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}";
            
            $options = [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
                \PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']}",
            ];
            
            // 创建Medoo实例
            $medooConfig = [
                'type' => 'mysql',
                'host' => $this->config['host'],
                'port' => $this->config['port'],
                'database' => $this->config['database'],
                'username' => $this->config['username'],
                'password' => $this->config['password'],
                'charset' => $this->config['charset'],
                'option' => $options
            ];
            
            $medoo = new \util\medoo\Medoo($medooConfig);
            
            // 从Medoo中获取PDO连接，避免重复连接
            $pdo = $medoo->pdo;
            
            \util\log\akcsLog::debug("[MySQLConnectionPool][DEBUG] 创建新的数据库连接");
            
            return [
                'pdo' => $pdo,
                'medoo' => $medoo,
                'created_at' => time(),
            ];
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[MySQLConnectionPool][ERROR] 创建数据库连接失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 关闭数据库连接
     */
    private function closeConnection($connection)
    {
        try {
            if (isset($connection['pdo'])) {
                $connection['pdo'] = null;
            }
            if (isset($connection['medoo'])) {
                $connection['medoo'] = null;
            }
            \util\log\akcsLog::debug("[MySQLConnectionPool][DEBUG] 关闭数据库连接");
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[MySQLConnectionPool][ERROR] 关闭数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查连接健康状态
     */
    private function checkConnection($connection)
    {
        try {
            if (!isset($connection['pdo'])) {
                return false;
            }
            
            $pdo = $connection['pdo'];
            $result = $pdo->query('SELECT 1')->fetch();
            
            return $result !== false;
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[MySQLConnectionPool][ERROR] 连接健康检查失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取连接（自动管理协程上下文）
     */
    public function getConnection()
    {
        try {
            // 检查协程上下文中是否已有连接
            $connection = Context::get('mysql_connection');
            if ($connection) {
                \util\log\akcsLog::debug("[MySQLConnectionPool][DEBUG] 从协程上下文获取连接");
                return $connection;
            }
            
            // 从连接池获取连接
            $connection = self::$pool->get();
            
            // 保存到协程上下文
            Context::set('mysql_connection', $connection);
            
            // 设置协程结束时自动归还连接
            Coroutine::defer(function () use ($connection) {
                self::$pool->put($connection);
                Context::set('mysql_connection', null);
                \util\log\akcsLog::debug("[MySQLConnectionPool][DEBUG] 协程结束，自动归还连接");
            });
            
            \util\log\akcsLog::debug("[MySQLConnectionPool][DEBUG] 从连接池获取连接");
            return $connection;
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[MySQLConnectionPool][ERROR] 获取连接失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 手动归还连接（通常不需要调用，协程结束时自动归还）
     */
    public function putConnection($connection)
    {
        try {
            self::$pool->put($connection);
            Context::set('mysql_connection', null);
            \util\log\akcsLog::debug("[MySQLConnectionPool][DEBUG] 手动归还连接");
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[MySQLConnectionPool][ERROR] 归还连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取连接池统计信息
     */
    public function getStats()
    {
        try {
            $connectionCount = self::$pool->getConnectionCount();
            
            return [
                'total_connections' => $connectionCount,
                'max_connections' => $this->config['max_connections'],
                'min_connections' => $this->config['min_connections'],
                'config' => $this->config,
            ];
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[MySQLConnectionPool][ERROR] 获取统计信息失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 关闭连接池
     */
    public function close()
    {
        try {
            if (self::$pool) {
                self::$pool->closeConnections();
                \util\log\akcsLog::debug("[MySQLConnectionPool][INFO] 连接池已关闭");
            }
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[MySQLConnectionPool][ERROR] 关闭连接池失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查连接池是否健康
     */
    public function isHealthy()
    {
        try {
            $connection = $this->getConnection();
            return $this->checkConnection($connection);
        } catch (\Exception $e) {
            return false;
        }
    }
}
