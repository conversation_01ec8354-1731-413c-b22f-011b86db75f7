<?php

namespace common\model;

function getUserInfoByMobileNumber($mobileNumber)
{
    return \util\container\medooDb()->get("PersonalAccountUserInfo", ["Email", "MobileNumber", "Passwd", "AppMainUserAccount", "AppLastLoginUserAccount", "UUID"], ["MobileNumber" => $mobileNumber]);
}

function getUserInfoByMainAccount($mainAccount)
{
    return \util\container\medooDb()->get("PersonalAccountUserInfo",  ["Email", "MobileNumber", "Passwd", "AppMainUserAccount", "AppLastLoginUserAccount", "UUID"], ["AppMainUserAccount" => $mainAccount]);
}

function getUserInfoByUUID($uuid)
{
    return \util\container\medooDb()->get("PersonalAccountUserInfo",  ["Email", "MobileNumber", "Passwd", "AppMainUserAccount", "AppLastLoginUserAccount", "UUID"], ["UUID" => $uuid]);
}

function updateLastLoginUserByUUID($appLastLoginUserAccount, $uuid)
{
    \util\container\medooDb()->update("PersonalAccountUserInfo", ["AppLastLoginUserAccount" => $appLastLoginUserAccount], ["UUID" => $uuid]);
}