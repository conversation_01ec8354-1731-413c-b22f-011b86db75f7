<?php

namespace resident\control;

require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";

function setNfcCode($request, $response)
{   
    $resultToken = \util\container\getUserData();
   
    $postDatas = $request->getParsedBody();
    $randNFCcode = "";
    if ($postDatas['enable'] == '1') {
        \util\common\getNFCCode($randNFCcode);
    }

    \common\model\UpdateNFCCode($randNFCcode, $resultToken['UserAccount']);

    $user = \util\container\getUserData();
    if ($user['Role'] === ROLE_TYPE_PERSONNAL_MASTER) {
        webPersonalModifyNotify(WEB_PER_UPDATE_RF, $user['Account']);
    } elseif ($user['Role'] === ROLE_TYPE_PERSONNAL_SLAVE) {
        webPersonalModifyNotify(WEB_PER_UPDATE_RF, $user['Account']);
    } elseif ($user['Role'] == ROLE_TYPE_COMMUNITY_MASTER) {  //社区主账号
        webCommunityModifyNotify(WEB_COMM_UPDATE_RF, $user['Account'], $mac = "", $user['ParentID'], $user['UnitID']);
        WebCommunityAccountModifyNotify($user['ParentID'], $resultToken['Account'], 0);
    } elseif ($user['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) { //社区从账号
        $accounts[0] = $user['Account']; // 需要node查询设备(刷apt door)
        $accounts[1] = $user['UserAccount']; // 需要更新当前账号的数据版本
        webCommunityModifyNotify(WEB_COMM_UPDATE_RF, $user['Account'], $mac = "", $user['MngID'], $user['UnitID']);
        WebCommunityAccountModifyNotify($user['MngID'], $accounts, 0);
    } elseif ($user['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        WebCommunityAccountModifyNotify($user['ParentID'], $resultToken['Account'], 0);
    }

    $datas = [
        'nfccode' => $randNFCcode
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}