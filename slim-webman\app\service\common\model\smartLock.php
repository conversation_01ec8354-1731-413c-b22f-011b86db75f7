<?php

namespace common\model;
use PDO;

function SmartLockMqttAuthCheck($mac, $clientId, $password)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT MqttPwd FROM SmartLock WHERE UUID = :uuid and MAC = :mac");
    $sth->bindParam(':uuid', $clientId, PDO::PARAM_STR);
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    $lockInfo = $sth->fetch(PDO::FETCH_ASSOC);
    if($lockInfo && (\util\utility\passwdDecode($lockInfo['MqttPwd']) == $password))
    {
        return true;
    }

    return false;
}