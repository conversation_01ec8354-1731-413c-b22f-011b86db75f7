<?php

namespace support\bootstrap;

use Workerman\Worker;
use Webman\Bootstrap;
use util\pool\MySQLConnectionPool;
use util\pool\RedisConnectionPool;

require_once __DIR__ . '/../../app/util/log.php';
require_once __DIR__ . '/../../app/util/MySQLConnectionPool.php';
require_once __DIR__ . '/../../app/util/RedisConnectionPool.php';

/**
 * 数据库和Redis连接池Bootstrap启动器
 *
 * 在webman框架的worker进程启动时初始化MySQL和Redis连接池
 * 确保每个worker进程都有独立的连接池实例
 */
class ConnectionPool implements Bootstrap
{
    /**
     * MySQL连接池实例存储
     */
    private static $mysqlPoolInstance = null;

    /**
     * Redis连接池实例存储
     */
    private static $redisPoolInstance = null;
    
    /**
     * 启动连接池
     * 
     * @param Worker|null $worker Worker进程实例
     * @return void
     */
    public static function start(?Worker $worker)
    {
        // 只在worker进程中启动连接池
        if (!$worker) {
            return;
        }
        
        try {
            // 获取连接池配置
            $mysqlConfig = self::getMySQLPoolConfig();
            $redisConfig = self::getRedisPoolConfig();

            // 记录启动信息
            $pid = function_exists('posix_getpid') ? posix_getpid() : getmypid();
            self::logInfo("正在启动数据库和Redis连接池 (Worker PID: " . $pid . ")");

            // 初始化MySQL连接池
            self::$mysqlPoolInstance = MySQLConnectionPool::getInstance($mysqlConfig);

            // 初始化Redis连接池
            self::$redisPoolInstance = RedisConnectionPool::getInstance($redisConfig);

            // 注册worker停止时的清理回调
            self::registerShutdownHandler($worker);

            // 记录启动成功信息
            $mysqlStats = self::$mysqlPoolInstance->getStats();
            $redisStats = self::$redisPoolInstance->getStats();
            self::logInfo("MySQL连接池启动成功,初始连接数: {$mysqlStats['current_idle']}");
            self::logInfo("Redis连接池启动成功,初始连接数: {$redisStats['current_idle']}");

        } catch (\Exception $e) {
            self::logError("连接池启动失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取MySQL连接池配置
     *
     * @return array
     */
    private static function getMySQLPoolConfig()
    {
        return [
            'min_connections' => 5,
            'max_connections' => 20,
            'connection_timeout' => 30,
            'idle_timeout' => 300,
            'health_check_interval' => 60,
            'retry_attempts' => 3,
            'debug' => false,
            'enable_stats' => true,
            'stats_log_interval' => 300,
        ];
    }

    /**
     * 获取Redis连接池配置
     *
     * @return array
     */
    private static function getRedisPoolConfig()
    {
        return [
            'min_connections' => 3,
            'max_connections' => 10,
            'connection_timeout' => 5,
            'idle_timeout' => 300,
            'health_check_interval' => 60,
            'retry_attempts' => 3,
            'debug' => false,
            'enable_stats' => true,
            'stats_log_interval' => 300,
        ];
    }
    
    /**
     * 注册worker停止时的清理回调
     * 
     * @param Worker $worker
     */
    private static function registerShutdownHandler($worker)
    {
        // 注册worker停止回调
        $worker->onWorkerStop = function() {
            self::shutdown();
        };
        
        // 注册PHP脚本结束回调
        register_shutdown_function([self::class, 'shutdown']);
    }
    
    /**
     * 获取MySQL连接池实例
     *
     * @return MySQLConnectionPool|null
     */
    public static function getMySQLPool()
    {
        return self::$mysqlPoolInstance;
    }

    /**
     * 获取Redis连接池实例
     *
     * @return RedisConnectionPool|null
     */
    public static function getRedisPool()
    {
        return self::$redisPoolInstance;
    }

    /**
     * 获取数据库连接
     *
     * @return array 包含PDO和Medoo实例的连接信息
     * @throws \Exception
     */
    public static function getConnection()
    {
        if (!self::$mysqlPoolInstance) {
            throw new \Exception('MySQL连接池未初始化');
        }

        return self::$mysqlPoolInstance->getConnection();
    }

    /**
     * 获取Redis连接
     *
     * @return array 包含Redis实例的连接信息
     * @throws \Exception
     */
    public static function getRedisConnection()
    {
        if (!self::$redisPoolInstance) {
            throw new \Exception('Redis连接池未初始化');
        }

        return self::$redisPoolInstance->getConnection();
    }

    /**
     * 归还数据库连接
     *
     * @param array $connection 连接信息
     * @return bool
     */
    public static function releaseConnection($connection)
    {
        if (!self::$mysqlPoolInstance) {
            return false;
        }

        return self::$mysqlPoolInstance->releaseConnection($connection);
    }

    /**
     * 归还Redis连接
     *
     * @param array $connection 连接信息
     * @return bool
     */
    public static function releaseRedisConnection($connection)
    {
        if (!self::$redisPoolInstance) {
            return false;
        }

        return self::$redisPoolInstance->releaseConnection($connection);
    }

    /**
     * 获取MySQL连接池统计信息
     *
     * @return array
     */
    public static function getStats()
    {
        if (!self::$mysqlPoolInstance) {
            return [];
        }

        return self::$mysqlPoolInstance->getStats();
    }

    /**
     * 获取Redis连接池统计信息
     *
     * @return array
     */
    public static function getRedisStats()
    {
        if (!self::$redisPoolInstance) {
            return [];
        }

        return self::$redisPoolInstance->getStats();
    }
    
    /**
     * 关闭连接池
     */
    public static function shutdown()
    {
        if (self::$mysqlPoolInstance) {
            self::logInfo("正在关闭MySQL连接池");
            self::$mysqlPoolInstance->close();
            self::$mysqlPoolInstance = null;
            self::logInfo("MySQL连接池已关闭");
        }

        if (self::$redisPoolInstance) {
            self::logInfo("正在关闭Redis连接池");
            self::$redisPoolInstance->close();
            self::$redisPoolInstance = null;
            self::logInfo("Redis连接池已关闭");
        }
    }

    /**
     * 检查连接池健康状态
     *
     * @return bool
     */
    public static function isHealthy()
    {
        $mysqlHealthy = false;
        $redisHealthy = false;

        if (self::$mysqlPoolInstance) {
            try {
                $stats = self::$mysqlPoolInstance->getStats();
                $mysqlHealthy = $stats['current_total'] > 0;
            } catch (\Exception $e) {
                self::logError("检查MySQL连接池健康状态失败: " . $e->getMessage());
            }
        }

        if (self::$redisPoolInstance) {
            try {
                $stats = self::$redisPoolInstance->getStats();
                $redisHealthy = $stats['current_total'] > 0;
            } catch (\Exception $e) {
                self::logError("检查Redis连接池健康状态失败: " . $e->getMessage());
            }
        }

        return $mysqlHealthy && $redisHealthy;
    }
    

    
    /**
     * 记录信息日志
     *
     * @param string $message
     */
    private static function logInfo($message)
    {
        \util\log\akcsLog::debug("[ConnectionPool][INFO] " . $message);
    }

    /**
     * 记录错误日志
     *
     * @param string $message
     */
    private static function logError($message)
    {
        \util\log\akcsLog::debug("[ConnectionPool][ERROR] " . $message);
    }
}