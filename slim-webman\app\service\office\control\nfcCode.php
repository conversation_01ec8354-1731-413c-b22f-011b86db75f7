<?php

namespace office\control;

require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";

function setOfficeNfcCode($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();
 
    $randNFCcode = "";
    if ($postDatas['enable'] == '1') {
        \util\common\getNFCCode($randNFCcode);
    }

    \common\model\UpdateNFCCode($randNFCcode, $userConf['UserAccount']);

    WebOfficeAccountModifyNotify($userConf['ParentID'], $userConf['UserAccount']);

    $datas = [
        'nfccode' => $randNFCcode
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}
