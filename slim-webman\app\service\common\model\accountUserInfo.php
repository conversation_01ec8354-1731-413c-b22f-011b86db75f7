<?php

namespace common\model;

function getAccountUserInfoByMainAccount($appMainUserAccount)
{
    return \util\container\medooDb()->get("AccountUserInfo", ["LoginAccount", "Email", "Passwd", "AppLastLoginUserAccount", "IsLink", "UUID"], ["AppMainUserAccount" => $appMainUserAccount]);
}

function getAccountUserInfoByUUID($uuid)
{
    return \util\container\medooDb()->get("AccountUserInfo", ["LoginAccount", "Email", "Passwd", "AppLastLoginUserAccount", "AppMainUserAccount", "IsLink"], ["UUID" => $uuid]);
}

function updateAccountLastLoginUserByUUID($appLastLoginUserAccount, $uuid)
{
    \util\container\medooDb()->update("AccountUserInfo", ["AppLastLoginUserAccount" => $appLastLoginUserAccount],  ["UUID" => $uuid]);
}

function getAccountUserInfoByEmail($email)
{
    return \util\container\medooDb()->get("AccountUserInfo", ["LoginAccount", "UUID", "Passwd", "AppLastLoginUserAccount", "AppMainUserAccount", "IsLink"], ["Email" => $email]);
}

function getAccountUserInfoByLoginAccount($loginAccount)
{
    return \util\container\medooDb()->get("AccountUserInfo", ["LoginAccount", "Email", "UUID", "Passwd", "AppLastLoginUserAccount", "AppMainUserAccount", "IsLink"], ["LoginAccount" => $loginAccount]);
}

function getAccountUserInfoByPhone($phone)
{
    return \util\container\medooDb()->get("AccountUserInfo", ["LoginAccount", "Email", "UUID", "Passwd", "AppLastLoginUserAccount", "AppMainUserAccount", "IsLink"], ["Phone" => $phone]);
}



