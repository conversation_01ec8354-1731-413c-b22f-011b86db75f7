<?php

namespace  resident\control;

require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../../resident/model/personalAccountCnf.php";

function setBleConf($request, $response)
{
    $postDatas = $request->getParsedBody();
    $type = @$postDatas['opendoor_type'];
    $userConf = \util\container\getUserData();

    $randBLEcode = "";
    if ($postDatas['enable'] == '1') {
        \util\common\getBLECode($randBLEcode);
    }

    \common\model\UpdateBLECode($randBLEcode, $userConf['UserAccount'], $type);

    if ($userConf['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userConf['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        webPersonalModifyNotify(WEB_PER_UPDATE_RF, $userConf['Account']);
    } elseif ($userConf['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        webCommunityModifyNotify(WEB_COMM_UPDATE_RF, $userConf['Account'], $mac = "", $userConf['MngID'], 0); //旧社区刷RfKeyFiles
        WebCommunityAccountModifyNotify($userConf['MngID'], $userConf['UserAccount'], 0); //新社区刷usermeta
    } elseif ($userConf['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        $accounts[0] = $userConf['Account']; // 需要node查询设备(刷apt door)
        $accounts[1] = $userConf['UserAccount']; // 需要更新当前账号的数据版本
        webCommunityModifyNotify(WEB_COMM_UPDATE_RF, $userConf['Account'], $mac = "", $userConf['MngID'], $userConf['UnitID']);
        WebCommunityAccountModifyNotify($userConf['MngID'], $accounts, 0);
    } else {
        webCommunityModifyNotify(WEB_COMM_UPDATE_RF, $userConf['Account'], $mac = "", $userConf['MngID'], $userConf['UnitID']);
        WebCommunityAccountModifyNotify($userConf['MngID'], $userConf['UserAccount'], 0);
    }

    $datas = [
        'blecode' => $randBLEcode,
        'opendoor_type' => intval($type)
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}