<?php

namespace support\bootstrap;

use Webman\Bootstrap;
use Workerman\Worker;

require_once __DIR__ . '/../../app/util/log.php';

/**
 * 基于Workerman Pool的数据库连接池启动器
 * 
 * 在Worker进程启动时初始化基于Workerman官方Pool的连接池
 */
class DatabasePool implements Bootstrap
{
    private static $mysqlPoolInstance = null;
    private static $redisPoolInstance = null;
    
    /**
     * Worker进程启动时调用
     */
    public static function start(?Worker $worker)
    {
        // 只在worker进程中启动连接池
        if (!$worker) {
            return;
        }
        
        try {
            $pid = function_exists('posix_getpid') ? posix_getpid() : getmypid();
            self::logInfo("正在启动Workerman连接池 (Worker PID: " . $pid . ")");
            
            // 初始化MySQL连接池
            self::initializeMySQLPool();
            
            // 初始化Redis连接池
            self::initializeRedisPool();
            
            self::logInfo("Workerman连接池启动完成");
            
        } catch (\Exception $e) {
            self::logError("Workerman连接池启动失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 初始化MySQL连接池
     */
    private static function initializeMySQLPool()
    {
        try {
            require_once __DIR__ . '/../../app/util/MySQLConnectionPool.php';
            
            $config = self::getMySQLConfig();
            self::$mysqlPoolInstance = \util\mysql\MySQLConnectionPool::getInstance($config);
            
            self::logInfo("Workerman MySQL连接池初始化完成");
            
        } catch (\Exception $e) {
            self::logError("Workerman MySQL连接池初始化失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 初始化Redis连接池
     */
    private static function initializeRedisPool()
    {
        try {
            require_once __DIR__ . '/../../app/util/RedisConnectionPool.php';
            
            $config = self::getRedisConfig();
            self::$redisPoolInstance = \util\redis\RedisConnectionPool::getInstance($config);
            
            self::logInfo("Workerman Redis连接池初始化完成");
            
        } catch (\Exception $e) {
            self::logError("Workerman Redis连接池初始化失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取MySQL连接（兼容旧接口）
     */
    public static function getConnection()
    {
        if (self::$mysqlPoolInstance) {
            return self::$mysqlPoolInstance->getConnection();
        }
        return null;
    }
    
    /**
     * 获取Redis连接
     */
    public static function getRedisConnection()
    {
        if (self::$redisPoolInstance) {
            return self::$redisPoolInstance->getConnection();
        }
        return null;
    }
    
    /**
     * 获取MySQL连接池统计信息
     */
    public static function getStats()
    {
        $stats = [];
        
        if (self::$mysqlPoolInstance) {
            $stats['mysql'] = self::$mysqlPoolInstance->getStats();
        }
        
        if (self::$redisPoolInstance) {
            $stats['redis'] = self::$redisPoolInstance->getStats();
        }
        
        return $stats;
    }
    
    /**
     * 获取MySQL配置
     */
    private static function getMySQLConfig()
    {
        return [
            'host' => '127.0.0.1',
            'port' => 3306,
            'database' => 'akcs_db',
            'username' => 'root',
            'password' => '123456',
            'charset' => 'utf8mb4',
            'max_connections' => 20,
            'min_connections' => 5,
            'idle_timeout' => 300,
            'heartbeat_interval' => 60,
            'wait_timeout' => 10,
        ];
    }
    
    /**
     * 获取Redis配置
     */
    private static function getRedisConfig()
    {
        return [
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'database' => 0,
            'timeout' => 5,
            'max_connections' => 20,
            'min_connections' => 5,
            'idle_timeout' => 300,
            'heartbeat_interval' => 60,
            'wait_timeout' => 10,
        ];
    }
    
    /**
     * 记录信息日志
     */
    private static function logInfo($message)
    {
        \util\log\akcsLog::debug("[DatabasePool][INFO] " . $message);
    }
    
    /**
     * 记录错误日志
     */
    private static function logError($message)
    {
        \util\log\akcsLog::debug("[DatabasePool][ERROR] " . $message);
    }
    
    /**
     * 关闭连接池
     */
    public static function close()
    {
        try {
            if (self::$mysqlPoolInstance) {
                self::$mysqlPoolInstance->close();
                self::logInfo("MySQL连接池已关闭");
            }
            
            if (self::$redisPoolInstance) {
                self::$redisPoolInstance->close();
                self::logInfo("Redis连接池已关闭");
            }
            
        } catch (\Exception $e) {
            self::logError("关闭连接池失败: " . $e->getMessage());
        }
    }
}
