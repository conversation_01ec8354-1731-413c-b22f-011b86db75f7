<?php

namespace support\bootstrap;

use Workerman\Worker;
use Webman\Bootstrap;
use util\pool\MySQLConnectionPool;
use util\pool\RedisConnectionPool;
use Workerman\Timer;

require_once __DIR__ . '/../../app/util/MySQLConnectionPool.php';
require_once __DIR__ . '/../../app/util/RedisConnectionPool.php';
require_once __DIR__ . '/../../app/util/log.php';

/**
 * 数据库和Redis连接池Bootstrap启动器
 *
 * 在webman框架的worker进程启动时初始化MySQL和Redis连接池
 * 确保每个worker进程都有独立的连接池实例
 */
class DatabasePool implements Bootstrap
{
    /**
     * MySQL连接池实例存储
     */
    private static $mysqlPoolInstance = null;

    /**
     * Redis连接池实例存储
     */
    private static $redisPoolInstance = null;
    
    /**
     * 统计信息定时器
     */
    private static $statsTimer = null;
    
    /**
     * 启动连接池
     * 
     * @param Worker|null $worker Worker进程实例
     * @return void
     */
    public static function start(?Worker $worker)
    {
        // 只在worker进程中启动连接池
        if (!$worker) {
            return;
        }
        
        try {
            // 获取连接池配置
            $defaultConfig = self::getConnectionPoolDefaultConfig();

            // 记录启动信息
            $pid = function_exists('posix_getpid') ? posix_getpid() : getmypid();
            self::logInfo("正在启动数据库和Redis连接池 (Worker PID: " . $pid . ")");

            // 初始化MySQL连接池
            self::$mysqlPoolInstance = MySQLConnectionPool::getInstance($defaultConfig);

            // 初始化Redis连接池
            self::$redisPoolInstance = RedisConnectionPool::getInstance($defaultConfig);

            // 启动统计信息定时器
            self::startStatsTimer($defaultConfig);

            // 注册worker停止时的清理回调
            self::registerShutdownHandler($worker);

            // 记录启动成功信息
            $mysqlStats = self::$mysqlPoolInstance->getStats();
            $redisStats = self::$redisPoolInstance->getStats();
            self::logInfo("MySQL连接池启动成功, 初始连接数: {$mysqlStats['current_idle']}");
            self::logInfo("Redis连接池启动成功, 初始连接数: {$redisStats['current_idle']}");

        } catch (\Exception $e) {
            self::logError("连接池启动失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取连接池默认配置
     *
     * @return array
     */
    private static function getConnectionPoolDefaultConfig()
    {
        // 获取CPU核心数，用于动态计算连接池大小
        $cpuCount = function_exists('cpu_count') ? cpu_count() : 4;
        $workerCount = ($cpuCount * 2) + 2; // webman的worker进程数

        // 动态计算连接池大小，确保总连接数不超过MySQL限制
        // 假设MySQL max_connections = 151，预留20%给其他应用
        $maxTotalConnections = 120;
        $maxPerWorker = max(2, min(5, intval($maxTotalConnections / $workerCount)));
        $minPerWorker = max(1, intval($maxPerWorker / 2));

        self::logInfo("动态连接池配置 - CPU核心数: {$cpuCount}, Worker进程数: {$workerCount}, 每进程连接数: {$minPerWorker}-{$maxPerWorker}");

        return [
            'min_connections' => $minPerWorker,      // 动态计算最小连接数
            'max_connections' => $maxPerWorker,      // 动态计算最大连接数
            'connection_timeout' => 30,
            'idle_timeout' => 300,
            'health_check_interval' => 60,
            'retry_attempts' => 3,
            'debug' => false,
            'enable_stats' => true,
            'stats_log_interval' => 300,
        ];
    }
    
    /**
     * 启动统计信息定时器
     *
     * @param array $mysqlConfig
     * @param array $redisConfig
     */
    private static function startStatsTimer($defaultConfig)
    {
        $interval = $defaultConfig['stats_log_interval'];

        self::$statsTimer = Timer::add($interval, function() {
            if (self::$mysqlPoolInstance) {
                $mysqlStats = self::$mysqlPoolInstance->getStats();
                self::logInfo("MySQL连接池统计: " . json_encode($mysqlStats, JSON_UNESCAPED_UNICODE));
            }

            if (self::$redisPoolInstance) {
                $redisStats = self::$redisPoolInstance->getStats();
                self::logInfo("Redis连接池统计: " . json_encode($redisStats, JSON_UNESCAPED_UNICODE));
            }
        });

        self::logInfo("启动连接池统计定时器，间隔: {$interval}秒");
    }
    
    /**
     * 注册worker停止时的清理回调
     * 
     * @param Worker $worker
     */
    private static function registerShutdownHandler($worker)
    {
        // 注册worker停止回调
        $worker->onWorkerStop = function() {
            self::shutdown();
        };
        
        // 注册PHP脚本结束回调
        register_shutdown_function([self::class, 'shutdown']);
    }
    
    /**
     * 获取MySQL连接池实例
     *
     * @return MySQLConnectionPool|null
     */
    public static function getMySQLPool()
    {
        return self::$mysqlPoolInstance;
    }

    /**
     * 获取Redis连接池实例
     *
     * @return RedisConnectionPool|null
     */
    public static function getRedisPool()
    {
        return self::$redisPoolInstance;
    }

    /**
     * 获取数据库连接
     *
     * @return array 包含PDO和Medoo实例的连接信息
     * @throws \Exception
     */
    public static function getConnection()
    {
        if (!self::$mysqlPoolInstance) {
            throw new \Exception('MySQL连接池未初始化');
        }

        return self::$mysqlPoolInstance->getConnection();
    }

    /**
     * 获取Redis连接
     *
     * @return array 包含Redis实例的连接信息
     * @throws \Exception
     */
    public static function getRedisConnection()
    {
        if (!self::$redisPoolInstance) {
            throw new \Exception('Redis连接池未初始化');
        }

        return self::$redisPoolInstance->getConnection();
    }

    /**
     * 归还数据库连接
     *
     * @param array $connection 连接信息
     * @return bool
     */
    public static function releaseConnection($connection)
    {
        if (!self::$mysqlPoolInstance) {
            return false;
        }

        return self::$mysqlPoolInstance->releaseConnection($connection);
    }

    /**
     * 归还Redis连接
     *
     * @param array $connection 连接信息
     * @return bool
     */
    public static function releaseRedisConnection($connection)
    {
        if (!self::$redisPoolInstance) {
            return false;
        }

        return self::$redisPoolInstance->releaseConnection($connection);
    }

    /**
     * 获取MySQL连接池统计信息
     *
     * @return array
     */
    public static function getStats()
    {
        if (!self::$mysqlPoolInstance) {
            return [];
        }

        return self::$mysqlPoolInstance->getStats();
    }

    /**
     * 获取Redis连接池统计信息
     *
     * @return array
     */
    public static function getRedisStats()
    {
        if (!self::$redisPoolInstance) {
            return [];
        }

        return self::$redisPoolInstance->getStats();
    }
    
    /**
     * 关闭连接池
     */
    public static function shutdown()
    {
        if (self::$statsTimer) {
            Timer::del(self::$statsTimer);
            self::$statsTimer = null;
        }

        if (self::$mysqlPoolInstance) {
            self::logInfo("正在关闭MySQL连接池");
            self::$mysqlPoolInstance->close();
            self::$mysqlPoolInstance = null;
            self::logInfo("MySQL连接池已关闭");
        }

        if (self::$redisPoolInstance) {
            self::logInfo("正在关闭Redis连接池");
            self::$redisPoolInstance->close();
            self::$redisPoolInstance = null;
            self::logInfo("Redis连接池已关闭");
        }
    }

    /**
     * 检查连接池健康状态
     *
     * @return bool
     */
    public static function isHealthy()
    {
        $mysqlHealthy = false;
        $redisHealthy = false;

        if (self::$mysqlPoolInstance) {
            try {
                $stats = self::$mysqlPoolInstance->getStats();
                $mysqlHealthy = $stats['current_total'] > 0;
            } catch (\Exception $e) {
                self::logError("检查MySQL连接池健康状态失败: " . $e->getMessage());
            }
        }

        if (self::$redisPoolInstance) {
            try {
                $stats = self::$redisPoolInstance->getStats();
                $redisHealthy = $stats['current_total'] > 0;
            } catch (\Exception $e) {
                self::logError("检查Redis连接池健康状态失败: " . $e->getMessage());
            }
        }

        return $mysqlHealthy && $redisHealthy;
    }
    

    
    /**
     * 记录信息日志
     *
     * @param string $message
     */
    private static function logInfo($message)
    {
        \util\log\akcsLog::debug("[DatabasePool][INFO] " . $message);
    }

    /**
     * 记录错误日志
     *
     * @param string $message
     */
    private static function logError($message)
    {
        \util\log\akcsLog::debug("[DatabasePool][ERROR] " . $message);
    }
}
