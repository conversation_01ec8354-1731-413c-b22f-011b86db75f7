<?php

namespace app\controller;

use support\Request;
use support\Response;
use util\smproxy\SMProxyManager;

/**
 * SMProxy管理控制器
 * 
 * 提供SMProxy状态查询、监控信息获取、性能测试等API接口
 */
class SMProxyController
{
    /**
     * 获取SMProxy状态
     * 
     * @param Request $request
     * @return Response
     */
    public function status(Request $request)
    {
        try {
            $config = config('smproxy', []);
            $manager = SMProxyManager::getInstance($config);
            
            $isHealthy = $manager->checkHealth();
            $stats = $manager->getStats();
            
            $response = [
                'success' => true,
                'data' => [
                    'enabled' => $config['enabled'] ?? false,
                    'healthy' => $isHealthy,
                    'stats' => $stats,
                    'config' => $manager->getConfig(),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ];
            
            return json($response);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'error' => '获取SMProxy状态失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 测试SMProxy连接
     * 
     * @param Request $request
     * @return Response
     */
    public function testConnection(Request $request)
    {
        $startTime = microtime(true);
        
        try {
            $config = config('smproxy', []);
            $manager = SMProxyManager::getInstance($config);
            
            $connection = $manager->getConnection();
            
            // 执行测试查询
            $result = $connection->query('SELECT 1 as test, NOW() as current_time, VERSION() as version')->fetch();
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2); // 毫秒
            
            return json([
                'success' => true,
                'data' => [
                    'test_result' => $result,
                    'duration_ms' => $duration,
                    'connection_info' => [
                        'host' => $config['host'] ?? 'unknown',
                        'port' => $config['port'] ?? 'unknown',
                    ],
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            return json([
                'success' => false,
                'error' => 'SMProxy连接测试失败: ' . $e->getMessage(),
                'duration_ms' => $duration
            ], 500);
        }
    }
    
    /**
     * SMProxy性能测试
     * 
     * @param Request $request
     * @return Response
     */
    public function performanceTest(Request $request)
    {
        try {
            $testCount = (int)($request->get('count', 10));
            $testCount = max(1, min(100, $testCount)); // 限制在1-100之间
            
            $config = config('smproxy', []);
            $manager = SMProxyManager::getInstance($config);
            
            $results = $manager->performanceTest($testCount);
            
            return json([
                'success' => true,
                'data' => $results
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'error' => 'SMProxy性能测试失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取SMProxy配置
     * 
     * @param Request $request
     * @return Response
     */
    public function config(Request $request)
    {
        try {
            $config = config('smproxy', []);
            
            // 移除敏感信息
            if (isset($config['password'])) {
                $config['password'] = '***';
            }
            if (isset($config['databases'])) {
                foreach ($config['databases'] as $key => &$dbConfig) {
                    if (isset($dbConfig['smproxy']['password'])) {
                        $dbConfig['smproxy']['password'] = '***';
                    }
                    if (isset($dbConfig['direct']['password'])) {
                        $dbConfig['direct']['password'] = '***';
                    }
                }
            }
            
            return json([
                'success' => true,
                'data' => [
                    'config' => $config,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'error' => '获取SMProxy配置失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 重新加载SMProxy配置
     * 
     * @param Request $request
     * @return Response
     */
    public function reloadConfig(Request $request)
    {
        try {
            $newConfig = $request->post();
            
            if (empty($newConfig)) {
                return json([
                    'success' => false,
                    'error' => '配置参数不能为空'
                ], 400);
            }
            
            $config = config('smproxy', []);
            $manager = SMProxyManager::getInstance($config);
            
            $result = $manager->reloadConfig($newConfig);
            
            return json([
                'success' => true,
                'data' => [
                    'reloaded' => true,
                    'healthy' => $result,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'error' => '重新加载SMProxy配置失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取数据库连接源信息
     * 
     * @param Request $request
     * @return Response
     */
    public function connectionSource(Request $request)
    {
        try {
            $globalApp = \util\container\GlobalApp::getInstance();
            $source = $globalApp->get('akcs_db_source') ?: 'unknown';
            
            $sourceInfo = [
                'current_source' => $source,
                'available_sources' => ['smproxy', 'pool', 'direct'],
                'source_descriptions' => [
                    'smproxy' => 'SMProxy数据库中间件',
                    'pool' => '内置MySQL连接池',
                    'direct' => '直连MySQL数据库'
                ]
            ];
            
            // 获取各个源的状态
            $sourceStatus = [];
            
            // SMProxy状态
            try {
                $config = config('smproxy', []);
                if ($config['enabled'] ?? false) {
                    $manager = SMProxyManager::getInstance($config);
                    $sourceStatus['smproxy'] = [
                        'available' => $manager->checkHealth(),
                        'config' => [
                            'host' => $config['host'] ?? 'unknown',
                            'port' => $config['port'] ?? 'unknown',
                        ]
                    ];
                } else {
                    $sourceStatus['smproxy'] = [
                        'available' => false,
                        'reason' => 'SMProxy未启用'
                    ];
                }
            } catch (\Exception $e) {
                $sourceStatus['smproxy'] = [
                    'available' => false,
                    'error' => $e->getMessage()
                ];
            }
            
            // 连接池状态
            try {
                require_once __DIR__ . '/../../support/bootstrap/DatabasePool.php';
                $poolStats = \support\bootstrap\DatabasePool::getStats();
                $sourceStatus['pool'] = [
                    'available' => !empty($poolStats),
                    'stats' => $poolStats
                ];
            } catch (\Exception $e) {
                $sourceStatus['pool'] = [
                    'available' => false,
                    'error' => $e->getMessage()
                ];
            }
            
            // 直连状态
            $sourceStatus['direct'] = [
                'available' => true,
                'description' => '直连MySQL始终可用'
            ];
            
            return json([
                'success' => true,
                'data' => [
                    'source_info' => $sourceInfo,
                    'source_status' => $sourceStatus,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'error' => '获取连接源信息失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取SMProxy详细信息
     * 
     * @param Request $request
     * @return Response
     */
    public function info(Request $request)
    {
        try {
            $config = config('smproxy', []);
            $manager = SMProxyManager::getInstance($config);
            
            $stats = $manager->getStats();
            $isHealthy = $manager->checkHealth();
            
            // 计算有用的指标
            $connectionUtilization = 0;
            if (isset($stats['max_connections']) && $stats['max_connections'] > 0) {
                $connectionUtilization = round(
                    ($stats['threads_connected'] / $stats['max_connections']) * 100, 
                    2
                );
            }
            
            $response = [
                'success' => true,
                'data' => [
                    'enabled' => $config['enabled'] ?? false,
                    'healthy' => $isHealthy,
                    'connection_utilization_percent' => $connectionUtilization,
                    'stats' => $stats,
                    'config_summary' => [
                        'host' => $config['host'] ?? 'unknown',
                        'port' => $config['port'] ?? 'unknown',
                        'timeout' => $config['timeout'] ?? 'unknown',
                        'health_check_interval' => $config['health_check']['interval'] ?? 'unknown',
                        'failover_enabled' => $config['failover']['enabled'] ?? false,
                    ],
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ];
            
            return json($response);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'error' => '获取SMProxy信息失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
