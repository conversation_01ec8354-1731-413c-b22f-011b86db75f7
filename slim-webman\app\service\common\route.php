<?php

require_once __DIR__ . "/../../util/response.php";
require_once __DIR__ . "/../common/control/bsiLock.php";
require_once __DIR__ . "/../common/control/tiandyHandle.php";
require_once __DIR__ . "/../common/control/userSetting.php";
require_once __DIR__ . "/../common/control/appFlags.php";
require_once __DIR__ . "/../common/control/deviceAbilities.php";
require_once __DIR__ . "/../common/control/personalAlarm.php";
require_once __DIR__ . "/../common/control/mobileCheckCode.php";
require_once __DIR__ . "/../common/control/thirdLockHandle.php";
require_once __DIR__ . "/../common/control/motionHandle.php";
require_once __DIR__ . "/../common/control/setPackageDetection.php";

$gApp->setProjectType(PROJECT_TYPE_COMMON);

// 获取三方平台的用户信息（三方 NVR 设备）
$gApp->get('/tiandy_userconf', function ($request, $response) { 
    $response = \common\control\getTiandyUserConf($request, $response);
    return $response;
});

$gApp->get('/undealedalarm', function ($request, $response) {
    $response = \common\control\getUnDealedAlarmNum($request, $response);
    return $response;
});

$gApp->get('/get_user_setting', function ($request, $response) {
    $response = \common\control\getUserSetting($request, $response);
    return $response;
});


$gApp->post('/apprest/v2/send_two_factor_authentication_code', function ($request, $response) {
    $response = \common\control\sendTwoFactorAuthCode($request, $response);
    return $response;
});


$gApp->get('/apprest/v2/app_flags', function ($request, $response) {
    $response = \common\control\getAppFlags($request, $response);
    return $response;
});

$gApp->get('/apprest/v2/device_abilities', function ($request, $response)  {
    $response = \common\control\getDeviceAbilities($request, $response);
    return $response;
});

// 发送手机验证码
$gApp->post('/send_mobile_checkcode', function ($request, $response) {
    $response = \common\control\updateAndSendVerificationCode($request, $response);
    return $response;
});

$gApp->post('/yale/webhook', function ($request, $response) {
    $response = \common\control\yaleWebhook($request, $response);
    return $response;
});

// H5获取三方锁oauth地址
$gApp->get('/third_party_lock/get_oauth_url', function ($request, $response) {
    $response = \common\control\getOauthUrl($request, $response);
    $response = \util\response\addCorsHeaders($response);
    return $response;
});

// 获取NFC开门配置
$gApp->get('/getnfcconf', function ($request, $response) {
    $response = \common\control\getNfcConf($request, $response);
    return $response;
});

// 获取蓝牙开门配置
$gApp->get('/getbleconf', function ($request, $response) {
    $response = \common\control\getBLEConf($request, $response);
    return $response;
});

// 获取免打扰配置
$gApp->get('/get_dnd', function ($request, $response) {
    $response = \common\control\getDnd($request, $response);
    return $response;
});

// 设置用户二次确认开关
$gApp->post('/set_confirm_switch', function ($request, $response) {
    $response = \common\control\setConfirmSwitch($request, $response);
    return $response;
});


// 获取门的relay状态
$gApp->get('/get_door_relay_status', function ($request, $response) {
    $response = \common\control\getDoorRelayStatus($request, $response);
    return $response;
});

// 设置强提醒
$gApp->post('/set_alarm_reminder', function ($request, $response) {
    $response = \common\control\setAlarmReminder($request, $response);
    return $response;
});


$gApp->post('/set_callkit', function ($request, $response) {
    $response = \common\control\setCallKit($request, $response);
    return $response;
});

// 功能已废弃
$gApp->post('/setmotionread', function ($request, $response) {
    $response = \common\control\setmotionread($request, $response);
    return $response;
});


// 注册三方平台的账号（tiandy的NVR设备）
$gApp->post('/tiandy_register_user', function ($request, $response) {
    $response = \common\control\handleTiandyRegisterUser($request, $response);
    $response = \util\response\addCorsHeaders($response);
    return $response;
});

// 绑定蓝牙锁
$gApp->post('/bind_bsi_lock', function ($request, $response) {
    $response = \common\control\bindBSILock($request, $response);
    return $response;
});

// 授权通过 进行用户信息绑定 
$gApp->post('/third_party_lock/auth_bind', function ($request, $response) {
    $response = \common\control\authBind($request, $response);
    $response = \util\response\addCorsHeaders($response);
    return $response;
});

$gApp->post('/apprest/v2/set_package_detection', function ($request, $response) {
    $response = \common\control\setPackageDetection($request, $response);
    return $response;
});

// 获取多套房的信息
$gApp->get('/get_all_site_info', function ($request, $response) {
    $response = \common\control\getAllSiteInfo($request, $response);
    return $response;
});

// 切换多套房用户
$gApp->post('/change_site', function ($request, $response) {
    $response = \common\control\handleChangeSite($request, $response);
    return $response;
});

// $gApp->post('/apprest/v2/set_sound_detection', function ($request, $response) {
//     $response = \common\control\setSoundDetection($request, $response);
//     return $response;
// });

// SMProxy管理API
$gApp->get('/admin/smproxy/status', function ($request, $response) {
    $controller = new \app\controller\SMProxyController();
    return $controller->status($request);
});

$gApp->get('/admin/smproxy/test', function ($request, $response) {
    $controller = new \app\controller\SMProxyController();
    return $controller->testConnection($request);
});

$gApp->get('/admin/smproxy/performance', function ($request, $response) {
    $controller = new \app\controller\SMProxyController();
    return $controller->performanceTest($request);
});

$gApp->get('/admin/smproxy/config', function ($request, $response) {
    $controller = new \app\controller\SMProxyController();
    return $controller->config($request);
});

$gApp->post('/admin/smproxy/reload', function ($request, $response) {
    $controller = new \app\controller\SMProxyController();
    return $controller->reloadConfig($request);
});

$gApp->get('/admin/smproxy/source', function ($request, $response) {
    $controller = new \app\controller\SMProxyController();
    return $controller->connectionSource($request);
});

$gApp->get('/admin/smproxy/info', function ($request, $response) {
    $controller = new \app\controller\SMProxyController();
    return $controller->info($request);
});
