{"name": "workerman/webman-framework", "type": "library", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://doc.workerman.net/", "source": "https://github.com/walkor/webman-framework"}, "require": {"php": ">=7.2", "ext-json": "*", "workerman/workerman": "^4.0.4 || ^5.0.0", "nikic/fast-route": "^1.3", "psr/container": ">=1.0"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"Webman\\": "./src", "support\\": "./src/support", "Support\\": "./src/support", "Support\\Bootstrap\\": "./src/support/bootstrap", "Support\\Exception\\": "./src/support/exception", "Support\\View\\": "./src/support/view"}}, "minimum-stability": "dev"}