<?php

namespace util\response;
use support\Response;
// 返回旧的约定的错误码
function internalSetOldSpecificResponseMessage($response, $result, $message = '')
{
    //若无自定义提示语，则返回错误码对应的默认提示语
    if (empty($message) && array_key_exists($result, OLD_SPECIFIC_ERROR_CODE_ARR)) {
        if (array_key_exists('message', OLD_SPECIFIC_ERROR_CODE_ARR[$result])) {
            $message = OLD_SPECIFIC_ERROR_CODE_ARR[$result]['message'];
        }
    }

    $response_data = [
        'result' => intval($result),
        'message' => $message,
    ];

    return json($response_data);
}

// 返回新的约定的错误码,由于要跟旧版本兼容，因此保留result字段
function internalSetNewSpecificResponseMessage($response, $err_code, $datas = [], $message = '')
{
    //result默认为通用错误
    $result = intval(ERR_CODE_FAILED);
    if (array_key_exists($err_code, NEW_SPECIFIC_ERROR_CODE_ARR)) {
        if (array_key_exists('result', NEW_SPECIFIC_ERROR_CODE_ARR[$err_code])) {
            $result = NEW_SPECIFIC_ERROR_CODE_ARR[$err_code]['result'];
        }

        //若无自定义提示语，则返回错误码对应的默认提示语
        if (empty($message) && array_key_exists('message', NEW_SPECIFIC_ERROR_CODE_ARR[$err_code])) {
            $message = NEW_SPECIFIC_ERROR_CODE_ARR[$err_code]['message'];
        }
    }

    $response_data = [
        'err_code' => strval($err_code),
        'result' => intval($result),
        'message' => $message,
    ];

    if (!empty($datas)) {
        $response_data['datas'] = $datas;
    }

    return json($response_data);
}

//新版本请求回复,由于要跟旧版本兼容，因此保留result字段
function setResponseMessage($response, $err_code = ERR_CODE_SUCCESS, $datas = [], $message = '')
{
    if (array_key_exists($err_code, OLD_SPECIFIC_ERROR_CODE_ARR)) {
        return internalSetOldSpecificResponseMessage($response, $err_code, $message);
    } else {
        return internalSetNewSpecificResponseMessage($response, $err_code, $datas, $message);
    }
}

//三方锁给H5返回的接口
function setThirdLinkerResponseMessage($response, $message, $code, $data = [])
{
    $responseData = json_encode([
                'msg' => $message,
                'code' => $code,
                'data' => $data
    ]);
    return new Response(200, ['Content-Type' => 'application/json'], $responseData);
}

function addCorsHeaders($response)
{
    $response = $response->withStatus(200);
    $response = $response->withHeader('Access-Control-Allow-Headers', 'x-auth-token,x-community-id,x-cloud-lang,x-cloud-version');
    $response = $response->withHeader('Access-Control-Allow-Origin', "*");
    $response = $response->withHeader('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
    return $response;
}