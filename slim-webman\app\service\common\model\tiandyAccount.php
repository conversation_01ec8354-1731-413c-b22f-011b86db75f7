<?php

namespace common\model;

use PDO;

function insertTiandyAccount($userId, $username, $passwd, $countryInfo, $nodeUUID)
{
    $uuid = \util\common\getMysqlUUID();
    
    $db = \util\container\getDb();
    $sth = $db->prepare("insert into TiandyAccount (TiandyUserID, TiandyUserName, TiandyPasswd, CountryCode, CountryPinyin, CountryIsoCode, CountryAreaName, CountryDomainAbbreviation, NodeUUID, UUID)  
                         values (:userId, :username, :passwd, :countryCode, :countryPinyin, :countryIsoCode, :countryAreaName, :countryDomainAbbreviation, :nodeUUID, :uuid)");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindParam(':userId', $userId, PDO::PARAM_STR);
    $sth->bindParam(':passwd', $passwd, PDO::PARAM_STR);
    $sth->bindParam(':username', $username, PDO::PARAM_STR);
    $sth->bindParam(':nodeUUID', $nodeUUID, PDO::PARAM_STR);
    $sth->bindParam(':countryCode', $countryInfo->code, PDO::PARAM_STR);
    $sth->bindParam(':countryPinyin', $countryInfo->pinyin, PDO::PARAM_STR);
    $sth->bindParam(':countryIsoCode', $countryInfo->isoCode, PDO::PARAM_STR);
    $sth->bindParam(':countryAreaName', $countryInfo->areaName, PDO::PARAM_STR);
    $sth->bindParam(':countryDomainAbbreviation', $countryInfo->domainAbbreviation, PDO::PARAM_STR);
    $sth->execute();
}

function tiandyAccountExists($tiandyUsername)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("/*master*/ select TiandyUserName,TiandyPasswd from TiandyAccount where TiandyUserName = :tiandyUsername");
    $sth->bindParam(':tiandyUsername', $tiandyUsername, PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    return $ret;
}

function getTiandyAccount($nodeUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("/*master*/ select TiandyUserName, TiandyPasswd, CountryCode, CountryPinyin, CountryIsoCode, CountryAreaName, CountryDomainAbbreviation from TiandyAccount where NodeUUID = :nodeUUID");
    $sth->bindParam(':nodeUUID', $nodeUUID, PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    return $ret;
}