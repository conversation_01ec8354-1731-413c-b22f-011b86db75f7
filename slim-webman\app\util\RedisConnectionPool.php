<?php

namespace util\redis;

use Worker<PERSON>\Coroutine\Pool;
use Workerman\Coroutine\Context;
use Worker<PERSON>\Coroutine;

require_once __DIR__ . '/log.php';

/**
 * 基于Workerman官方Pool的Redis连接池
 * 
 * 使用Workerman官方连接池实现，支持协程自动获取和归还连接
 */
class RedisConnectionPool
{
    private static $instance = null;
    private static $pool = null;
    private $config;
    
    private function __construct($config = null)
    {
        $this->config = $config ?: $this->getDefaultConfig();
        $this->initializePool();
    }
    
    public static function getInstance($config = null)
    {
        if (self::$instance === null) {
            self::$instance = new self($config);
        }
        return self::$instance;
    }
    
    private function getDefaultConfig()
    {
        return [
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'database' => 0,
            'timeout' => 5,
            'max_connections' => 20,
            'min_connections' => 5,
            'idle_timeout' => 300,
            'heartbeat_interval' => 60,
            'wait_timeout' => 10,
        ];
    }
    
    private function initializePool()
    {
        if (self::$pool !== null) {
            return;
        }
        
        $poolConfig = [
            'min_connections' => $this->config['min_connections'],
            'idle_timeout' => $this->config['idle_timeout'],
            'heartbeat_interval' => $this->config['heartbeat_interval'],
            'wait_timeout' => $this->config['wait_timeout'],
        ];
        
        \util\log\akcsLog::debug("[RedisConnectionPool][INFO] 初始化Workerman Redis连接池，最大连接数: {$this->config['max_connections']}");
        
        self::$pool = new Pool($this->config['max_connections'], $poolConfig);
        
        // 设置连接创建器
        self::$pool->setConnectionCreator(function () {
            return $this->createConnection();
        });
        
        // 设置连接销毁器
        self::$pool->setConnectionCloser(function ($redis) {
            $this->closeConnection($redis);
        });
        
        // 设置心跳检测器
        self::$pool->setHeartbeatChecker(function ($redis) {
            return $this->checkConnection($redis);
        });
        
        \util\log\akcsLog::debug("[RedisConnectionPool][INFO] Workerman Redis连接池初始化完成");
    }
    
    /**
     * 创建Redis连接
     */
    private function createConnection()
    {
        try {
            $redis = new \Redis();
            
            $connected = $redis->connect(
                $this->config['host'],
                $this->config['port'],
                $this->config['timeout']
            );
            
            if (!$connected) {
                throw new \Exception("无法连接到Redis服务器");
            }
            
            // 设置密码
            if (!empty($this->config['password'])) {
                $redis->auth($this->config['password']);
            }
            
            // 选择数据库
            if ($this->config['database'] > 0) {
                $redis->select($this->config['database']);
            }
            
            \util\log\akcsLog::debug("[RedisConnectionPool][DEBUG] 创建新的Redis连接");
            
            return $redis;
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[RedisConnectionPool][ERROR] 创建Redis连接失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 关闭Redis连接
     */
    private function closeConnection($redis)
    {
        try {
            if ($redis instanceof \Redis) {
                $redis->close();
            }
            \util\log\akcsLog::debug("[RedisConnectionPool][DEBUG] 关闭Redis连接");
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[RedisConnectionPool][ERROR] 关闭Redis连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查连接健康状态
     */
    private function checkConnection($redis)
    {
        try {
            if (!($redis instanceof \Redis)) {
                return false;
            }
            
            $result = $redis->ping();
            return $result === '+PONG' || $result === 'PONG' || $result === true;
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[RedisConnectionPool][ERROR] Redis连接健康检查失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取连接（自动管理协程上下文）
     */
    public function getConnection()
    {
        try {
            // 检查协程上下文中是否已有连接
            $redis = Context::get('redis_connection');
            if ($redis) {
                \util\log\akcsLog::debug("[RedisConnectionPool][DEBUG] 从协程上下文获取Redis连接");
                return $redis;
            }
            
            // 从连接池获取连接
            $redis = self::$pool->get();
            
            // 保存到协程上下文
            Context::set('redis_connection', $redis);
            
            // 设置协程结束时自动归还连接
            Coroutine::defer(function () use ($redis) {
                self::$pool->put($redis);
                Context::set('redis_connection', null);
                \util\log\akcsLog::debug("[RedisConnectionPool][DEBUG] 协程结束，自动归还Redis连接");
            });
            
            \util\log\akcsLog::debug("[RedisConnectionPool][DEBUG] 从连接池获取Redis连接");
            return $redis;
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[RedisConnectionPool][ERROR] 获取Redis连接失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 手动归还连接（通常不需要调用，协程结束时自动归还）
     */
    public function putConnection($redis)
    {
        try {
            self::$pool->put($redis);
            Context::set('redis_connection', null);
            \util\log\akcsLog::debug("[RedisConnectionPool][DEBUG] 手动归还Redis连接");
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[RedisConnectionPool][ERROR] 归还Redis连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取连接池统计信息
     */
    public function getStats()
    {
        try {
            $connectionCount = self::$pool->getConnectionCount();
            
            return [
                'total_connections' => $connectionCount,
                'max_connections' => $this->config['max_connections'],
                'min_connections' => $this->config['min_connections'],
                'config' => [
                    'host' => $this->config['host'],
                    'port' => $this->config['port'],
                    'database' => $this->config['database'],
                    'timeout' => $this->config['timeout'],
                ],
            ];
            
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[RedisConnectionPool][ERROR] 获取Redis统计信息失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 关闭连接池
     */
    public function close()
    {
        try {
            if (self::$pool) {
                self::$pool->closeConnections();
                \util\log\akcsLog::debug("[RedisConnectionPool][INFO] Redis连接池已关闭");
            }
        } catch (\Exception $e) {
            \util\log\akcsLog::debug("[RedisConnectionPool][ERROR] 关闭Redis连接池失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查连接池是否健康
     */
    public function isHealthy()
    {
        try {
            $redis = $this->getConnection();
            return $this->checkConnection($redis);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 魔术方法，代理Redis操作
     */
    public function __call($method, $arguments)
    {
        $redis = $this->getConnection();
        return call_user_func_array([$redis, $method], $arguments);
    }
}
