<?php

/**
 * 测试连接池降级机制
 * 这个脚本用于验证连接池和传统连接方式的降级逻辑
 */

require_once __DIR__ . '/app/util/container.php';
require_once __DIR__ . '/app/util/MySQLConnectionPool.php';

// 模拟常量定义（实际项目中这些应该在配置文件中）
if (!defined('AKCS_DATABASEIP')) {
    define('AKCS_DATABASEIP', 'localhost');
    define('AKCS_DATABASEPORT', 3306);
    define('DATABASEUSER', 'root');
    define('DATABASEPWD', 'password');
}

echo "=== 连接池降级机制测试 ===\n\n";

// 测试1: 测试连接池方式（如果连接池可用）
echo "1. 测试连接池方式:\n";
try {
    $config = [
        'min_connections' => 2,
        'max_connections' => 5,
        'idle_timeout' => 300,
        'health_check_interval' => 60
    ];
    
    $pool = \util\pool\MySQLConnectionPool::getInstance($config);
    echo "   ✓ 连接池初始化成功\n";
    
    $connection = $pool->getConnection();
    echo "   ✓ 从连接池获取连接成功\n";
    
    $medoo = $pool->getMedooConnection();
    echo "   ✓ 从连接池获取Medoo实例成功\n";
    
} catch (\Exception $e) {
    echo "   ✗ 连接池测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试2: 测试传统方式
echo "2. 测试传统连接方式:\n";
try {
    $db = \util\container\initDb();
    echo "   ✓ 传统PDO连接成功\n";
    
    $medoo = \util\container\initMedooDb();
    echo "   ✓ 传统Medoo连接成功\n";
    
} catch (\Exception $e) {
    echo "   ✗ 传统连接测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试3: 测试容器降级机制
echo "3. 测试容器降级机制:\n";
try {
    // 清空容器状态
    \util\container\GlobalApp::$instance = null;
    
    $db = \util\container\getDb();
    echo "   ✓ getDb() 降级机制工作正常\n";
    
    $medoo = \util\container\medooDb();
    echo "   ✓ medooDb() 降级机制工作正常\n";
    
    $redis = \util\container\getRedis();
    echo "   ✓ getRedis() 降级机制工作正常\n";
    
} catch (\Exception $e) {
    echo "   ✗ 容器降级测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
