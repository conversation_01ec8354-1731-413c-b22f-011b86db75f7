<?php

namespace newoffice\control;

require_once __DIR__ . "/../../office/model/officeInfo.php";
require_once __DIR__ . "/../../newoffice/model/officeCompany.php";
require_once __DIR__ . "/../../newoffice/model/officePersonnel.php";
require_once __DIR__ . "/../../newoffice/model/officeAdmin.php";
require_once __DIR__ . "/../../newoffice/model/officeGroup.php";
require_once __DIR__ . "/../../newoffice/model/devices.php";
require_once __DIR__ . "/../../common/model/personalAccountUserInfo.php";
require_once __DIR__ . "/../../common/model/account.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/officeMessageReceiver.php";

//固定写死的配置
function getAdminAppConstConf()
{
    $adminAppConstConf = [];
    $adminAppConstConf['pin_init'] = 1; //admin app不需要弹出pin初始化弹窗
    $adminAppConstConf['show_payment'] = 0;
    $adminAppConstConf['show_subscription'] = 0;    
    $adminAppConstConf['sip_server'] = G_PBX_IPV4;
    $adminAppConstConf['sip_server_ipv6'] = G_PBX_IPV6;
    $adminAppConstConf['video_res'] = "2";
    $adminAppConstConf['video_bitrate'] = "512";
    $adminAppConstConf['video_storage_time'] = "0";
    $adminAppConstConf['have_public_dev'] = "1";
    $adminAppConstConf['data_collection'] = 0; //app数据收集到网管系统 后续如果用户较多可采样收集
    $adminAppConstConf['check_dev'] = 1; //检查室内机的收费方案flag
    $adminAppConstConf['check_slave'] = 1;
    $adminAppConstConf['show_pm'] = "0"; //admin账号不展示Property Manager入口
    $adminAppConstConf['show_id_access'] = 0;
    $adminAppConstConf['enable_smarthome'] = 0;
    $adminAppConstConf['is_old_community'] = 0;
    $adminAppConstConf['call_enable'] = 1;
    $adminAppConstConf['room_name'] = ''; //办公room_name放空
    $adminAppConstConf['room_title'] = ''; //同room_name,放空
    $adminAppConstConf['motion_alert'] = '0'; // admin app不接收motion 
    $adminAppConstConf['is_site_link'] = 0; //admin app没有多套房场景
    return $adminAppConstConf;
}
//直接从数据容器user_data中就能取到的配置
function getAdminAppUserDataConf()
{
    $userData = \util\container\getUserData(); 
    $adminAppUserDataConf = [];
    $adminAppUserDataConf['sip'] = $userData['SipAccount'];
    $adminAppUserDataConf['sip_passwd'] = \util\utility\passwdDecode($userData['SipPwd']);
    $adminAppUserDataConf['main_sip'] = $adminAppUserDataConf['sip'];
    $adminAppUserDataConf['main_sip_passwd'] = $adminAppUserDataConf['sip_passwd'];
    $adminAppUserDataConf['display_name'] = $userData['Name'];
    $adminAppUserDataConf['uid'] = $userData['UserAccount'];
    $adminAppUserDataConf['node'] = $userData['Account'];
    $adminAppUserDataConf['codec'] = strval($userData['Codec']); //0=PCMU, 8 =PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义
    $adminAppUserDataConf['show_tempkey'] = intval($userData['TempKeyPermission']); 
    $adminAppUserDataConf['role'] = intval($userData['Role']);
    $adminAppUserDataConf['account'] = strval($userData['UserAccount']);
    $adminAppUserDataConf['is_active'] = intval($userData['Active']);
    return $adminAppUserDataConf;
}

function getAdminAppSwitchConf()
{
    $userData = \util\container\getUserData();
    $adminAppSwitchConf = [];
    $enablePinConfig = 1;
    $isShowLandLine = 1;
    $userSwitch = intval($userData['UserSwitch']);

    $accountInfo = \common\model\getAccountByParentId($userData['MngID']);
    if ($accountInfo)
    {
        $officeInfo = \office\model\getOfficeInfo($accountInfo['UUID']);
        if ($officeInfo)
        {
            if (!\office\model\officeFeaturePlanIsExpire($accountInfo['UUID']) && \common\model\checkCommunityFeaturePlan($userData['MngID'], FeatureItemPin)) {
                $enablePinConfig = \util\utility\switchHandle($officeInfo['Switch'], DevSwitchEnablePinConfig);
            } else {
                $enablePinConfig = 1;
            }
            $isShowLandLine = \util\utility\switchHandle($officeInfo['Switch'], DevSwitchEnableLandline);
            if($isShowLandLine == 1)
            {
                $isShowLandLine = intval(\newoffice\model\getAdminIsEnableLandLineByUUID($userData['UUID']));
            }
        }
    }
    $adminAppSwitchConf['enable_pin_config'] = intval($enablePinConfig);
    $adminAppSwitchConf['show_landline'] = intval($isShowLandLine);
    $adminAppSwitchConf['enable_confirm_flag'] = intval(\util\utility\switchHandle($userSwitch, PersonlAccountSwitchConfirm));

    return $adminAppSwitchConf;
}
//传输协议控制相关
function getAdminAppTransTypeConf()
{
    $adminAppTransTypeConf = [];
    $userData = \util\container\getUserData(); 
    $transType = $userData['SipType'];
    $mngSipType = \office\model\getOfficeUserMngSipType($userData['ParentID']);
    // 0=udp, 1=tcp, 2=tls, 3=none 管理员不配置
    if ($mngSipType["SipType"] != APP_SIP_TYPE_NONE) {
        $transType = $mngSipType["SipType"];
    }
    $transType = \util\common\transSipType($userData['UUID'], $transType);
    $adminAppTransTypeConf['trans_type'] = strval($transType); //0-udp, 1-tcp, 2-tls
    $adminAppTransTypeConf['rtp_confuse'] = intval($mngSipType["RtpConFuse"]);

    return $adminAppTransTypeConf;
}

function getAdminAppLandlineConf()
{
    $adminAppLandLineConf = [];
    $userData = \util\container\getUserData();
    $adminAppLandLineConf['landline'] = \util\common\getPbxLandlineNumber($userData['PhoneCode'], $userData['Phone'], $userData['Phone2'], $userData['Phone3']);
    // admin app无多套房场景
    $adminAppLandLineConf['all_sites_landline'] = $adminAppLandLineConf['landline'];

    return $adminAppLandLineConf;
}

//admin app暂不支持多套房
// function getAdminAppMultiSiteConf()
// {
    // $userData = \util\container\getUserData();
    
    // $adminAppMultiSiteConf = [];

    // $userInfo = \common\model\getUserInfoByUUID($userData['UserInfoUUID']);
    // $mainSipInfo = \common\model\getUserSipInfo($userInfo['AppMainUserAccount']);
    // $appMainSipPwd = $mainSipInfo['SipPwd'];

    // $adminAppMultiSiteConf['main_sip'] = $userInfo['AppMainUserAccount'];
    // $adminAppMultiSiteConf['main_sip_passwd'] = \util\utility\passwdDecode($appMainSipPwd);

    // // 是否为多套房用户 (Admin App暂时没有多套房场景)
    // $adminAppMultiSiteConf['is_site_link'] = \common\model\isMultiSiteUser($userData['UserInfoUUID']);
    // if ($adminAppMultiSiteConf['is_site_link']) {
    //     $allLandlines = array();
    //     $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($userData['UserInfoUUID']);
    //     foreach ($personalAccountList as $siteInfo) {
    //         $siteLandline = \util\common\getPbxLandlineNumber($siteInfo['PhoneCode'], $siteInfo['Phone'], $siteInfo['Phone2'], $siteInfo['Phone3']);
    //         $allLandlines = array_merge($allLandlines, $siteLandline);
    //     }
    //     //app根据all_sites_landline添加app联系人，根据show_landline和landline字段判断是否弹窗，如果all_sites_landline有变化也需要弹窗
    //     $adminAppMultiSiteConf['all_sites_landline'] = array_values(array_unique($allLandlines));
    // } else {
    //     $adminAppMultiSiteConf['all_sites_landline'] = $adminAppMultiSiteConf['landline'];
    // }

//     return $adminAppMultiSiteConf;
// }

function getAdminAppSiteConf()
{
    $userData = \util\container\getUserData();

    $adminAppSiteConf = [];

    $adminAppSiteConf['project_name'] = $userData['OfficeCompanyName']; //对于admin App只要展示对应的company name
    $adminAppSiteConf['personnel_num'] = intval(\newoffice\model\getCompanyPersonnelNum($userData['OfficeCompanyUUID']));
    $adminAppSiteConf['group_num'] = intval(\newoffice\model\getCompanyGroupNum($userData['OfficeCompanyUUID']));

    return $adminAppSiteConf;
}

function getAdminAppExpireConf()
{
    $adminAppExpireConf = [];
    $userData = \util\container\getUserData();
    \common\model\checkAccountExpire2($adminAppExpireConf, $userData);

    return $adminAppExpireConf;
}

function getAdminAppConf()
{
    //顺序不能调整，否则可能出现覆盖错误问题，比如过期的配置判断必须放最后
    $appConf = [];
    $appConf = array_merge($appConf, getAdminAppConstConf());
    $appConf = array_merge($appConf, getAdminAppUserDataConf());
    $appConf = array_merge($appConf, getAdminAppSwitchConf());
    $appConf = array_merge($appConf, getAdminAppTransTypeConf());
    $appConf = array_merge($appConf, getAdminAppLandlineConf());
    $appConf = array_merge($appConf, getAdminAppSiteConf());
    // $appConf = array_merge($appConf, getAdminAppMultiSiteConf());
    $appConf = array_merge($appConf, getAdminAppExpireConf());
    return $appConf;
}

function getAdminAppDevList()
{
    $userData = \util\container\getUserData();
    $devList = [];
    //Admin不存在Assign的场景，只需根据权限组判断
    \newoffice\model\getNewOfficeDevicesList($userData, $devList);
    return $devList;
}

function getAdminAppThirdPartyDevList($accesssDevicesList)
{
    // 获取三方锁(新办公目前只支持SALTO锁)
    $lockDevList = \newoffice\control\salto\getSaltoLockList($accesssDevicesList);
    $thirdPartyDevList = array();
    $thirdPartyDevList['lock_dev_list'] = $lockDevList;
    return $thirdPartyDevList;
}

function getAdminAppUnreadMsg()
{
    $unreadMessageArr = [];
    $userData = \util\container\getUserData();
    $unreadMessageArr['activities_num'] = intval(\common\model\getActivitiesNum($userData));
    $unreadMessageArr['messages_num'] = intval(\common\model\getMessagesNumV70(MESSAGE_RECEIVER_CLIENT_TYPE_APP));

    return $unreadMessageArr;
}

function getNewOfficeAdminUserConf($request, $response)
{
    $accessDevicesList = getAdminAppDevList();

    $datas = [
        "app_conf" => getAdminAppConf(),
        "dev_list" => $accessDevicesList,
        "third_party_dev_list" => getAdminAppThirdPartyDevList($accessDevicesList),
        "unread_msg" => getAdminAppUnreadMsg()
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}