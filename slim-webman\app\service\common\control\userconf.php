<?php

namespace common\control;

require_once __DIR__ . "/../../common/model/token.php";
require_once __DIR__ . "/../../common/model/appPushToken.php";
require_once __DIR__ . "/../../common/model/accountUserInfo.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/personalAccountUserInfo.php";
require_once __DIR__ . "/../../common/model/pmAccountMap.php";
require_once __DIR__ . "/../../common/model/devices.php";
require_once __DIR__ . "/../../common/model/awsRedirect.php";
require_once __DIR__ . "/../../common/model/twoFactorAuthCode.php";


function getNfcConf($request, $response)
{
    $userConf = \util\container\getUserData();

    $nfcCode = strval(\common\model\getNFCCode($userConf['UserAccount']));

    $enable = strlen($nfcCode) > 0 ? "1" : "0";

    \util\log\akcsLog::debug("enableNFC:" . $enable . ",NFCCode:" . $nfcCode);

    $datas = [
        'nfccode' => $nfcCode,
        'enable' => $enable
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas); 
}

function getBLEConf($request, $response)
{
    $userConf = \util\container\getUserData();

    $bleConf = \common\model\getBLEConf($userConf['UserAccount']);

    $enable = strlen($bleConf['BLECode']) > 0 ? "1" : "0";
    \util\log\akcsLog::debug("enableBLE:" . $enable . ",BLECode:" . $bleConf['BLECode']);

    $datas = [
        'enable' => $enable,
        'opendoor_type' => strval($bleConf['BLEOpenDoorType']),
        'blecode' => strval($bleConf['BLECode'])
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);

}

function setConfirmSwitch($request, $response)
{          
    $userConf = \util\container\getUserData();  

    $postDatas = $request->getParsedBody();
    $switch = $postDatas['switch'];

    if ($switch === null) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"body is null");
    }

    \common\model\updateConfirmSwitch($switch, $userConf['UUID']);

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function handleChangeSite($request, $response)
{
    $token = \util\container\getToken();
    $userData = \util\container\getUserData();
    $account = $request->getParsedBody()['account'];

    // 获取所有站点的用户
    $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($userData['UserInfoUUID']);

    if ($personalAccountList) {
        $accountColumn = array_column($personalAccountList, 'Account');
        // 要切换的站点在所有站点中，且要切换的站点不能为当前站点
        if (in_array($account, $accountColumn) && $account != $userData['UserAccount']) {
            // 更新token的Account
            \common\model\updateAccountByToken($account, $token);
            // 更新lastLoginAccount
            \common\control\updateLastLoginAccountByUUID($account, $userData['UserInfoUUID']);
            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        }
    }

    return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"No permission");
}

function updateLastLoginAccountByUUID($account, $uuid)
{    
    $tableFlag = \util\common\getAccountUserInfoTable($uuid);
    if ($tableFlag == TABLE_ACCOUNT_USER_INFO) {
        \common\model\updateAccountLastLoginUserByUUID($account, $uuid);
    } else if ($tableFlag == TABLE_PERSONAL_ACCOUNT_USER_INFO) {
        \common\model\updateLastLoginUserByUUID($account, $uuid);
    }
}

function checkIsMultiSiteUser()
{
    $userConf = \util\container\getUserData();

    if ($userConf['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        $userInfo = \common\model\getAccountUserInfoByUUID($userConf['UserInfoUUID']);
        if ($userInfo && $userInfo['IsLink']) {
            return 1;
        } else {
            return \common\model\isMultiSiteUser($userConf['UserInfoUUID']);
        }
    } else {
        return \common\model\isMultiSiteUser($userConf['UserInfoUUID']);
    }
}

function getAllSiteInfo($request, $response)
{
    $userData = \util\container\getUserData();

    //通过UserInfoUUID获取所有的site信息
    $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($userData['UserInfoUUID']);
    
    $allSiteInfo = array();
    //遍历所有的site信息，获取字段信息；多套房用户能够跨社区和办公，对role进行判断进行不同操作
    foreach ($personalAccountList as $siteInfo) {
        // 判断pm app开关,开关关闭的不下发
        if ($siteInfo['Role'] == ROLE_TYPE_COMMUNITY_PM) {
            if (0 == \common\model\checkPMAppAccountStatusByAccount($siteInfo['Account'])) {
                continue;
            }
        }
        
        $tmpInfo = array();
        if (\util\common\isResidentUser($siteInfo['Role'])) { 
            $siteDetailInfo = \common\model\getCommInfo($siteInfo['Account']);
            \common\control\getCommSiteInfo($siteDetailInfo, $tmpInfo);
        }
        else if (\util\common\isOfficeUser($siteInfo['Role'])) {
            $siteDetailInfo = \common\model\getOfficeCommInfo($siteInfo['Account']);
            \common\control\getOfficeSiteInfo($siteDetailInfo, $tmpInfo);
        }
        array_push($allSiteInfo, $tmpInfo);
    }

    $datas = [
        'sites' => $allSiteInfo
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function getOfficeSiteInfo($siteDetailInfo, &$tmpInfo)
{
    // site account
    $tmpInfo['account'] = (string)$siteDetailInfo['UserAccount'];
    //办公room_name放空
    $tmpInfo['room_name'] = '';
    // 办公名称
    $projectInfo = \common\model\getAccountByID($siteDetailInfo['MngID']);
    $tmpInfo['project_name'] = (string)$projectInfo['Location'];
    // 账号过期
    $tmpInfo['is_expire'] = (int)\common\model\checkSiteIsExpire($siteDetailInfo);
    $tmpInfo['room_title'] = (string)$tmpInfo['project_name'];
    // 是否激活
    $tmpInfo['is_active'] = (int)intval($siteDetailInfo['Active']);
    // check_dev 是室内机方案时室内机是否上线的标识（0-未上线 1-正常）
    $tmpInfo['check_dev'] = 1;
    //check_slave 是pm设置从账号个数  超过限制的不允许登陆（0-超出限制 1-正常）
    $tmpInfo['check_slave'] = 1;
}

function getCommSiteInfo($siteDetailInfo, &$tmpInfo)
{
    //site account
    $tmpInfo['account'] = (string)$siteDetailInfo['UserAccount'];
    //社区名称
    $projectInfo = \common\model\getAccountByID($siteDetailInfo['MngID']);
    //房间号/房间名称
    if ($siteDetailInfo['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        $tmpInfo['room_name'] = (string)PROPERTY_MANAGER;
        $tmpInfo['project_name'] = (string)$projectInfo['Location'];
        $tmpInfo['room_title'] = (string)$tmpInfo['project_name'];
    } elseif ($siteDetailInfo['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $siteDetailInfo['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        $roomInfo = \common\model\getCommunityRoomInfoByID($siteDetailInfo['RoomID']);
        $tmpInfo['room_name'] = (string)$roomInfo['RoomName'];
        $tmpInfo['project_name'] = (string)$projectInfo['Location'];
        $tmpInfo['room_title'] = (string)($tmpInfo['project_name'].'-'.$tmpInfo['room_name']);
    } elseif ($siteDetailInfo['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $siteDetailInfo['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        // 若没有Room Name展示Name（主从账号统一展示主账号的Name)
        $tmpInfo['room_name'] = (string)(strlen($siteDetailInfo['RoomNumber']) > 0 ? $siteDetailInfo['RoomNumber'] : $siteDetailInfo['NodeName']);
        $tmpInfo['room_title'] = (string)$tmpInfo['room_name'];
        $tmpInfo['project_name'] = "";
    }
    // 是否过期
    $tmpInfo['is_expire'] = (int)\common\model\checkSiteIsExpire($siteDetailInfo);
    // 是否激活
    $tmpInfo['is_active'] = (int)intval($siteDetailInfo['Active']);
    // check_dev 是室内机方案时室内机是否上线的标识（0-未上线 1-正常）
    $check_dev = 1;
    if ($siteDetailInfo['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $siteDetailInfo['Role'] == ROLE_TYPE_COMMUNITY_SLAVE
     || $siteDetailInfo['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $siteDetailInfo['Role'] == ROLE_TYPE_PERSONNAL_SLAVE)
    {
        $check_dev = \common\model\checkIndoorPayPlanDevOnline($siteDetailInfo);
    }
    $tmpInfo['check_dev'] = (int)$check_dev;

    //check_slave 是pm设置从账号个数  超过限制的不允许登陆（0-超出限制 1-正常）
    $check_slave = 1;
    if ($siteDetailInfo['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        $feature_expire = \resident\model\checkFeaturePlanIsExpire($siteDetailInfo['MngID']);
        $check_slave = \common\model\checkFamilyMemberControl($siteDetailInfo['MngID'], $siteDetailInfo['UserAccount'], $feature_expire);
    }
    $tmpInfo['check_slave'] = (int)$check_slave;
}

// 获取loginconf时根据mode获取登录提示语
function handleLoginMode(&$mode, &$wordKey)
{
    $userData = \util\container\getUserData();

    if (\common\control\checkIsMultiSiteUser() && !\util\version\isSupportMultiSite()) {
        $mode = 1;
    }
    
    if ($mode == 0) {
        CheckCallRuleModifyNotify($userData);
        if (\util\version\isNeedUpdatePinInitStatus()) {
            \common\model\updateAppPinInitStatus($userData['UserAccount']);
        }
        \common\model\updateAppLoginStatus($userData['UserAccount']);
    } elseif ($mode == 1) {
        $wordKey = LOGIN_INTERCEPT_WORD_KEY1;
    } elseif ($mode == 2) {
        $wordKey = LOGIN_INTERCEPT_WORD_KEY2;
    }
}

function getLoginInterceptCode(&$err_code, &$wordKey)
{
    $userData = \util\container\getUserData();

    if (\common\control\checkIsMultiSiteUser() && !\util\version\isSupportMultiSite()) {
        $err_code = ERR_CODE_INTERCEPT_UPGRADE;
    }
    
    if ($err_code == ERR_CODE_SUCCESS) {
        CheckCallRuleModifyNotify($userData);
        if (\util\version\isNeedUpdatePinInitStatus()) {
            \common\model\updateAppPinInitStatus($userData['UserAccount']);
        }

        \common\model\updateAppLoginStatus($userData['UserAccount']);
    } elseif ($err_code == ERR_CODE_INTERCEPT_UPGRADE) {
        $wordKey = LOGIN_INTERCEPT_WORD_KEY1;
    } elseif ($err_code == ERR_CODE_INTERCEPT_OK) {
        $wordKey = LOGIN_INTERCEPT_WORD_KEY2;
    }
}

// login_conf接口，判断账号是否为过期/未激活/额外的app未支付
function checkAppStatus()
{
    $userData = \util\container\getUserData();

    $role = $userData['Role'];
    $active = $userData['Active'];
    $expire = $userData['Expire'];

    // 账号激活，但是已过期
    if ($active && $expire) {
        return ERR_APP_EXPIRE;
    }

    // 主账号未激活
    if (!$active && $role != ROLE_TYPE_PERSONNAL_SLAVE && $role != ROLE_TYPE_COMMUNITY_SLAVE) {
        return ERR_APP_UNACTIVE;
    }

    // 从账号未激活,查询下对应的主账号
    if (!$active && ($role == ROLE_TYPE_PERSONNAL_SLAVE || $role == ROLE_TYPE_COMMUNITY_SLAVE)) {
        $nodeData = \common\model\getCommInfo($userData['Account']);
        //主账号已经激活,从账号未激活,则证明是额外的app
        if ($nodeData['Active']) {
            return ERR_APP_UNPAID;
        } else {
            return ERR_APP_UNACTIVE;
        }
    }

    // pm 判断app开关
    if ($role == ROLE_TYPE_COMMUNITY_PM) {
        if (0 == \common\model\checkPMAppAccountStatusByAccount($userData['UserAccount'])) {
            return ERR_PM_APP_STATUS_CLOSED;
        }
    }

    return ERR_SUCCESS;
}

function getAppStatusCode(&$err_code)
{
    $userData = \util\container\getUserData();

    $role = $userData['Role'];
    $active = $userData['Active'];
    $expire = $userData['Expire'];

    // 账号激活，但是已过期
    if ($active && $expire) {
        $err_code = ERR_CODE_APP_ACTIVE_BUT_EXPIRED;
        return;
    }

    // 主账号未激活
    if (!$active && $role != ROLE_TYPE_PERSONNAL_SLAVE && $role != ROLE_TYPE_COMMUNITY_SLAVE) {
        $err_code = ERR_CODE_MASTER_ACCOUNT_UNACTIVE;
        return;
    }

    // 从账号未激活,查询下对应的主账号
    if (!$active && ($role == ROLE_TYPE_PERSONNAL_SLAVE || $role == ROLE_TYPE_COMMUNITY_SLAVE)) {
        $nodeData = \common\model\getCommInfo($userData['Account']);
        //主账号已经激活,从账号未激活,则证明是额外的app
        if ($nodeData['Active']) {
            $err_code = ERR_CODE_SLAVE_ACCOUNT_UNACTIVE;
            return;
        } else {
            $err_code = ERR_CODE_MASTER_ACCOUNT_UNACTIVE;
            return;
        }
    }

    // pm 判断app开关
    if ($role == ROLE_TYPE_COMMUNITY_PM) {
        if (0 == \common\model\checkPMAppAccountStatusByAccount($userData['UserAccount'])) {
            $err_code = ERR_CODE_PM_APP_CLOSED;
            return;
        }
    }
   // admin 判断app开关
    else if ($role == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        if (0 == \newoffice\model\getAdminAppStatusByPerUUID($userData['UUID'])) {
            $err_code = ERR_CODE_ADMIN_APP_CLOSED;
            return;
        }
    }
    

    return;
}

//直接返回设备对应relay状态，不需校验设备与account的关系，即使越权了关系也不大
function getDoorRelayStatus($request, $response)
{
    $paramValue = $request->getQueryParams();
    $mac = $paramValue['mac'];

    $doorRelayStatus = array();
    $doorSeRelayStatus = array();
    \common\model\getAllDoorRelayStatus($mac, $doorRelayStatus, $doorSeRelayStatus);

    $doorRelayStatusList = array();
    $doorSeRelayStatusList = array();
    $doorRelayStatusList = \util\common\createDoorRelayStatusArray($doorRelayStatus);
    $doorSeRelayStatusList = \util\common\createDoorRelayStatusArray($doorSeRelayStatus);

    $datas = [
        "relay" => $doorRelayStatusList,
        "security_relay" => $doorSeRelayStatusList
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function GetAppStatusByCode($code)
{
    $app_status = 0; //默认值
    if (array_key_exists($code, CODE_APPSTATUS_MAP))
    {
        $app_status = CODE_APPSTATUS_MAP[$code];
    }
    return $app_status;
}

function GetInterceptModeByCode($code)
{
    $intercept_mode = 0; //默认值
    if (array_key_exists($code, CODE_INTERCEPT_MODE_MAP))
    {
        $intercept_mode = CODE_INTERCEPT_MODE_MAP[$code];
    }
    return $intercept_mode;
}

//单呼逻辑：社区用户第一次登录App，要刷用户对应的设备联系人
function CheckCallRuleModifyNotify($userData)
{
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE)
    {
        if (\common\model\isAppFirstLogin($userData['UserAccount'])) {
            \util\log\akcsLog::debug("single call rule modify notify: account:" . $userData['Account'] . "unitid:" . $userData['UnitID'] . "mngid:" . $userData['MngID']);
            webCommunityModifyNotify(WEB_COMM_UPDATE_APT_CALLRULE, $userData['Account'], "", $userData['MngID'], $userData['UnitID']);
        }
    }
}