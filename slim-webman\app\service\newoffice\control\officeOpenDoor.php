<?php

namespace newoffice\control;

require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/../../resident/control/residentOpenDoor.php";

function OpenSiteDoorNewOffice($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();
    $isCanOpendoor = 0;
    $mac = $postDatas['mac'];
    $relay = $postDatas['relay'];
    $securityRelay=$postDatas['security_relay'];
    $traceID = $postDatas['trace_id'];
    
    $agMacList = array(); //key is mac,value is relay
    \newoffice\model\getUserAGDeviceListForNewOffice($userConf, $agMacList);
    
    $relay1 = \util\common\relayId2RelayInt($relay);
    $securityRelay1 = \util\common\relayId2RelayInt($securityRelay);
    $isCanOpendoor  = \util\common\checkAppOpenDoor($agMacList, $mac, $relay1, $securityRelay1);
    $microseconds = (int)(microtime(true) * 1000000);

    if ($isCanOpendoor) {
        $datas = array();
        if ($traceID) {
            $datas['is_need_wait_response'] = 0;
            if (\util\common\DevSupportOpenDoorAck(\common\model\getDevicesFunctionByMac($mac)))
            {
                $datas['is_need_wait_response'] = 1;
            }
        } else {
            $traceID = \util\utility\createTraceID(6); //随机生成一个，避免traceID为空影响后续流程
        }

        if (!is_null($relay)) {
            \util\log\akcsLog::debug("OpenDoorNotify:mac=$mac,relay=$relay,uid=" . $userConf['UserAccount'] . "trace_id=" . $traceID);

            $jsondata = [
                "msg_type" => "remote_open_door",
                "trace_id"=> strval($traceID),
                "timestamp"=> $microseconds,
                "data"=> [
                    "mac" => strval($mac),
                    "relay" => strval($relay),
                    "trace_id"=> strval($traceID),
                    "uid" => strval($userConf['UserAccount']),
                    "repost" => strval(DEIVCE_DISABLE_REPOST)
                ]
            ];
            JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);
        }

        if (!is_null($securityRelay)) {
            \util\log\akcsLog::debug("OpenSecurityNotify:mac=$mac,relay=$securityRelay,uid=" . $userConf['UserAccount'] . "trace_id=" . $traceID);
            $jsondata = [
                "msg_type" => "remote_open_security_relay",
                "trace_id"=> strval($traceID),
                "timestamp"=> $microseconds,
                "data"=> [
                    "mac" => strval($mac),
                    "trace_id"=> strval($traceID),
                    "uid" => strval($userConf['UserAccount']),
                    "security_relay" => strval($securityRelay),
                    "repost" => strval(DEIVCE_DISABLE_REPOST)
                ]
            ];

            JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);
        }
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_NO_PERMISSION);
    }
}
