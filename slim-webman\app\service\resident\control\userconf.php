<?php

namespace  resident\control;

require_once __DIR__ . "/../../common/control/userconf.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";
require_once __DIR__ . "/../../common/model/appPushToken.php";
require_once __DIR__ . "/../../common/model/token.php";
require_once __DIR__ . "/../model/communityInfo.php";
require_once __DIR__ . "/../../common/model/communityCallRule.php";
require_once __DIR__ . "/../../common/model/devices.php";
require_once __DIR__ . "/../model/AmenityDevice.php";
require_once __DIR__ . "/../../common/model/callHistory.php";
require_once __DIR__ . "/../../common/model/thirdPartCamera.php";
require_once __DIR__ . "/../../common/model/thirdPartyLockDevice.php";
require_once __DIR__ . "/../../common/control/dormakabaLock.php";
require_once __DIR__ . "/../../common/model/accountUserInfo.php";
require_once __DIR__ . "/../../common/model/personalAccountUserInfo.php";
require_once __DIR__ . "/../../common/model/communityRoom.php";
require_once __DIR__ . "/../../common/model/messageAccountList.php";
require_once __DIR__ . "/pmUserConf.php";
require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/pmUserConf.php";
require_once __DIR__ . "/perUserConf.php";
require_once __DIR__ . "/commUserConf.php";
require_once __DIR__ . "/../../common/model/saltoLock.php";
require_once __DIR__ . "/../../common/control/iTecLock.php";
require_once __DIR__ . "/../../common/control/TTLock.php";
require_once __DIR__ . "/../../common/control/thirdNvr.php";

function getCommConf($request, $response)
{
    $userConf = \util\container\getUserData();

    $commCode = \common\model\getCommCodeConf($userConf['UserAccount']);

    $bleCode = $commCode['BLECode'];
    $nfcCode = $commCode['NFCCode'];
    $enableNFC = strlen($commCode['NFCCode']) > 0 ? "1" : "0";
    $enableBLE = strlen($commCode['BLECode']) > 0 ? "1" : "0";

    $callTypeInfo = \common\model\getCalltypeByAccount($userConf['Account']);

    $appInfo = \common\model\getAppPushCnf($userConf['AppMainUserAccount']);

    $tokenInfo = \common\model\getTokenInfoByAppMainAccount($userConf['AppMainUserAccount']);
    
    $commInfo = \resident\model\getCommunityInfo($userConf['MngID']);
    
    //IOS V4.3版本在新的版本设置为不存在的calltype出现崩溃
    if ($appInfo['AppType'] == 0 && $appInfo['Version'] < 4400 && $callTypeInfo['CallType'] > 2) {
        $callTypeInfo['CallType'] = 0;
    }

    $contactPreference = 0;
    if ($userConf['IsPer'])
    {
        $contactPreference = $callTypeInfo['EnableRobinCall'];
    }
    else if ($userConf['Role'] == ROLE_TYPE_COMMUNITY_SLAVE || $userConf['Role'] == ROLE_TYPE_COMMUNITY_MASTER)
    {
        $contactPreference = \common\model\getCommunityContactPreference($userConf['NodeUUID']);
    } 

    $datas = [
        'ble' => [
            'enable' => $enableBLE,
            'code' => $bleCode,
            'opendoor_type' => $commCode['BLEOpenDoorType']
        ],
        'nfc' => [
            'enable' => $enableNFC,
            'code' => $nfcCode
        ],
        'calltype' => strval($callTypeInfo['CallType']),
        'calltype_personal' => strval($contactPreference),   // 0:群呼，1:顺序呼叫
        'enable_callkit' => strval($tokenInfo['EnableCallkit'])
    ];
    // get community enable landline, 只有这两个角色需要下发
    if ($userConf['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userConf['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        $datas['calltype_show_phone'] = strval(\util\utility\switchHandle($commInfo['Switch'], DevSwitchEnableLandline));
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function getUserConfV30($request, $response)
{    
    $userData = \util\container\getUserData();

    //pm账号
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        return \resident\control\getPMUserConfV64($response);
    }

    //社区主从账号
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        return \resident\control\getCommUserConfV65($response);
    }

    //单住户主从账号
    if ($userData['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userData['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        return \resident\control\getPerUserConfV65($response);
    }

    return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"account does not exist");
}

function getLoginConfV64($request, $response)
{
    $userData = \util\container\getUserData();

    $isInit = "1";	//除了社区主账号其它都默认已初始化
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userData['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        $communityInfo = \resident\model\getCommunityInfo($userData['MngID']);
        $enablePin = \util\utility\switchHandle($communityInfo['Switch'], DevSwitchEnablePinConfig);
        $isInit = $enablePin ? $userData['Initialization'] : "1";
    }

    $role = $userData['Role'];

    $mode = 0;
    $wordKey = '';
    \common\control\handleLoginMode($mode, $wordKey);

    if ($mode == 0) {
        \common\model\updateAppLoginStatus($userData['UserAccount']);
        \common\model\updateAppPinInitStatus($userData['UserAccount']);
    } elseif ($mode == 1) {
        $wordKey = LOGIN_INTERCEPT_WORD_KEY1;
    } elseif ($mode == 2) {
        $wordKey = LOGIN_INTERCEPT_WORD_KEY2;
    }

    $datas = [
            "intercept_mode" => intval($mode),
            "word_key" => strval($wordKey),
            "is_init" => strval($isInit),
            "role" => strval($role),
    ];
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);  
}

function getLoginConfV65($request, $response)
{
    $userData = \util\container\getUserData();
    
    $isInit = "0";
    $wordKey = '';
    $role = $userData['Role'];

    //社区主从账户根据pin开关和初始化标记
    if ($role == ROLE_TYPE_COMMUNITY_MASTER || $role == ROLE_TYPE_COMMUNITY_SLAVE || $role == ROLE_TYPE_COMMUNITY_PM) {
        $communityInfo = \resident\model\getCommunityInfo($userData['MngID']);
        $enablePin = \util\utility\switchHandle($communityInfo['Switch'], DevSwitchEnablePinConfig);
        $isInit = $enablePin ? $userData['Initialization'] : "1";
    } else {
        $isInit = $userData['Initialization'];
    }

    $code = ERR_CODE_SUCCESS;

    \common\control\getLoginInterceptCode($code, $wordKey);
    $old_intercept_mode = \common\control\GetInterceptModeByCode($code);

    $featureExpire = \resident\model\checkFeaturePlanIsExpire($userData['MngID']);

    //checkDev 是室内机方案时室内机是否上线的标识（0-未上线 1-正常）
    $checkDev = 1;
    if ($role == ROLE_TYPE_COMMUNITY_MASTER || $role == ROLE_TYPE_COMMUNITY_SLAVE || $role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_PERSONNAL_SLAVE) {
        $checkDev = \common\model\checkIndoorPayPlanDevOnline($userData);
    }

    //checkSlave 是pm设置从账号个数  超过限制的不允许登陆（0-超出限制 1-正常）
    $checkSlave = 1;
    if ($role == ROLE_TYPE_COMMUNITY_SLAVE) {
        $checkSlave = \common\model\checkFamilyMemberControl($userData['MngID'], $userData['UserAccount'], $featureExpire);
    }

    // 是否为多套房用户
    $isSiteLink = \common\control\checkIsMultiSiteUser();

    // 一人多套房:app忽略login的errCode,移到login_conf判断,用于App弹窗提示
    \common\control\getAppStatusCode($code);
    $old_app_status = \common\control\GetAppStatusByCode($code);
    
    // v6.7 安卓引导优化 , show_tempkey挪到loginconf中
    $showTmpKey = 1;
    if ($userData == ROLE_TYPE_COMMUNITY_PM) {
        $showTmpKey = intval($userData['TempKeyPermission']);
    } elseif ($userData['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userData['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        $showTmpKey= 1;
    } elseif ($userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        $showTmpKey = \common\model\checkShowTmpkey($userData, $featureExpire);
    }

    $datas = [
            "intercept_mode" => intval($old_intercept_mode),
            "word_key" => strval($wordKey),
            "is_init" => strval($isInit),
            "role" => strval($role),
            "check_dev" => intval($checkDev),
            "check_slave" => intval($checkSlave),
            "is_site_link" => intval($isSiteLink),
            "app_status" => intval($old_app_status),
            "show_tempkey" => intval($showTmpKey),
    ];
    return \util\response\setResponseMessage($response, $code, $datas);
}








