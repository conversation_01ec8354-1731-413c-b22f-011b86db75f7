<?php

namespace resident\control;

function getLockStatusList($request, $response)
{
    try {
        $params = $request->getParsedBody();
        
        // 参数验证
        if (empty($params['lock_uuids']) || !is_array($params['lock_uuids'])) {
            \util\log\akcsLog::debug("getLockStatusList: lockUuids not found");
            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, []);
        }
        
        $lockUuids = $params['lock_uuids'];
        // 获取锁状态列表
        $lockList = \resident\model\getLockStatusByUuids($lockUuids);
        
        $result = array_map(function($item) {
                return [
                    'uuid' => $item['UUID'],
                    'lock_status' => (int)$item['LockStatus']
                ];
            }, $lockList);
        
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $result);
        
    } catch (\Exception $e) {
        \util\log\akcsLog::debug("getLockStatusList error: " . $e->getMessage());
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, []);
    }
}