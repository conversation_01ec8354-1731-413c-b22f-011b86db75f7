<?php

namespace resident\model;

use PDO;

function getExtraDevicesByConfigUUID($configUUID)
{
    $db = \util\container\getDb();
    $result = [];
    
    $extraDeviceSql = "SELECT UUID, DeviceIndex, Switch as EnableSwitch 
                       FROM ExtraDevice 
                       WHERE IndoorMonitorConfigUUID = :configUUID";
    $extraDeviceStmt = $db->prepare($extraDeviceSql);
    $extraDeviceStmt->bindParam(":configUUID", $configUUID, PDO::PARAM_STR);
    $extraDeviceStmt->execute();
    $extraDevices = $extraDeviceStmt->fetchAll(PDO::FETCH_ASSOC);
    
    return $extraDevices;
} 