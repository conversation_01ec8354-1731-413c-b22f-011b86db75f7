<?php

namespace util\pool;

use Redis;
use SplQueue;
use Workerman\Timer;
use Exception;

require_once __DIR__ . '/redisManage.php';
require_once __DIR__ . '/log.php';

/**
 * Redis连接池管理器
 * 提供高效的Redis连接复用机制
 */
class RedisConnectionPool
{
    /**
     * 连接池实例（单例）
     */
    private static $instance = null;

    /**
     * 空闲连接队列
     */
    private $idleConnections;

    /**
     * 活跃连接数组 [connection_id => connection_info]
     */
    private $activeConnections = [];

    /**
     * 连接池配置
     */
    private $config;

    /**
     * 健康检查定时器ID
     */
    private $healthCheckTimer = null;

    /**
     * 连接ID计数器
     */
    private $connectionIdCounter = 0;

    /**
     * 连接池统计信息
     */
    private $stats = [
        'total_created' => 0,
        'total_destroyed' => 0,
        'current_active' => 0,
        'current_idle' => 0,
        'peak_active' => 0,
        'connection_errors' => 0,
        'health_check_failures' => 0
    ];

    private function __construct($config)
    {
        $this->config = $config;
        $this->idleConnections = new SplQueue();
        $this->initializePool();
        $this->startHealthCheck();
    }

    /**
     * 获取连接池实例
     */
    public static function getInstance($config = null)
    {
        if (self::$instance === null) {
            if ($config === null) {
                throw new Exception('Redis连接池配置不能为空');
            }
            self::$instance = new self($config);
        }
        return self::$instance;
    }

    /**
     * 初始化连接池
     */
    private function initializePool()
    {
        $minConnections = $this->config['min_connections'] ?? 3;
        
        for ($i = 0; $i < $minConnections; $i++) {
            try {
                $connection = $this->createConnection();
                if ($connection) {
                    $this->idleConnections->enqueue($connection);
                    $this->stats['current_idle']++;
                }
            } catch (Exception $e) {
                $this->logError("初始化Redis连接池失败: " . $e->getMessage());
                $this->stats['connection_errors']++;
            }
        }
        
        $this->logInfo("Redis连接池初始化完成，创建了 {$this->stats['current_idle']} 个连接");
    }

    /**
     * 创建新的Redis连接
     */
    private function createConnection()
    {
        $connectionId = ++$this->connectionIdCounter;
        
        try {
            $redis = null;
            
            // 根据配置选择连接方式
            if (ENABLE_REDIS_SENTINEL == 0) {
                // 直连模式
                $redis = $this->createDirectConnection();
            } else {
                // Sentinel模式
                $redis = $this->createSentinelConnection();
            }
            
            if (!$redis) {
                throw new Exception("无法创建Redis连接");
            }

            $connectionInfo = [
                'id' => $connectionId,
                'redis' => $redis,
                'created_at' => time(),
                'last_used' => time(),
                'is_healthy' => true,
                'selected_db' => 0  // 记录当前选择的数据库
            ];

            $this->stats['total_created']++;
            $this->logDebug("创建新Redis连接 ID: {$connectionId}");
            
            return $connectionInfo;
            
        } catch (Exception $e) {
            $this->logError("创建Redis连接失败: " . $e->getMessage());
            $this->stats['connection_errors']++;
            throw $e;
        }
    }

    /**
     * 创建直连Redis连接
     */
    private function createDirectConnection()
    {
        $redis = new Redis();
        $connected = $redis->connect(REDISIP, REDISSOCKET, $this->config['connection_timeout'] ?? 5);
        
        if (!$connected) {
            throw new Exception("无法连接到Redis服务器: " . REDISIP . ":" . REDISSOCKET);
        }
        
        // 认证
        if (REDISPW) {
            $authResult = $redis->auth(REDISPW);
            if (!$authResult) {
                throw new Exception("Redis认证失败");
            }
        }
        
        return $redis;
    }

    /**
     * 创建Sentinel模式Redis连接
     */
    private function createSentinelConnection()
    {
        $sentinels = explode(',', REDIS_SENTINEL_HOSTS);
        shuffle($sentinels);
        
        foreach ($sentinels as $sentinelHost) {
            try {
                $sentinel = explode(':', $sentinelHost);
                $sentinelRedis = new Redis();
                
                $connected = $sentinelRedis->connect($sentinel[0], $sentinel[1], REDIS_SENTINEL_TIMEOUT ?? 5);
                if (!$connected) {
                    continue;
                }
                
                $masters = $sentinelRedis->rawCommand('SENTINEL', 'masters');
                $masterData = $this->parseArrayResult($masters);
                
                if (!empty($masterData)) {
                    $masterIp = $masterData[0]["ip"];
                    $masterPort = $masterData[0]["port"];
                    
                    $redis = new Redis();
                    $connected = $redis->connect($masterIp, $masterPort, $this->config['connection_timeout'] ?? 5);
                    
                    if ($connected) {
                        // 认证
                        if (REDISPW) {
                            $redis->auth(REDISPW);
                        }
                        
                        // 验证是否为主节点
                        $info = $redis->rawCommand('INFO', 'Replication');
                        if (strpos($info, "role:master") !== false) {
                            $sentinelRedis->close();
                            return $redis;
                        }
                    }
                }
                
                $sentinelRedis->close();
                
            } catch (Exception $e) {
                $this->logDebug("Sentinel连接失败: " . $e->getMessage());
                continue;
            }
        }
        
        throw new Exception("无法通过Sentinel获取Redis主节点连接");
    }

    /**
     * 解析Sentinel返回的数组结果
     */
    private function parseArrayResult(array $data)
    {
        $result = array();
        $count = count($data);
        for ($i = 0; $i < $count;) {
            $record = $data[$i];
            if (is_array($record)) {
                $result[] = $this->parseArrayResult($record);
                $i++;
            } else {
                $result[$record] = $data[$i + 1];
                $i += 2;
            }
        }
        return $result;
    }

    /**
     * 获取连接
     */
    public function getConnection()
    {
        // 首先尝试从空闲连接池获取
        if (!$this->idleConnections->isEmpty()) {
            $connection = $this->idleConnections->dequeue();
            $this->stats['current_idle']--;
            
            // 检查连接是否仍然有效
            if ($this->isConnectionHealthy($connection)) {
                $connection['last_used'] = time();
                $connection['selected_db'] = 0; // 重置为默认数据库
                $this->activeConnections[$connection['id']] = $connection;
                $this->stats['current_active']++;
                $this->stats['peak_active'] = max($this->stats['peak_active'], $this->stats['current_active']);
                
                $this->logDebug("从Redis连接池获取连接 ID: {$connection['id']}");
                return $connection;
            } else {
                // 连接无效，销毁并创建新连接
                $this->destroyConnection($connection);
            }
        }

        // 如果没有空闲连接，检查是否可以创建新连接
        $totalConnections = $this->stats['current_active'] + $this->stats['current_idle'];
        $maxConnections = $this->config['max_connections'] ?? 10;
        
        if ($totalConnections < $maxConnections) {
            try {
                $connection = $this->createConnection();
                $connection['last_used'] = time();
                $this->activeConnections[$connection['id']] = $connection;
                $this->stats['current_active']++;
                $this->stats['peak_active'] = max($this->stats['peak_active'], $this->stats['current_active']);
                
                $this->logDebug("创建新Redis连接 ID: {$connection['id']}");
                return $connection;
                
            } catch (Exception $e) {
                $this->logError("无法创建新Redis连接: " . $e->getMessage());
                throw new Exception("Redis连接池无法提供连接: " . $e->getMessage());
            }
        }

        throw new Exception("Redis连接池已满，无法获取新连接");
    }

    /**
     * 归还连接到连接池
     */
    public function releaseConnection($connection)
    {
        if (!isset($connection['id'])) {
            $this->logError("尝试归还无效Redis连接");
            return false;
        }

        $connectionId = $connection['id'];
        
        if (!isset($this->activeConnections[$connectionId])) {
            $this->logError("尝试归还未知Redis连接 ID: {$connectionId}");
            return false;
        }

        // 从活跃连接中移除
        unset($this->activeConnections[$connectionId]);
        $this->stats['current_active']--;

        // 检查连接是否健康
        if ($this->isConnectionHealthy($connection)) {
            // 检查空闲超时
            $idleTimeout = $this->config['idle_timeout'] ?? 300;
            $connectionAge = time() - $connection['created_at'];
            
            if ($connectionAge < $idleTimeout) {
                // 重置连接状态
                try {
                    $connection['redis']->select(0); // 重置到默认数据库
                    $connection['selected_db'] = 0;
                } catch (Exception $e) {
                    $this->logDebug("重置Redis连接状态失败: " . $e->getMessage());
                    $this->destroyConnection($connection);
                    return true;
                }
                
                // 连接仍然有效，归还到空闲池
                $this->idleConnections->enqueue($connection);
                $this->stats['current_idle']++;
                $this->logDebug("归还Redis连接到连接池 ID: {$connectionId}");
                return true;
            }
        }

        // 连接无效或超时，销毁连接
        $this->destroyConnection($connection);
        return true;
    }

    /**
     * 检查连接健康状态
     */
    private function isConnectionHealthy($connection)
    {
        if (!isset($connection['redis']) || !($connection['redis'] instanceof Redis)) {
            return false;
        }

        try {
            $connection['redis']->ping();
            return true;
        } catch (Exception $e) {
            $this->logDebug("Redis连接健康检查失败 ID: {$connection['id']}, 错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 销毁连接
     */
    private function destroyConnection($connection)
    {
        if (isset($connection['id'])) {
            $this->logDebug("销毁Redis连接 ID: {$connection['id']}");
        }
        
        // 关闭Redis连接
        if (isset($connection['redis']) && $connection['redis'] instanceof Redis) {
            try {
                $connection['redis']->close();
            } catch (Exception $e) {
                // 忽略关闭时的错误
            }
            $connection['redis'] = null;
        }
        
        $this->stats['total_destroyed']++;
    }

    /**
     * 启动健康检查定时器
     */
    private function startHealthCheck()
    {
        $interval = $this->config['health_check_interval'] ?? 60;
        
        $this->healthCheckTimer = Timer::add($interval, function() {
            $this->performHealthCheck();
        });
        
        $this->logInfo("启动Redis连接池健康检查，间隔: {$interval}秒");
    }

    /**
     * 执行健康检查
     */
    private function performHealthCheck()
    {
        $this->logDebug("开始Redis连接池健康检查");
        
        // 检查空闲连接
        $healthyConnections = new SplQueue();
        $unhealthyCount = 0;
        
        while (!$this->idleConnections->isEmpty()) {
            $connection = $this->idleConnections->dequeue();
            $this->stats['current_idle']--;
            
            if ($this->isConnectionHealthy($connection)) {
                $healthyConnections->enqueue($connection);
                $this->stats['current_idle']++;
            } else {
                $this->destroyConnection($connection);
                $unhealthyCount++;
                $this->stats['health_check_failures']++;
            }
        }
        
        $this->idleConnections = $healthyConnections;
        
        // 确保最小连接数
        $minConnections = $this->config['min_connections'] ?? 3;
        $currentTotal = $this->stats['current_active'] + $this->stats['current_idle'];
        
        if ($currentTotal < $minConnections) {
            $needCreate = $minConnections - $currentTotal;
            for ($i = 0; $i < $needCreate; $i++) {
                try {
                    $connection = $this->createConnection();
                    $this->idleConnections->enqueue($connection);
                    $this->stats['current_idle']++;
                } catch (Exception $e) {
                    $this->logError("健康检查时创建Redis连接失败: " . $e->getMessage());
                    break;
                }
            }
        }
        
        if ($unhealthyCount > 0) {
            $this->logInfo("Redis健康检查完成，清理了 {$unhealthyCount} 个无效连接");
        }
    }

    /**
     * 获取连接池统计信息
     */
    public function getStats()
    {
        return array_merge($this->stats, [
            'current_total' => $this->stats['current_active'] + $this->stats['current_idle']
        ]);
    }

    /**
     * 关闭连接池
     */
    public function close()
    {
        // 停止健康检查定时器
        if ($this->healthCheckTimer) {
            Timer::del($this->healthCheckTimer);
            $this->healthCheckTimer = null;
        }

        // 关闭所有空闲连接
        while (!$this->idleConnections->isEmpty()) {
            $connection = $this->idleConnections->dequeue();
            $this->destroyConnection($connection);
        }

        // 关闭所有活跃连接
        foreach ($this->activeConnections as $connection) {
            $this->destroyConnection($connection);
        }

        $this->activeConnections = [];
        $this->stats['current_active'] = 0;
        $this->stats['current_idle'] = 0;
        
        $this->logInfo("Redis连接池已关闭");
    }

    /**
     * 日志记录方法
     */
    private function logInfo($message)
    {
        \util\log\akcsLog::debug("[RedisConnectionPool][INFO] " . $message);
    }

    private function logDebug($message)
    {
        if ($this->config['debug'] ?? false) {
            \util\log\akcsLog::debug("[RedisConnectionPool][DEBUG] " . $message);
        }
    }

    private function logError($message)
    {
        \util\log\akcsLog::debug("[RedisConnectionPool][ERROR] " . $message);
    }

    /**
     * 析构函数
     */
    public function __destruct()
    {
        $this->close();
    }
}
