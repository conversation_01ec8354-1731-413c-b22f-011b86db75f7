<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use util\route\RouteCollect;
use Webman\Route;

global $gApp;
$gApp = \util\route\RouteCollect::getInstance();

require_once app_path('/service/resident/route.php');
require_once app_path('/service/office/route.php');
require_once app_path('/service/newoffice/route.php');
require_once app_path('/service/common/route.php');
require_once app_path('/service/smartlock/route.php');
require_once app_path('/service/smarthome/route.php');

$uriList = $gApp->getAllUri();
foreach($uriList as $uri)
{
    Route::any($uri, function ($request) {
        global $gApp;
        $projectType = \util\container\getProjectType();
        $callback = $gApp->getCallback($request->path(), $projectType);
        $response = '';
        if (is_callable($callback))
        {
            return $callback($request, $response);
        }
        \util\log\akcsLog::debug("route: " . $request->path() . " projectType: " . $projectType . " not found");
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED);
    });
}

Route::disableDefaultRoute();

