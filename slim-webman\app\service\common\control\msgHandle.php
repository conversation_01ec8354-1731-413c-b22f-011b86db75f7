<?php

namespace  common\control;

require_once __DIR__ . "/../../common/model/messageAccountList.php";

function setMsgRead($request, $response) 
{
    $bodyValue = $request->getParsedBody();
    $noticeSwitch = $bodyValue["notice"];
    $messageAccountListID = $bodyValue["message_account_list_id"];

    //通过UserInfoUUID获取所有的site信息
    $userData = \util\container\getUserData();

    // message_account_list_id不为空，则只设置指定消息为已读
    if ($messageAccountListID) {
        $ret = setMsgReadByMessageAccountListID($messageAccountListID);
    }
    // noticeSwitch为1，message_account_list_id为空，则设置所有消息为已读
    else if ($noticeSwitch == 1 && !$messageAccountListID) {
        $ret = setAllMsgRead($userData['UserInfoUUID']);
    }
    else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "param error");
    }

    if ($ret != 0) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "set msg read failed");
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function setAllMsgRead($userInfoUUID)
{
    $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($userInfoUUID);
    return \common\model\setMessageRead($personalAccountList);
}

function setMsgReadByMessageAccountListID($messageAccountListID)
{
    return \common\model\setMessageReadByMessageAccountListID($messageAccountListID);
}

