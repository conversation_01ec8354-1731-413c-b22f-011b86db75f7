<?php

namespace newoffice\control;

require_once __DIR__ . "/common.php";
require_once __DIR__ . "/../model/officePersonnel.php";
require_once __DIR__ . "/../../common/control/userconf.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/personalAccountUserInfo.php";
require_once __DIR__ . "/../../common/model/account.php";

// V7.0.0 - 新办公
function getLoginConfV70($request, $response)   
{
    $wordKey = '';
    $code = ERR_CODE_SUCCESS;

    // Get the user data.
    $userData = \util\container\getUserData();
    $role = $userData['Role'];

    // 新办公，必须使用7.0的APP版本
    if ($code == ERR_CODE_SUCCESS) {
        $version = \util\container\getApiVersion();
        if ($role == ROLE_TYPE_OFFICE_NEW_PER && floatval($version) < 7.0) {
            $code = ERR_CODE_INTERCEPT_UPGRADE;     // APP识别该错误号后，提示更新并退出APP
            $wordKey = LOGIN_INTERCEPT_WORD_KEY4;
        } 
    }

    // 判断有效登录时间
    if ($code == ERR_CODE_SUCCESS) {
        if($role == ROLE_TYPE_OFFICE_NEW_PER && \newoffice\control\IsOfficePersonnelValid($userData["MngUUID"], $userData["UUID"]) == false){
            $code = ERR_CODE_INTERCEPT_OK;     // APP识别该错误号后，提示更新并退出APP
            $wordKey = LOGIN_INTERCEPT_WORD_KEY3;
        }
    }

    if ($code == ERR_CODE_SUCCESS) {
        \common\control\getLoginInterceptCode($code, $wordKey);
    }

    if ($code == ERR_CODE_SUCCESS) {
        \common\control\getAppStatusCode($code);
    }

    $featureExpire = \resident\model\checkFeaturePlanIsExpire($userData['MngID']);
    $showTmpKey = \common\model\checkShowTmpkey($userData, $featureExpire);
    $datas = [
        "intercept_mode" => intval(\common\control\GetInterceptModeByCode($code)),
        "word_key" => strval($wordKey),
        "is_init" => "1",         // 新办公默认不用设置pin码
        "role" => strval($role),
        "check_dev" => 1,       // 新办公没有室内机
        "check_slave" => 1,     // 
        "is_site_link" => 0,    // 新办公没有多套房方案
        "app_status" => intval(\common\control\GetAppStatusByCode($code)),
        "show_tempkey" => intval($showTmpKey),
    ];
    return \util\response\setResponseMessage($response, $code, $datas);
}
