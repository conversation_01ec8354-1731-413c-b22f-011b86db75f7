<?php

namespace newoffice\model;

use PDO;

require_once __DIR__ . "/../../common/model/manegeFeature.php";

function getPersonnelCalltypeByPersonalAccountUUID($personalAccountUUID)
{       
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT CallType FROM OfficePersonnel WHERE PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    if(array_key_exists("CallType", $result) == false) {
        $result["CallType"] = 0;
    }

    return $result;
}

function getValidTimeByAccountUUID($PersonalAccountUUID)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("SELECT IsSetValidTime,ValidStartTime,ValidEndTime FROM OfficePersonnel WHERE PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $PersonalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result;
}


function getOfficePersonnelByUUID($PersonalAccountUUID)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("SELECT IsSmartPlusIntercom,AppIntercomeActive,AppIntercomeExpireTime > now() as CallNotExpire FROM OfficePersonnel WHERE PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $PersonalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result;
}

function updatePersonnelCalltype($userUUID, $calltype)
{
    $db = \util\container\getDb();

    $db->beginTransaction();

    $sthRole = $db->prepare("update OfficePersonnel set CallType=:Calltype WHERE PersonalAccountUUID = :PersonalAccountUUID");
    $sthRole->bindParam(':PersonalAccountUUID', $userUUID, PDO::PARAM_STR);
    $sthRole->bindParam(':Calltype', $calltype, PDO::PARAM_STR);
    $sthRole->execute();

    $db->commit();
}

function getCompanyPersonnelNum($companyUUID)
{
    $db = \util\container\getDb();

    $sth = $db->prepare("select count(*) as PersonnelNum from OfficePersonnel where OfficeCompanyUUID = :OfficeCompanyUUID");
    $sth->bindParam(':OfficeCompanyUUID', $companyUUID, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC)['PersonnelNum'];
}

function getIsEnableLandLineByUUID($personalAccountUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT IsEnableLandLine,AppIntercomeActive FROM OfficePersonnel WHERE PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    return $result['IsEnableLandLine'] && $result['AppIntercomeActive'];
}
