<?php

namespace  resident\control;

require_once __DIR__ . "/../../common/model/account.php";
require_once __DIR__ . "/../../resident/model/communityInfo.php";
require_once __DIR__ . "/../../office/model/officeInfo.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/distributorInfo.php";

//和用户无关，固定写死的配置
function getPMAppConstConf()
{
    $constConf = [];
    $constConf['show_payment'] = 0;
    $constConf['show_subscription'] = 0;    
    $constConf['sip_server'] = G_PBX_IPV4;
    $constConf['sip_server_ipv6'] = G_PBX_IPV6;
    $constConf['video_res'] = "2";
    $constConf['video_bitrate'] = "512";
    $constConf['video_storage_time'] = "0";
    $constConf['have_public_dev'] = "1";
    $constConf['data_collection'] = 0; //app数据收集到网管系统 后续如果用户较多可采样收集
    $constConf['check_dev'] = 1; //检查室内机的收费方案flag
    $constConf['check_slave'] = 1;
    $constConf['show_pm'] = "0"; //pm账号不展示Property Manager入口
    $constConf['show_id_access'] = 0;
    return $constConf;
}
//直接从数据容器user_data中就能取到的配置
function getPMAppUserDataConf()
{
    $userDataConf = [];
    $userData = \util\container\getUserData(); 
    $userDataConf['sip'] = $userData['SipAccount'];
    $userDataConf['sip_passwd'] = \util\utility\passwdDecode($userData['SipPwd']);
    $userDataConf['display_name'] = $userData['Name'];
    $userDataConf['uid'] = $userData['UserAccount'];
    $userDataConf['uuid'] = $userData['UUID'];
    $userDataConf['node'] = $userData['Account'];
    $userDataConf['codec'] = strval($userData['Codec']); //0=PCMU, 8 =PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义
    $userDataConf['show_tempkey'] = intval($userData['TempKeyPermission']);
    $userDataConf['role'] = intval($userData['Role']);
    $userDataConf['motion_alert'] = \util\redisManage\getAppMotionStatus($userData['UserAccount']); 
    return $userDataConf;
}

//用户开关相关配置
function getPMAppSwitchConf()
{
    $switchConf = [];
    $userData = \util\container\getUserData(); 

    $enablePinConfig = 1;
    $isShowLandline = 1;
    $enableSmarthome = 0;
    $communityInfo = \resident\model\getCommunityInfo($userData['MngID']);
    if ($communityInfo) {
        $isNewComm = $communityInfo['IsNew'];
        if (!\resident\model\checkFeaturePlanIsExpire($userData['MngID']) && \common\model\checkCommunityFeaturePlan($userData['MngID'], FeatureItemPin)) {
            $enablePinConfig = \util\utility\switchHandle($communityInfo['Switch'], DevSwitchEnablePinConfig);
        } else {
            $enablePinConfig = 1;
        }
        $distributorInfo = \common\model\getDistributorInfoByMngUUID($userData['MngUUID']);
        if($distributorInfo['IsEnableAptChargePlan']){
            $personalAccountCnf = \common\model\getPersonalAccountCnf($userData['Account']);
            $isShowLandline = $personalAccountCnf["EnableLandline"];
        }
        else{
            $isShowLandline = \util\utility\switchHandle($communityInfo['Switch'], DevSwitchEnableLandline);
        }
        $enableSmarthome = \util\utility\switchHandle($communityInfo['Switch'], CommunitySwitchEnableSmartHome);
    }
    $switchConf['enable_pin_config'] = intval($enablePinConfig);
    $switchConf['show_landline'] = intval($isShowLandline);
    $switchConf['enable_smarthome'] = intval($enableSmarthome);
    $switchConf['is_old_community'] = $isNewComm == 1 ? 0 : 1;
    
    $switch = intval($userData['UserSwitch']);
    $switchConf['enable_confirm_flag'] = \util\utility\switchHandle($switch, PersonlAccountSwitchConfirm);  //高级设置开关
    $featureExpire = \resident\model\checkFeaturePlanIsExpire($userData['MngID']);
    $switchConf["enable_third_camera"] = \common\model\checkEnableThirdCamera($userData['MngID'], $featureExpire);

    if (\util\version\isNeedReturnPinInit()) {
        $pinInit = $enablePinConfig ? \util\utility\switchHandle($switch, PersonlAccountSwitchPinInit) : 1;  // 判断社区创建pin开关
        $switchConf['pin_init'] = intval($pinInit); //pin初始化标记
    }

    return $switchConf;
}
//transtype相关
function getPMAppTransTypeConf()
{
    $transTypeConf = [];
    $userData = \util\container\getUserData(); 
    $transType = $userData['SipType'];
    $mngSipType = \common\model\getUserMngSipType($userData['Role'], $userData['ParentID']);
    if ($mngSipType["SipType"] != APP_SIP_TYPE_NONE) {
        $transType = $mngSipType["SipType"];
    }
    $transType = \util\common\transSipType($userData['UUID'], $transType);
    $transTypeConf['trans_type'] = strval($transType); //0-udp, 1-tcp, 2-tls
    $transTypeConf['rtp_confuse'] = intval($mngSipType["RtpConFuse"]);

    return $transTypeConf;
}

//落地相关
function getPMAppLandlineConf()
{
    $landlineConf = [];
    $userData = \util\container\getUserData(); 
    $landlineConf['landline'] = \util\common\getPbxLandlineNumber($userData['PhoneCode'], $userData['Phone'], $userData['Phone2'], $userData['Phone3']);
    return $landlineConf;
}
    
//多套房相关
function getPMAppMultiSiteConf()
{
    $multiSiteConf = []; 
    $userData = \util\container\getUserData(); 

    $userInfo = \common\model\getAccountUserInfoByUUID($userData['UserInfoUUID']);
    if (!$userInfo) {
        $userInfo = \common\model\getUserInfoByUUID($userData['UserInfoUUID']);
    }
    $mainSipInfo = \common\model\getUserSipInfo($userInfo['AppMainUserAccount']);
    $multiSiteConf['main_sip'] = $userInfo['AppMainUserAccount'];
    $multiSiteConf['main_sip_passwd'] = \util\utility\passwdDecode($mainSipInfo['SipPwd']);
    $multiSiteConf['is_site_link'] = \common\control\checkIsMultiSiteUser();
    $siteinfo = array();
    \common\control\getCommSiteInfo($userData, $siteinfo);
    $multiSiteConf['room_name'] = $siteinfo['room_name'];
    $multiSiteConf['project_name'] = $siteinfo['project_name'];
    $multiSiteConf['room_title'] = $siteinfo['room_title'];

    return $multiSiteConf;
}

//过期处理相关
function  getPMAppExpireConf()
{
    $expireConf = []; 
    $userData = \util\container\getUserData(); 
    \common\model\checkAccountExpire2($expireConf, $userData);
    return $expireConf;
}

function getPMAppConf()
{
    //顺序不能调整，否则可能出现覆盖错误问题，比如过期的配置判断必须放最后
    $appConf = [];
    $appConf = array_merge($appConf, getPMAppConstConf());
    $appConf = array_merge($appConf, getPMAppUserDataConf());
    $appConf = array_merge($appConf, getPMAppSwitchConf());
    $appConf = array_merge($appConf, getPMAppTransTypeConf());
    $appConf = array_merge($appConf, getPMAppLandlineConf());
    $appConf = array_merge($appConf, getPMAppMultiSiteConf());
    $appConf = array_merge($appConf, getPMAppExpireConf());
    
    return $appConf;
}

function getPMAppUnreadMsg()
{
    $unreadData = [];
    $userData = \util\container\getUserData(); 
    $unreadData['activities_num'] = intval(\common\model\getActivitiesNum($userData));
    $unreadData['messages_num'] = intval(\common\model\getMessagesNumV65());
    return $unreadData;
}

function getPMAppThirdDev()
{
    $userData = \util\container\getUserData(); 
    $thirdPartyDevList = [];
    \common\model\getThirdPartyCameraList($userData, $thirdPartyDevList);
    // dormakaba锁
    $dormakabaLockList = \common\control\getDormakabaLockList();
    // salto锁
    $saltoLockList = \common\control\salto\getPmSaltoLockList();
    $thirdPartyDevList["lock_dev_list"] = $dormakabaLockList["lock_dev_list"];
    $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $saltoLockList);

    // nvr
    $nvrList = \common\control\thirdNvr\getPmThirdNvrList();
    $thirdPartyDevList["third_nvr_list"] = $nvrList;

    if(\util\version\isShowNewBrandLock())
    {
        //ITec锁
        $itecLockList = \common\control\iTec\getPmLockList();
        $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $itecLockList);
    }

    return $thirdPartyDevList;
}

function getPMAppDevList()
{
    $userData = \util\container\getUserData(); 
    $responseDevList = [];
    $pubDevList = [];
    \common\model\getPubDevicesList($userData, $pubDevList);

    $noMonitorList = \common\model\getNoMonitorList();
    $supportHighResolutionList = \common\model\getSupportHighResolutionMonitoringList();
    $manageProjectType = getPMManageProjectType($userData);

    $redis = \util\container\getRedis();
    $redis->select(4);
    foreach ($pubDevList as $row => $dev) {
        $curNode = [];
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce;
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = strval($dev['Type']);
        $curNode['is_public'] = '1';
        $curNode['dclient_ver'] = strval($dev['DclientVer']);
        $curNode['dtmf'] = '#';
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $agRelay = ALL_RELAY_ON;
        $agSecurityRelay = ALL_RELAY_ON;
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], $agRelay, RELAY_TYPE_LOCAL);
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], $agSecurityRelay, RELAY_TYPE_SE_LOCAL);
        }

        // 门口机pm不受开关限制，全部展示
        if ($curNode['dev_type'] == DEVICE_TYPE_STAIR ||
            $curNode['dev_type'] == DEVICE_TYPE_DOOR ||
            $curNode['dev_type'] == DEVICE_TYPE_ACCESS ) {
            foreach (['relay', 'security_relay'] as $key) {
                if (isset($curNode[$key])) {
                    foreach ($curNode[$key] as &$val) {
                        $val['show_home'] = "1";
                        $val['show_talking'] = "1";
                    }
                    unset($val);
                }
            }
        }

        $curNode['firmware'] = $dev['Firmware'];
        $curNode['is_need_monitor'] = \util\common\isPmNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware']);
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } else {
            $curNode['arming_function'] = 1;
        }
        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }

        if (\util\common\isPmManageNewProject($manageProjectType))
        {
            $curNode['camera_num'] = \util\utility\switchHandle($dev['Function'], DevFunctionSupportMultiMonitor) ? 2 : 1;
            $curNode['is_support_high_resolution'] = \util\common\isSupportHighResolutionMonitoring($supportHighResolutionList, $dev['Firmware']);
        }

        // 允许设备门常开开关
        $curNode['allow_hold_door'] = intval(\resident\model\getEnableShowHoldDoor($userData['UserAccount']));
        // pm app不受开关限制，永远允许call
        $curNode['is_need_call'] = 1;
        array_push($responseDevList, $curNode);
    }

    return $responseDevList;
}

function getPMUserConfV64($response)
{    
    $datas = [
        "app_conf" => getPMAppConf(),
        "dev_list" => getPMAppDevList(),
        "third_party_dev_list" => getPMAppThirdDev(),
        "unread_msg" => getPMAppUnreadMsg()
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);  
}

function getPMManageProjectType($userInfo)
{
    $project_info = \common\model\getAccountByUUID($userInfo['MngUUID']);
    if ($project_info['Grade'] == GRADE_TYPE_COMM) {
        $communityInfo = \resident\model\getCommunityInfo($userInfo['MngID']);
        if ($communityInfo) {
            $isNewComm = $communityInfo['IsNew'];
        }
        if ($isNewComm)
        {
            return PM_MANAGE_PROJECT_NEW_COMMUNITY;
        }
        return PM_MANAGE_PROJECT_OLD_COMMUNTIY;
    }
    else if ($project_info['Grade'] == GRADE_TYPE_OFFICE)
    {
        $officeInfo = \office\model\getOfficeInfo($userInfo['MngUUID']);
        if ($officeInfo) {
            $isNewOffice = $officeInfo['IsNew'];
        }
        if ($isNewOffice)
        {
            return PM_MANAGE_PROJECT_NEW_OFFICE;
        }
        return PM_MANAGE_PROJECT_OLD_OFFICE;
    }
    return PM_MANAGE_PROEJCT_NULL;
}
