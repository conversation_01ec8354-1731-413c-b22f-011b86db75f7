<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use PDO;
use support\bootstrap\DatabasePool;
use Exception;

require_once __DIR__ . "/../util/utility.php";
require_once __DIR__ . "/../util/medoo.php";
require_once __DIR__ . "/../util/auditlog.php";
require_once __DIR__ . "/../../support/bootstrap/DatabasePool.php";

/**
 * 全局资源中间件
 * 负责为每个请求初始化数据库连接、Redis连接等全局资源
 * 使用连接池优化数据库连接管理
 *
 * @package app\middleware
 */
class GlobalResourceMiddle implements MiddlewareInterface
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param callable $next
     * @return Response
     */
    public function process(Request $request, callable $next): Response
    {
        $dbConnection = null;
        $redisConnection = null;

        try {
            // 初始化全局资源
            $this->initializeGlobalResources($request);

            // 从连接池获取数据库连接
            $dbConnection = $this->getDatabaseConnection();

            // 设置数据库连接到全局容器
            $this->setDatabaseConnections($dbConnection);

            // 从连接池获取Redis连接
            $redisConnection = $this->getRedisConnection();

            // 设置Redis连接到全局容器
            $this->setRedisConnection($redisConnection);

            // 处理请求
            $response = $next($request);

            return $response;

        } catch (Exception $e) {
            // 连接池获取失败时的降级处理
            $this->handleConnectionFailure($e, $request);

            // 使用传统方式初始化数据库连接
            $this->initializeFallbackConnections();

            // 继续处理请求
            $response = $next($request);

            return $response;

        } finally {
            // 清理资源
            $this->cleanup($dbConnection, $redisConnection);
        }
    }

    /**
     * 初始化全局资源（非数据库相关）
     *
     * @param Request $request
     */
    private function initializeGlobalResources(Request $request)
    {
        $globalApp = \util\container\GlobalApp::getInstance();

        // 设置请求追踪ID
        $globalApp->set('akcs_trace_id', \util\utility\createTraceID(16));

        // 设置API版本
        $globalApp->set('akcs_api_version', $request->header('api-version'));
        $globalApp->set('api-version', $request->header('api-version'));

        // 设置认证Token
        $globalApp->set('akcs_token', $this->initToken($request));

        // 设置审计日志
        $globalApp->set('akcs_audit_log', $this->initAuditLog());

        // 延迟初始化的资源
        $globalApp->set('akcs_log_db', false); // 实际使用时初始化
        $globalApp->set('akcs_redis', false);  // 实际使用时初始化
    }

    /**
     * 从连接池获取数据库连接
     *
     * @return array 连接信息
     * @throws Exception
     */
    private function getDatabaseConnection()
    {
        try {
            $connection = DatabasePool::getConnection();

            if (!$connection || !isset($connection['pdo']) || !isset($connection['medoo'])) {
                throw new Exception('从连接池获取的连接无效');
            }

            return $connection;
        } catch (Exception $e) {
            \util\log\akcsLog::debug("从连接池获取连接失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 设置数据库连接到全局容器
     *
     * @param array $connection 连接信息
     */
    private function setDatabaseConnections($connection)
    {
        $globalApp = \util\container\GlobalApp::getInstance();

        // 设置Medoo和PDO连接
        $globalApp->set('akcs_medoo_db', $connection['medoo']);
        $globalApp->set('akcs_db', $connection['pdo']);

        // 保存连接信息用于归还
        $globalApp->set('akcs_db_connection', $connection);
    }

    /**
     * 从连接池获取Redis连接
     */
    private function getRedisConnection()
    {
        try {
            $connection = \support\bootstrap\DatabasePool::getRedisConnection();
            return $connection;
        } catch (Exception $e) {
            \util\log\akcsLog::debug("从Redis连接池获取连接失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 设置Redis连接到全局容器
     */
    private function setRedisConnection($redisConnection)
    {
        if ($redisConnection && isset($redisConnection['redis'])) {
            $globalApp = \util\container\GlobalApp::getInstance();
            $globalApp->set('akcs_redis', $redisConnection['redis']);
            $globalApp->set('akcs_redis_connection', $redisConnection);
        }
    }

    /**
     * 设置Redis延迟初始化
     */
    private function setRedisLazyInit()
    {
        $globalApp = \util\container\GlobalApp::getInstance();

        // 延迟初始化的资源（保持原有逻辑）
        $globalApp->set('akcs_redis', false);  // 实际使用时初始化
    }

    /**
     * 处理连接获取失败的情况
     *
     * @param Exception $e
     * @param Request $request
     */
    private function handleConnectionFailure(Exception $e, Request $request)
    {
        \util\log\akcsLog::debug("连接池不可用，启用降级模式: " . $e->getMessage());
        // 可以在这里添加告警通知
        // 例如：发送邮件、短信或推送到监控系统
    }

    /**
     * 初始化传统方式的数据库连接（降级模式）
     */
    private function initializeFallbackConnections()
    {
        try {
            $globalApp = \util\container\GlobalApp::getInstance();

            // 使用传统方式创建Medoo连接
            $medooDb = $this->createMedooConnection();
            $globalApp->set('akcs_medoo_db', $medooDb);

            // 设置PDO连接
            $globalApp->set('akcs_db', $medooDb->pdo);

            \util\log\akcsLog::debug("降级模式：使用传统数据库连接");

        } catch (Exception $e) {
            \util\log\akcsLog::debug("降级模式初始化失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 创建传统的Medoo连接
     *
     * @return \util\medoo\Medoo
     */
    private function createMedooConnection()
    {
        return new \util\medoo\Medoo([
            'database_name' => 'AKCS',
            'server' => AKCS_DATABASEIP,
            'port' => AKCS_DATABASEPORT,
            'username' => DATABASEUSER,
            'password' => DATABASEPWD
        ]);
    }



    /**
     * 清理资源
     *
     * @param array|null $dbConnection
     * @param array|null $redisConnection
     */
    private function cleanup($dbConnection, $redisConnection = null)
    {
        try {
            // 归还数据库连接到连接池
            if ($dbConnection) {
                DatabasePool::releaseConnection($dbConnection);
            }

            // 如果没有传入Redis连接，尝试从全局容器获取
            if (!$redisConnection) {
                $globalApp = \util\container\GlobalApp::getInstance();
                $redisConnection = $globalApp->get('akcs_redis_connection');
            }

            // 归还Redis连接到连接池
            if ($redisConnection) {
                DatabasePool::releaseRedisConnection($redisConnection);
            }

        } catch (Exception $e) {
            \util\log\akcsLog::debug("归还连接失败: " . $e->getMessage());
        } finally {
            // 清理全局容器（这一步很重要！）
            \util\container\GlobalApp::$instance = null;
        }
    }

    /**
     * 初始化认证Token
     *
     * @param Request $request
     * @return string
     */
    private function initToken(Request $request)
    {
        if ($request->get('token')) {
            return $request->get('token');
        } else if ($request->header('token')) {
            return $request->header('token');
        } else {
            return $request->header('x-auth-token');
        }
    }

    /**
     * 初始化审计日志
     *
     * @return \util\auditlog\AuditLog
     */
    private function initAuditLog()
    {
        return new \util\auditlog\AuditLog();
    }
}
