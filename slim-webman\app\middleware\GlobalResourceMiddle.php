<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use PDO;

require_once __DIR__ . "/../util/utility.php";
require_once __DIR__ . "/../util/medoo.php";
require_once __DIR__ . "/../util/auditlog.php";

/**
 * Class GlobalResourceMiddle
 * @package app\middleware
 */
class GlobalResourceMiddle implements MiddlewareInterface
{
    public function process(Request $request, callable $next): Response
    {
        \util\container\GlobalApp::getInstance()->set('akcs_trace_id', \util\utility\createTraceID(16));
        \util\container\GlobalApp::getInstance()->set('akcs_medoo_db', $this->initMedooDb());        
        \util\container\GlobalApp::getInstance()->set('akcs_db', $this->initDb());
        \util\container\GlobalApp::getInstance()->set('akcs_log_db', false); //实际使用时初始化
        \util\container\GlobalApp::getInstance()->set('akcs_redis', false); //实际使用了再初始化
        \util\container\GlobalApp::getInstance()->set('akcs_api_version', $request->header('api-version'));
        \util\container\GlobalApp::getInstance()->set('api-version', $request->header('api-version'));
        \util\container\GlobalApp::getInstance()->set('akcs_token', $this->initToken($request));
        \util\container\GlobalApp::getInstance()->set('akcs_audit_log', $this->initAuditLog());

        $response = $next($request);
        \util\container\GlobalApp::$instance = null;
        return $response;
    }

    private function initMedooDb() {
        $database = new \util\medoo\Medoo([
            'database_name' => 'AKCS',
            'server' => AKCS_DATABASEIP,
            'port' => AKCS_DATABASEPORT,
            'username' => 'dbuser01',
            'password' => DATABASEPWD
        ]);
        return $database;
    }

    private function initDb() {
        return \util\container\medooDb()->pdo;
    }
/*
    private function initDb()
    {
        $dbuser = "dbuser01";
        $dbpass = "Ak@56@<EMAIL>";
        $dbip = "*************";
        $dbport = 3306;
    
        $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCS";
        $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
        $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $dbConnection->query('set names utf8;');
        return $dbConnection;        
    }
*/

    private function initToken($request)
    {
        if ($request->get('token')) {
            return $request->get('token');
        } else if ($request->header('token')) {
            return $request->header('token');
        } else {
            return $request->header('x-auth-token');
        }
    }

    private function initAuditLog()
    {
        return new \util\auditlog\AuditLog();
    }
}
