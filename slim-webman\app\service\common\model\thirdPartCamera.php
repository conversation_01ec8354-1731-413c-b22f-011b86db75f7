<?php

namespace common\model;

use PDO;

function getThirdPartyCameraList($resultToken, &$thirdPartyDevList, $accessDevs = [])
{
    $db = \util\container\getDb();
    $cameraList = array();
    //单住户第三方摄像头根据主账户查找下发
    if ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        $sth = $db->prepare("select UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC,AllowEndUserMonitor,MonitoringPlatform from PersonalThirdPartCamera 
        where PersonalAccountUUID = :uuid");
        $sth->bindParam(':uuid', $resultToken['NodeUUID'], PDO::PARAM_STR);
        $sth->execute();
        $cameras = $sth->fetchALL(PDO::FETCH_ASSOC);
        //插入列表
        \common\model\createSingleHouseCameraList($cameras, $cameraList);
    } elseif ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        //社区用户第三方摄像头列表下发规则：1、如果未绑定设备，根据位置下发；2、如果绑定设备，根据设备的权限下发
        //最外围第三方摄像头
        $sth = $db->prepare("select UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC,UnitID,AllowEndUserMonitor,MonitoringPlatform from ThirdPartCamera 
        where ProjectUUID = :project_uuid and (Grade = ".COMMUNITY_DEVICE_TYPE_PUBLIC." or Grade = ".COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT.")");
        $sth->bindParam(':project_uuid', $resultToken['MngUUID'], PDO::PARAM_STR);
        $sth->execute();
        $publicCameras = $sth->fetchALL(PDO::FETCH_ASSOC);
        //插入列表
        \common\model\createCommunityPubCameraList($publicCameras, $accessDevs, $resultToken['UnitID'], $cameraList);
  
        //家庭第三方摄像头
        $sth = $db->prepare("select UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC,AllowEndUserMonitor,MonitoringPlatform from ThirdPartCamera 
        where ProjectUUID = :project_uuid and PersonalAccountUUID = :personal_uuid and Grade = ".COMMUNITY_DEVICE_TYPE_PERSONAL);
        $sth->bindParam(':project_uuid', $resultToken['MngUUID'], PDO::PARAM_STR);
        $sth->bindParam(':personal_uuid', $resultToken['NodeUUID'], PDO::PARAM_STR);
        $sth->execute();
        $nodeCameras = $sth->fetchALL(PDO::FETCH_ASSOC);
        \common\model\createCommunityAptCameraList($nodeCameras, $accessDevs, $cameraList);

    } elseif ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        $sth = $db->prepare("select UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC,UnitID from ThirdPartCamera 
        where ProjectUUID = :project_uuid and (Grade = ".COMMUNITY_DEVICE_TYPE_PUBLIC." or Grade = ".COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT.")");
        $sth->bindParam(':project_uuid', $resultToken['MngUUID'], PDO::PARAM_STR);
        $sth->execute();
        $publicCameras = $sth->fetchALL(PDO::FETCH_ASSOC);
        //插入列表
        \common\model\createPmCameraList($publicCameras, $cameraList);
    }

    $thirdPartyDevList['camera_dev_list'] = $cameraList;
}


function createCommunityAptCameraList($cameras, $accessDevs, &$cameraList)
{
    foreach ($cameras as $camera) {
        if ($camera['MAC'] && !array_key_exists($camera['MAC'], $accessDevs))
        {
            //绑定mac时，不在accessDevs中说明用户没有权限，跳出，否则插入摄像头列表
            continue;
        }
        // 处理摄像头
        processCamera($camera, $cameraList);
    }
}

function createCommunityPubCameraList($cameras, $accessDevs, $unitID, &$cameraList)
{
    foreach ($cameras as $camera) {
        //绑定mac时，不在accessDevs中说明用户没有权限，跳过
        if ($camera['MAC'] && !array_key_exists($camera['MAC'], $accessDevs))
        {
            continue;
        } 
        //未绑定mac的，不能获取其它楼栋的摄像头
        else if(!$camera['MAC'] && $camera['UnitID'] != 0 && $camera['UnitID'] != $unitID)
        {
            continue;
        }
        // 处理摄像头
        processCamera($camera, $cameraList);
        
    }
}

function createPmCameraList($cameras, &$cameraList)
{
    foreach ($cameras as $camera) 
    {
        //Pm 默认允许监控
        $isNeedMonitor = 1;
        insertCameraList($camera, $cameraList, $isNeedMonitor);   
    }
}

function createSingleHouseCameraList($cameras, &$cameraList)
{
    foreach ($cameras as $camera) {
        // 处理摄像头
       processCamera($camera, $cameraList);
        
    }
}

function insertCameraList($camera, &$cameraList, $isNeedMonitor)
{
    // 不需要监控且未link设备，则不下发，直接返回。
    if ($isNeedMonitor == 0 && empty($camera['MAC'])) {
        return; 
    }
    $cameraNode = array();
    $cameraNode['uuid'] = $camera['UUID'];
    $cameraNode['location'] = $camera['Location'];
    $cameraNode['rtsp_url'] = $camera['RtspAddress'];
    $cameraNode['username'] = $camera['RtspUserName'];
    $cameraNode['password'] = $camera['RtspPwd'];
    $cameraNode['bonded_device'] = $camera['MAC'];
    $cameraNode['is_need_monitor'] = $isNeedMonitor;
    array_push($cameraList, $cameraNode);
}

// 判断 App 能否监控三方摄像头并插入摄像头列表
function processCamera($camera, &$cameraList)
{
    if ($camera['AllowEndUserMonitor']) {
        if ($camera['MonitoringPlatform'] == MONITOR_TYPE_SMARTPLUS_AND_INDOOR_MANAGMENT || $camera['MonitoringPlatform'] == MONITOR_TYPE_ONLY_SMARTPLUS) 
        {
            $isNeedMonitor = 1;
            insertCameraList($camera, $cameraList, $isNeedMonitor);
        } 
        else 
        {
            $isNeedMonitor = 0;
            insertCameraList($camera, $cameraList, $isNeedMonitor);
            \util\log\akcsLog::debug("Apt Camera cannot Monitor. Camera UUID: " . $camera['UUID']);
        }
    } 
    else 
    {   
        $isNeedMonitor = 0;
        insertCameraList($camera, $cameraList, $isNeedMonitor);
        \util\log\akcsLog::debug("End user monitoring not allowed. Camera UUID: " . $camera['UUID']);
    }
}

