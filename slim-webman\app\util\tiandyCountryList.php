<?php

namespace util\countrylist;

class Country
{
    /**
     * @var string
     */
    public $areaName;

    /**
     * @var string
     */
    public $code;

    /**
     * @var string
     */
    public $domainAbbreviation;

    /**
     * @var string
     */
    public $isoCode;

    /**
     * @var string
     */
    public $pinyin;

    /**
     * @param string $code
     * @param string $pinyin
     * @param string $isoCode
     * @param string $areaName
     * @param string $domainAbbreviation
     */
    public function __construct($code, $pinyin, $isoCode, $areaName, $domainAbbreviation)
    {
        $this->code = $code;
        $this->pinyin = $pinyin;
        $this->isoCode = $isoCode;
        $this->areaName = $areaName;
        $this->domainAbbreviation = $domainAbbreviation;
    }
}

class CountryList
{
    /**
     * @var array
     */
    protected $countries = array();

    /**
     * @param array $countries
     */
    public function __construct($countries = array())
    {
        if (empty($countries)) {
            $countries = $this->loadFromDataDir();
        }

        $this->countries = $countries;
    }

    /**
     * @return array
     */
    final protected function loadFromDataDir()
    {
        $countries = json_decode(file_get_contents(__DIR__.'/tiandyCountryList.json'), true);
        $return = array();
        foreach ($countries as $country) {
            $return[$country['code']] = $country;
        }
        return $return;
    }

    /**
     * @param string $code
     * @return Country
     */
    public function findByCode($code)
    {
        $code = strtoupper($code);

        if (!isset($this->countries[$code])) {
            return false;
        }

        return new Country(
            $this->countries[$code]['code'],
            $this->countries[$code]['pinyin'],
            $this->countries[$code]['isoCode'],
            $this->countries[$code]['areaName'],
            $this->countries[$code]['domainAbbreviation']
        );
    }

    /**
     * @return Country[]
     */
    public function findAll()
    {
        return array_map(
            function ($country) {
                return new Country(
                    $country['code'],
                    $country['pinyin'],
                    $country['isoCode'],
                    $country['areaName'],
                    $country['domainAbbreviation']
                );
            },
            $this->countries
        );
    }
}