<?php

namespace common\control;

require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/manegeFeature.php";
require_once __DIR__ . "/../../resident/model/personalAccountCnf.php";

function getUserSetting($request, $response)
{
    $userData = \util\container\getUserData();

    //获取Alarm强提醒开关设置
    $alarmReminder = \common\model\getAlarmReminder($userData['UUID']);
    if(!array_key_exists("EnableStrongAlarm", $alarmReminder)) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "user alarm reminder setting no found");
    }
    $datasJson["enable_strong_alarm"] = intval($alarmReminder["EnableStrongAlarm"]);

    $datasJson["is_have_booking"] = 0;
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        if (\common\model\checkCommunityFeaturePlan($userData['MngID'], FeatureItemBooking)) {
            $datasJson["is_have_booking"] = 1;
        }
    }
    $datasJson["enable_show_hold_door"] = intval(\resident\model\getEnableShowHoldDoor($userData['UserAccount']));

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datasJson);
}

//Alarm强提醒开关设置
function setAlarmReminder($request, $response)
{
    $bodyValue = $request->getParsedBody();
    
    if(!array_key_exists("status", $bodyValue)) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "missing http body: status");
    }

    $strongAlarmSwitch = $bodyValue["status"];
    $userData = \util\container\getUserData();
    if(!\common\model\updateAlarmReminder($userData['UUID'], $strongAlarmSwitch)) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "update alarm reminder failed");
    }
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}


function setCallKit($request, $response)
{
    $bodyValue = $request->getParsedBody();
    
    if(!array_key_exists("status", $bodyValue)) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "missing http body: status");
    }

    $callkitSwitch = $bodyValue["status"];
    $userData = \util\container\getUserData();
    \common\model\setAppEnableCallkit($userData['AppMainUserAccount'], $callkitSwitch);
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}