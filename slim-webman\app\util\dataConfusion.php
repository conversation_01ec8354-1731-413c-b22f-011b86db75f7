<?php

namespace util\dataConfusion;

class DataConfusion
{
    const METHOD = 'AES-256-CBC';
    const SECRET_KEY = 'Akuvox1956131*69czeahaaew216023*';
    const IV = "0123456789000000";

    public static $instance = null;

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function encrypt($text)
    {
        if ($text === '' or is_null($text)) {
            return $text;
        }

        $res = openssl_encrypt($text, self::METHOD, self::SECRET_KEY . $text, 0, self::IV);

        return $res;
    }

    public function decrypt($text)
    {
        if ($text === '' or is_null($text)) {
            return $text;
        }

        $res = openssl_decrypt($text, self::METHOD, self::SECRET_KEY . $text, 0, self::IV);
       
        return $res;
    }
}
