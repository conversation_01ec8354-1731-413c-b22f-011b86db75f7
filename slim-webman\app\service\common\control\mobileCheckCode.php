<?php

namespace common\control;

require_once __DIR__ . "/../../common/model/verificationCode.php";
require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../../common/model/smsRateLimit.php";
require_once __DIR__ . "/../../common/model/awsRedirect.php";
require_once __DIR__ . "/../../common/model/accountMap.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";

function updateAndSendEndUserVerificationCode($request, $response)
{
    $bodyValue = $request->getParsedBody();
    $mobileNumber = $bodyValue['MobileNumber'];
    $type = $bodyValue['Type'];
    $areaCode = $bodyValue['AreaCode'];
    
    $userInfo = \common\model\getUserInfoByMobileNumber($mobileNumber);
    if (!$userInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "Mobile Number doesn't exist" );
    }

    $userConf = \common\model\getCommInfo($userInfo['AppLastLoginUserAccount']);
    
    $userConf = array();
    // 若last_login_account存在则使用last_login_account
    if (strlen($userInfo['AppLastLoginUserAccount']) > 0) {
        $userConf = \common\model\getCommInfo($userInfo['AppLastLoginUserAccount']);
    } 
    // 若last_login_account没查询到账号(可能被删除)，则使用main_user_account
    if (!$userConf) {
        $userConf = \common\model\getCommInfo($userInfo['AppMainUserAccount']);
    }

    $phoneCode = $userConf["PhoneCode"];
    // 区号校验
    if (!checkPhoneCode($areaCode, $phoneCode)) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "AreaCode Error");
    }
    // 短信限流校验
    if (checkSmsRateIsLimited($request, $mobileNumber, SMS_CODE_USER_TYPE_END_USER)) {
        return \util\response\setResponseMessage($response, ERR_CODE_SMS_RATE_LIMIT);
    }

    // 生成验证码
    $code = mt_rand(100000, 999999);

    \common\model\checkRedirectAndUpdateCode($code, $userConf); 

    \common\model\updateVerificationCode($userConf['UserAccount'], $code);

    sendVerificationCode($type, $code, $phoneCode, $mobileNumber, SMS_CODE_USER_TYPE_END_USER);

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);

}

function updateAndSendNewOfficeAdminVerificationCode($request, $response)
{
    $bodyValue = $request->getParsedBody();
    $type = $bodyValue['Type'];
    $areaCode = $bodyValue['AreaCode'];
    $mobileNumber = $bodyValue['MobileNumber'];

    $userInfo = \common\model\getAccountUserInfoByPhone($mobileNumber);
    if (!$userInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "Mobile Number doesn't exist" );
    }

    $accountUUID = \common\model\getAccountUUIDByUserInfoUUID($userInfo['UUID']);
    if (!$accountUUID) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "Account UUID doesn't exist" );
    }

    $officeAdmin = \newoffice\model\getOfficeAdminByAccountUUID($accountUUID);
    if (!$officeAdmin) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "Office Admin doesn't exist" );
    }

    $phoneCode = $officeAdmin['PhoneCode'];
    // 区号校验
    if (!checkPhoneCode($areaCode, $phoneCode)) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "AreaCode Error");
    }

    $adminPerConf = \common\model\getPersonalAccountInfoByUUID($officeAdmin['PersonalAccountUUID']);
    if (!$adminPerConf) {
        return \util\response\setResponseMessage($response, ERR_CODE_PHONE_NOT_SUPPORT_ADMIN);
    }

    // 短信限流校验
    if (checkSmsRateIsLimited($request, $mobileNumber, SMS_CODE_USER_TYPE_NEW_OFFICE_ADMIN)) {
        return \util\response\setResponseMessage($response, ERR_CODE_SMS_RATE_LIMIT);
    }

    // 生成验证码
    $code = mt_rand(100000, 999999);

    $adminPerConf['UserAccount'] = $adminPerConf['Account'];
    \common\model\checkRedirectAndUpdateCode($code, $adminPerConf); 

    \common\model\updateVerificationCode($adminPerConf['UserAccount'], $code);

    sendVerificationCode($type, $code, $phoneCode, $mobileNumber, SMS_CODE_USER_TYPE_NEW_OFFICE_ADMIN);

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function updateAndSendVerificationCode($request, $response)
{
    $bodyValue = $request->getParsedBody();
    $areaCode = $bodyValue['AreaCode'];
    $mobileNumber = $bodyValue['MobileNumber'];
    $userType = $bodyValue['UserType'] ? $bodyValue['UserType'] : SMS_CODE_USER_TYPE_END_USER; // 兼容旧接口，默认是end user

    if (!$mobileNumber || !$areaCode) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "Mobile Number or Area Code doesn't exist" );
    }

    if ($userType == SMS_CODE_USER_TYPE_NEW_OFFICE_ADMIN) {
        return updateAndSendNewOfficeAdminVerificationCode($request, $response);
    } else if ($userType == SMS_CODE_USER_TYPE_END_USER) {
        return updateAndSendEndUserVerificationCode($request, $response);
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "User Type Not Supported");
    }
}

function checkPhoneCode($report_phone_code, $user_phone_code)
{
    if (!$report_phone_code || !$user_phone_code) {
        return false;
    }

    if ($report_phone_code != $user_phone_code) {
        return false;
    }
    return true;
}

function checkSmsRateIsLimited($request, $mobileNumber, $userType)
{
    if (\util\version\isNeedSmsRateLimited())
    {
        return \common\model\isSmsRateLimited($request, $mobileNumber, $userType);
    }
    return false;
}


