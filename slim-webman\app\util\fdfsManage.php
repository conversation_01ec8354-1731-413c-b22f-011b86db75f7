<?php

namespace util\fdfs;

function storagePictoFdfs($faceFilePath, $beforePath='')
{
    $ret = false;
    $tracker = fastdfs_tracker_get_connection();
    if (!$tracker) {
        $error_no = fastdfs_get_last_error_no();
        $error_info = fastdfs_get_last_error_info();
        \util\log\akcsLog::debug("fastdfs_tracker_get_connection falied! Error no: " . $error_no . ", Error info: " . $error_info);
        return $ret;
    }
    $storage = fastdfs_tracker_query_storage_store(FACE_FDFS_GROUP);
    if (!$storage) {
        $error_no = fastdfs_get_last_error_no();
        $error_info = fastdfs_get_last_error_info();
        \util\log\akcsLog::debug("fastdfs_tracker_query_storage_store falied! Error no: " . $error_no . ", Error info: " . $error_info);
        return $ret;
    }
    $server = fastdfs_connect_server($storage['ip_addr'], $storage['port']);
    if (!$server) {
        $error_no = fastdfs_get_last_error_no();
        $error_info = fastdfs_get_last_error_info();
        \util\log\akcsLog::debug("fastdfs_connect_server falied! Error no: " . $error_no . ", Error info: " . $error_info);
        return $ret;
    }
    $storage['sock'] = $server['sock'];

    if ($beforePath) {
        $beforeFile = explode('/', $beforePath, 3); //拆分/group1/M00/1D/71/rBIp3GEvI1mAVD7XAAB7l2czVOE82967.jpg
        if (strstr($beforeFile[1], 'group')) {  //如果存在fdfs的图片链接，则对应删除
            fastdfs_storage_delete_file($beforeFile[1], $beforeFile[2]);
        }
    }

    $file_info = fastdfs_storage_upload_by_filename($faceFilePath, null, array(), null, $tracker, $storage);
    if ($file_info) {
        $group_name = $file_info['group_name'];
        $remote_filename = $file_info['filename'];
        $ret = '/'.$file_info['group_name'].'/'.$file_info['filename'];
    }

    fastdfs_disconnect_server($storage);
    return $ret;
}

function fdfs_del_pic_by_url($url)
{
    $beforeFile = explode('/', $url, 3); //拆分/group1/M00/1D/71/rBIp3GEvI1mAVD7XAAB7l2czVOE82967.jpg
    //如果存在fdfs的图片链接，则对应删除
    if (strstr($beforeFile[1], 'group')) {
        fastdfs_storage_delete_file($beforeFile[1], $beforeFile[2]);
        return true;
    } else {
        return false;
    }
}