<?php

namespace resident\model;
use PDO;
function updatePersonalDevicesRelayHoldDelayByUUID($deviceUUID, $autoCloseTime, $relayID) 
{
    $userData = \util\container\getUserData();
    $db = \util\container\getDB();
    $device = \common\model\getDeviceInfoByRoleAndUUID($userData['Role'], $deviceUUID);
    if (!$device) 
    {
        return -1;
    }
    
    $relayData = json_decode($device['Relay'], true);
    if ($relayData === null) 
    {
        $relayData = [];
    }
     
    $relayIndex = intval($relayID);
    
    // 确保数组有足够的元素
    while (count($relayData) <= $relayIndex) 
    {
        $relayData[] = [];
    }
    
    $relayData[$relayIndex]['hold_delay'] = $autoCloseTime;
    
    $relayJson = json_encode($relayData);
    
    $updateSql = "UPDATE PersonalDevices SET Relay = :relay WHERE UUID = :uuid";
    $updateStmt = $db->prepare($updateSql);
    $updateStmt->bindParam(':relay', $relayJson, PDO::PARAM_STR);
    $updateStmt->bindParam(':uuid', $deviceUUID, PDO::PARAM_STR);
    $result = $updateStmt->execute();
    
    if ($result) 
    {
        return 0;
    } 
    else 
    {
        return -1;
    }
} 