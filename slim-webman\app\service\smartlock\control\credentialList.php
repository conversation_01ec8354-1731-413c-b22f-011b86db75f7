<?php

namespace  smartlock\control;

require_once __DIR__ . "/../../smartlock/model/smartLockShadow.php";


function getCredentialList($request, $response)
{
    // 鉴权
    $user = $request->header('Username');
    $pwd = $request->header('Password');
    $mqttClientId = $request->header('Client-Id');
    $clientId = \util\smartlockAdapt\mqttClientToRealClient($mqttClientId);

    // 终端锁鉴权
    $userParts = explode('-', $user);
    if (count($userParts) < 2) {
        \util\log\akcsLog::debug("invalid user format: $user");
        return json([
            'success' => false,
            'timestamp' => time(),
            'configuration' => []
        ]);
    }
    
    $deviceStyle = $userParts[0]; 
    $deviceMac = $userParts[1]; 
    
    if ($deviceStyle == SMARTLOCK_MQTT_CLIENT_FLAG_SL50
        && \common\model\SmartLockMqttAuthCheck($deviceMac, $clientId, $pwd)) {
        // 鉴权通过
    } else {
        \util\log\akcsLog::debug("auth error. user:$user, client_id:$mqttClientId, pwd:$pwd");
        return json([
            'success' => false,
            'timestamp' => time(),
            'configuration' => []
        ]);
    }

    // 获取configuration
    $smartLockShadow = \smartlock\model\getLockConfiguration($clientId);
    $configuration = $smartLockShadow["Configuration"] ?? '';
    $retConfiguration = [];

    $param = [];

    // 处理configuration字段
    if (!empty($configuration)) {
        $root = json_decode($configuration, true);
        if ($root !== null && isset($root['configuration'])) {
            $retConfiguration = $root['configuration'];
        }
    }

    return json([
        'success' => true,
        'timestamp' => time(),
        'configuration' => $retConfiguration,
    ]);
}