<?php

// 简单测试Bootstrap接口是否正确
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/define.php';
require_once __DIR__ . '/app/config/dynamic_config.php';

// 测试Bootstrap接口
try {
    echo "测试Bootstrap接口...\n";
    
    // 检查接口是否存在
    if (!interface_exists('Webman\Bootstrap')) {
        throw new Exception('Webman\Bootstrap接口不存在');
    }
    echo "✓ Webman\Bootstrap接口存在\n";
    
    // 检查DatabasePool类是否存在
    require_once __DIR__ . '/support/bootstrap/DatabasePool.php';
    if (!class_exists('support\bootstrap\DatabasePool')) {
        throw new Exception('DatabasePool类不存在');
    }
    echo "✓ DatabasePool类存在\n";
    
    // 检查是否实现了Bootstrap接口
    $reflection = new ReflectionClass('support\bootstrap\DatabasePool');
    if (!$reflection->implementsInterface('Webman\Bootstrap')) {
        throw new Exception('DatabasePool类没有实现Bootstrap接口');
    }
    echo "✓ DatabasePool类正确实现了Bootstrap接口\n";
    
    // 检查start方法是否存在
    if (!$reflection->hasMethod('start')) {
        throw new Exception('DatabasePool类缺少start方法');
    }
    echo "✓ DatabasePool类有start方法\n";
    
    // 检查start方法签名
    $startMethod = $reflection->getMethod('start');
    if (!$startMethod->isStatic()) {
        throw new Exception('start方法必须是静态方法');
    }
    echo "✓ start方法是静态方法\n";
    
    $parameters = $startMethod->getParameters();
    if (count($parameters) !== 1) {
        throw new Exception('start方法必须有一个参数');
    }
    echo "✓ start方法参数正确\n";
    
    echo "\n🎉 所有测试通过！Bootstrap接口修复成功！\n";
    
} catch (Exception $e) {
    echo "\n❌ 测试失败: " . $e->getMessage() . "\n";
    exit(1);
}
