# SMProxy集成文档

## 概述

本文档描述了如何将SMProxy（Swoole MySQL Proxy）集成到slim-webman项目中。SMProxy是一个基于Swoole的MySQL数据库中间件，提供连接池、读写分离、负载均衡等高级功能。

## SMProxy简介

SMProxy是一个轻量级的MySQL数据库中间件，具有以下特性：

- **连接池管理**: 高效的数据库连接复用
- **读写分离**: 自动路由读写请求到不同的数据库实例
- **负载均衡**: 支持多个数据库实例的负载均衡
- **协程调度**: 基于Swoole协程，高并发性能
- **MySQL兼容**: 完全兼容MySQL协议，无需修改应用代码
- **事务支持**: 完整的MySQL事务支持
- **跨语言**: 支持任何MySQL客户端

## 集成架构

```
应用层 (slim-webman)
       ↓
SMProxy中间件 (端口3366)
       ↓
MySQL数据库集群 (主从/集群)
```

### 连接优先级

1. **SMProxy连接** (优先级最高)
2. **内置连接池** (备用方案)
3. **直连MySQL** (降级方案)

## 安装和配置

### 1. 环境要求

- PHP 8.1+
- Swoole扩展 4.8+
- MySQL 5.5+
- Docker (可选)

### 2. SMProxy部署

#### 方式一：Docker部署 (推荐)

```bash
cd slim-webman/smproxy
docker-compose up -d
```

#### 方式二：直接部署

```bash
# 克隆SMProxy项目
git clone https://github.com/louislivi/SMProxy.git smproxy-source
cd smproxy-source

# 安装依赖
composer install

# 复制配置文件
cp conf/database.json.example conf/database.json
cp conf/server.json.example conf/server.json

# 启动SMProxy
php bin/SMProxy start
```

### 3. 配置文件

#### 数据库配置 (`smproxy/conf/database.json`)

```json
{
  "database": {
    "account": {
      "root": {
        "user": "root",
        "password": "123456"
      }
    },
    "serverInfo": {
      "main_server": {
        "write": {
          "host": ["127.0.0.1"],
          "port": 3306,
          "timeout": 5,
          "account": "root"
        },
        "read": {
          "host": ["127.0.0.1"],
          "port": 3306,
          "timeout": 5,
          "account": "root",
          "startConns": "swoole_cpu_num()*5",
          "maxSpareConns": "swoole_cpu_num()*10",
          "maxSpareExp": 3600,
          "maxConns": "swoole_cpu_num()*20"
        }
      }
    },
    "databases": {
      "akcs_db": {
        "serverInfo": "main_server",
        "startConns": "swoole_cpu_num()*3",
        "maxSpareConns": "swoole_cpu_num()*5",
        "maxSpareExp": 3600,
        "maxConns": "swoole_cpu_num()*10",
        "charset": "utf8mb4"
      }
    }
  }
}
```

#### 服务器配置 (`smproxy/conf/server.json`)

```json
{
  "server": {
    "user": "root",
    "password": "123456",
    "charset": "utf8mb4",
    "host": "0.0.0.0",
    "port": "3366",
    "mode": "SWOOLE_PROCESS",
    "sock_type": "SWOOLE_SOCK_TCP",
    "swoole": {
      "worker_num": "swoole_cpu_num()",
      "max_coro_num": 8000,
      "open_tcp_nodelay": true,
      "daemonize": false
    }
  }
}
```

#### slim-webman配置 (`config/smproxy.php`)

```php
return [
    'enabled' => env('SMPROXY_ENABLED', true),
    'host' => env('SMPROXY_HOST', '127.0.0.1'),
    'port' => env('SMPROXY_PORT', 3366),
    'username' => env('SMPROXY_USERNAME', 'root'),
    'password' => env('SMPROXY_PASSWORD', '123456'),
    'charset' => env('SMPROXY_CHARSET', 'utf8mb4'),
    'timeout' => env('SMPROXY_TIMEOUT', 5),
    
    'health_check' => [
        'enabled' => true,
        'interval' => 30,
        'timeout' => 5,
    ],
    
    'failover' => [
        'enabled' => true,
        'fallback_to_direct' => true,
        'retry_times' => 3,
    ],
];
```

## 使用方法

### 1. 基本使用

现有代码无需修改，继续使用原有接口：

```php
// 获取数据库连接（自动使用SMProxy）
$db = \util\container\getDb();

// 正常使用数据库操作
$result = $db->query('SELECT * FROM users')->fetchAll();

// 使用Medoo
$medoo = \util\container\medooDb();
$users = $medoo->select('users', '*');
```

### 2. 直接使用SMProxy管理器

```php
use util\smproxy\SMProxyManager;

// 获取SMProxy管理器
$config = config('smproxy');
$manager = SMProxyManager::getInstance($config);

// 检查健康状态
$isHealthy = $manager->checkHealth();

// 获取连接
$connection = $manager->getConnection();

// 获取统计信息
$stats = $manager->getStats();
```

### 3. 环境变量配置

在 `.env` 文件中配置：

```env
# SMProxy配置
SMPROXY_ENABLED=true
SMPROXY_HOST=127.0.0.1
SMPROXY_PORT=3366
SMPROXY_USERNAME=root
SMPROXY_PASSWORD=123456
```

## 管理API

### SMProxy状态管理

- `GET /admin/smproxy/status` - 获取SMProxy状态
- `GET /admin/smproxy/test` - 测试SMProxy连接
- `GET /admin/smproxy/performance` - 性能测试
- `GET /admin/smproxy/config` - 获取配置信息
- `POST /admin/smproxy/reload` - 重新加载配置
- `GET /admin/smproxy/source` - 获取连接源信息
- `GET /admin/smproxy/info` - 获取详细信息

### 响应示例

```json
{
  "success": true,
  "data": {
    "enabled": true,
    "healthy": true,
    "stats": {
      "threads_connected": 5,
      "max_connections": 1000,
      "connection_utilization": 0.5
    },
    "timestamp": "2024-01-01 12:00:00"
  }
}
```

## 监控和告警

### 关键指标

- **连接池使用率**: 当前连接数 / 最大连接数
- **响应时间**: 查询响应时间
- **健康状态**: SMProxy服务可用性
- **故障转移**: 降级到备用连接的次数

### 健康检查

SMProxy管理器会定期执行健康检查：

```php
// 自动健康检查（每30秒）
$manager = SMProxyManager::getInstance($config);
$isHealthy = $manager->checkHealth();

// 手动健康检查
curl http://localhost:8079/admin/smproxy/status
```

## 故障转移

### 自动降级

当SMProxy不可用时，系统会自动降级：

1. **SMProxy连接失败** → 尝试内置连接池
2. **连接池不可用** → 降级到直连MySQL
3. **记录降级日志** → 便于问题排查

### 故障恢复

- 定期检查SMProxy健康状态
- 服务恢复后自动切换回SMProxy
- 支持手动强制切换连接源

## 性能优化

### SMProxy配置优化

```json
{
  "swoole": {
    "worker_num": "swoole_cpu_num()",
    "max_coro_num": 8000,
    "open_tcp_nodelay": true
  }
}
```

### 连接池参数调优

- **startConns**: 初始连接数 = CPU核数 × 3-5
- **maxConns**: 最大连接数 = CPU核数 × 10-20
- **maxSpareExp**: 空闲超时 = 3600秒

### 环境建议

- **开发环境**: 较少连接数，启用调试
- **测试环境**: 中等连接数，启用性能测试
- **生产环境**: 较多连接数，关闭调试

## 测试

### 运行测试

```bash
# SMProxy功能测试
php tests/SMProxyTest.php

# 连接池测试
php tests/RedisPoolTest.php

# 数据库连接测试
curl http://localhost:8079/admin/smproxy/test
```

### 性能测试

```bash
# API性能测试
curl "http://localhost:8079/admin/smproxy/performance?count=100"

# 压力测试
ab -n 1000 -c 10 http://localhost:8079/admin/smproxy/test
```

## 故障排除

### 常见问题

1. **SMProxy启动失败**
   - 检查端口3366是否被占用
   - 确认MySQL服务可访问
   - 查看SMProxy日志

2. **连接超时**
   - 检查网络连接
   - 调整timeout配置
   - 确认防火墙设置

3. **性能问题**
   - 调整连接池参数
   - 检查MySQL服务器性能
   - 优化SQL查询

### 日志查看

```bash
# SMProxy日志
tail -f smproxy/logs/system.log

# slim-webman日志
tail -f logs/webman.log

# Docker日志
docker-compose logs -f smproxy
```

## 最佳实践

1. **配置管理**: 使用环境变量管理不同环境的配置
2. **监控告警**: 设置关键指标的监控和告警
3. **故障演练**: 定期进行故障转移演练
4. **性能测试**: 定期进行性能基准测试
5. **日志管理**: 合理配置日志级别和轮转策略

## 与现有连接池的对比

| 特性 | SMProxy | 内置连接池 | 直连MySQL |
|------|---------|------------|-----------|
| 连接复用 | ✅ | ✅ | ❌ |
| 读写分离 | ✅ | ❌ | ❌ |
| 负载均衡 | ✅ | ❌ | ❌ |
| 跨语言支持 | ✅ | ❌ | ✅ |
| 部署复杂度 | 中等 | 低 | 低 |
| 性能开销 | 低 | 极低 | 无 |

## 升级和维护

- 定期更新SMProxy到最新版本
- 监控SMProxy社区动态和安全更新
- 根据业务增长调整配置参数
- 定期备份配置文件

---

**注意**: SMProxy集成为可选功能，可以通过配置开关控制。当SMProxy不可用时，系统会自动降级到现有的连接池或直连方式，确保服务的高可用性。
