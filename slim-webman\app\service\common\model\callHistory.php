<?php

namespace common\model;

use PDO;

require_once __DIR__ . "/logSlice.php";

function getActivitiesNum($resultToken)
{   
    $projectUuid = \common\model\getLogProjectUUID($resultToken);
    $akcsTables = [];
    $logTables = [];
    \common\model\getSqlTables("CallHistory", $projectUuid, $akcsTables, $logTables);
    $callNum = 0;
    getLOGCallHistoryCount($logTables, $resultToken['UserAccount'], $callNum);

    return intval($callNum);
}

function getLOGCallHistoryCount($logTables, $account, &$callNum)
{
    $db = \util\container\getLOGDb();
    foreach ($logTables as $table){
        try {
            $sth = $db->prepare("select count(*) as num from $table where CalleeID = :account and IsAnswer = 0 and Status = 0");
            $sth->bindParam(':account', $account, PDO::PARAM_STR);
            $sth->execute();
            $ret = $sth->fetch(PDO::FETCH_ASSOC);
            if ($ret) {
                $callNum += (int)$ret['num'];
            }
        } catch (\PDOException $e) {
            \util\log\akcsLog::debug("getLOGCallHistoryCount failed, error:" . $e->getMessage());
            continue;
        }
    }   
}

