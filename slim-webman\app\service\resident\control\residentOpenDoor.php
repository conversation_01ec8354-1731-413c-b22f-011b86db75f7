<?php

namespace resident\control;

require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/devices.php";
require_once __DIR__ . "/../../common/model/accessGroup.php";
require_once __DIR__ . "/../../resident/model/communityInfo.php";
require_once __DIR__ . "/../../office/control/officeOpenDoor.php";

function OpenSitedoor($request, $response)
{
    $userConf = \util\container\getUserData();  
    $postDatas = $request->getParsedBody();
    $mac = $postDatas['mac'];
    $relay = $postDatas['relay'];
    $securityRelay = $postDatas['security_relay'];
    $traceID = $postDatas['trace_id'];

    $canOpendoor = \resident\control\checkOpendoorPermission($userConf, $mac, $relay, $securityRelay);

    if ($canOpendoor) {
        $activateRepost = \resident\control\IsDevOpenDoorNeedRepost($mac);
        $datas = array();
        if ($traceID) {
            $datas['is_need_wait_response'] = 0;
            if (\util\common\DevSupportOpenDoorAck(\common\model\getDevicesFunctionByMac($mac)) && !$activateRepost)
            {
                $datas['is_need_wait_response'] = 1;
            }
        } else {
            $traceID = \util\utility\createTraceID(6);
        }
        \resident\control\executeOpenDoor($mac, $relay, $securityRelay, $userConf['UserAccount'], $activateRepost, $traceID);
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_NO_PERMISSION);
    }
}

function checkOpendoorPermission($userConf, $mac, $relay, $securityRelay)
{   
    if ($userConf['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userConf['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        // 单住户权限验证
        return \common\model\IsPersonalCanOpenDoor($userConf, $mac);
    } elseif ($userConf['Role'] == ROLE_TYPE_COMMUNITY_PM) { 
        // 社区PM权限验证
        return \common\model\isPmCanOpenDoor($userConf, $mac);
    } else {
        // 社区权限验证
        return \common\model\isCommCanOpenDoor($userConf, $mac, $relay, $securityRelay);
    }
}

function executeOpenDoor($mac, $relay, $securityRelay, $account, $activateRepost, $trace_id = '')
{
    if (!is_null($relay)) {
        \util\log\akcsLog::debug("OpenDoorNotify:mac=$mac,relay=$relay,uid=$account,repost=$activateRepost,trace_id=$trace_id");
        openDoorNotify(PROJECT_TYPE_RESIDENCE, $mac, $relay, $account, $activateRepost, $trace_id);
    }

    if (!is_null($securityRelay)) {
        \util\log\akcsLog::debug("OpenSecurityNotify:mac=$mac,relay=$securityRelay,uid=$account,repost=$activateRepost,trace_id=$trace_id");
        openSecurityRelayNotify(PROJECT_TYPE_RESIDENCE, $mac, $securityRelay, $account, $activateRepost, $trace_id);
    }
}

function IsDevOpenDoorNeedRepost($mac)
{
    //设备是否开启转流开关
    $is_dev_active_repost = \common\model\isDevActivateRepost($mac);

    //没开启转流，直接返回
    if (!$is_dev_active_repost)
    {
        return $is_dev_active_repost;
    }

    $dev_info = \common\model\getDevicesInfoByMac($mac);

    //非社区设备直接返回
    if (!$dev_info || $dev_info['ProjectType'] != PROJECT_TYPE_RESIDENCE)
    {
        return true;
    }

    //即插即用转流自适应方案, 即使设备开启转流开关,若设备在线也不走转流逻辑
    $comm_info = \resident\model\getCommunityInfo($dev_info['MngAccountID']);
    if ($comm_info["CommunityPlan"] == CommunityPlanCreatorTypeInstallerKit && $dev_info['Status'] == 1)
    {
        $is_dev_active_repost = 0;
    }

    return $is_dev_active_repost;
}