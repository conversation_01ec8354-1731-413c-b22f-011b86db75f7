<?php

namespace newoffice\control;

require_once __DIR__ . "/../../../notify/notify_newoffice.php";
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";
require_once __DIR__ . "/../model/officeAdmin.php";
require_once __DIR__ . "/../model/officePersonnel.php";

//setCallType 0=app 1=phone 2=app先 未接听后phone
function setOfficeCallType($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();
    $callType = $postDatas['calltype'];

    \newoffice\control\updateCallType($userConf, $callType);
    $jsondata = \util\common\createGeneralMessage(
        "account_modify",
        array(
            "account_uuid" => strval($userConf['UUID']),
            "office_uuid" => strval($userConf['MngUUID'])
        )
    );
    JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);

    $datas = [
        'calltype' => $callType
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function updateCallType($userData, $callType)
{
    if ($userData['Role'] == ROLE_TYPE_OFFICE_NEW_PER) {
        \newoffice\model\updatePersonnelCalltype($userData['UUID'], $callType);
    } else if ($userData['Role'] == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        \newoffice\model\updateAdminCalltype($userData['UUID'], $callType);
    } else {
        \util\log\akcsLog::debug("update calltype failed. not support user role:" . $userData['Role']);
    }
}

function getNewOfficeCalltype($userData)
{
    if ($userData['Role'] == ROLE_TYPE_OFFICE_NEW_PER) {
        return \newoffice\model\getPersonnelCalltypeByPersonalAccountUUID($userData['UUID'])['CallType'];
    } else if ($userData['Role'] == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        return \newoffice\model\getAdminCalltypeByPersonalAccountUUID($userData['UUID'])['CallType'];
    } else {
        return 0; //默认值
    }
}