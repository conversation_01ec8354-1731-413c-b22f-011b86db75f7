<?php

require_once __DIR__ . "/proxy.php";

function curl_file_get_contents($durl){  
    $ch = curl_init();  
    curl_setopt($ch, CURLOPT_URL, $durl);  
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true) ; // 获取数据返回    
    curl_setopt($ch, CURLOPT_BINARYTRANSFER, true) ; // 在启用 CURLOPT_RETURNTRANSFER 时候将获取数据返回    
    curl_setopt($ch, CURLOPT_TIMEOUT_MS, 10000); //默认超时时间10s
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 10000); 
    $r = curl_exec($ch);  
    curl_close($ch);  
    return $r;  
}

class UFileClient
{
    public function __construct($public_key, $private_key, $proxy_suffix) {
        global $UCLOUD_PUBLIC_KEY;
        global $UCLOUD_PRIVATE_KEY;
        global $UCLOUD_PROXY_SUFFIX;
        $UCLOUD_PUBLIC_KEY = $public_key;
        $UCLOUD_PRIVATE_KEY = $private_key;
        $UCLOUD_PROXY_SUFFIX = "." . $proxy_suffix; //域名前补"."
    }

    public function UploadFile($bucket, $key, $local_path) {
        //上传
        list($data, $err) = UCloud_PutFile($bucket, $key, $local_path);
        
        if ($err) {
            return $err; //上传失败，返回错误
        }
        
        return null;
    }

    public function DownloadFile($bucket, $key, $local_path) {
        //下载
        //获取下载链接
        $url = UCloud_MakePrivateUrl($bucket, $key);
        $content = curl_file_get_contents($url);
        file_put_contents($local_path, $content);
    }

    public function GetSignUrl($bucket, $key, $expire_time_sec) {
        if($expire_time_sec == 0)
        {
            $url = UCloud_MakePrivateUrl($bucket, $key);//内网url无过期时间限制
            $url = "http://" . $url;
        }
        else
        {
            $curtime = time();
            $curtime += $expire_time_sec;
            $url = UCloud_MakePrivateUrl($bucket, $key, $curtime);
            $url = "https://" . $url;
            $url = str_replace("internal-", "", $url);
        }
        return $url;
    }

}