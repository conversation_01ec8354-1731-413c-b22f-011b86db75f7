<?php

namespace newoffice\model;

use PDO;

require_once __DIR__ . "/../../../util/common.php";
require_once __DIR__ . "/../../common/model/version.php";
require_once __DIR__ . "/devicesDoorList.php";
require_once __DIR__ . "/doorReaderList.php";

const GROUP_TABLE_PERSONNEL = "OfficePersonnelGroup";
const GROUP_TABLE_ADMIN = "OfficeAdminGroup";
const DB_DEVICES_TABLE = "Devices";
const DB_DEVICES_FIELD = ["UUID","Status","MAC","Relay","SipAccount","Location","Firmware","Type","RtspPwd","SipPwd","DclientVer","ArmingFunction","Flags","DoorRelayStatus","DoorSeRelayStatus","AllowEndUserMonitor","Function"];




/**
 * brief  绑定数组参数，从数据库中查询数据
 * @param $db       数据库对象
 * @param $sql      sql语句
 * @param $bindArg  绑定的变量名
 * @param $arrData  参数数组
 */
function QueryFromDbByListParameter($db, $sql, $bindArg, $arrData)
{
    // 给每个字符串元素加上引号
    $quotedArray = array_map(
        function ($item) {
            if (is_string($item)) {
                return "'$item'";
            }
            return $item;
        },
        $arrData
    );

    // 组装 SQL 语句并绑定参数
    $sth = $db->prepare($sql);
    $sth->bindParam($bindArg, implode(', ', $quotedArray), PDO::PARAM_STR);

    $sth->execute();
    $result = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $result;
}

// 新Office 从权限组 获取设备列表
// maclist  设备列表
function getUserAGDeviceListForNewOffice($userData, &$maclist)
{
    $db = \util\container\getDb();

    $groupTableName = '';
    $psersonal = OFFICE_ACCESS_GROUP_BIND_PERSONNEL;

    if ($userData['Role'] == ROLE_TYPE_OFFICE_NEW_PER) {
        $groupTableName = GROUP_TABLE_PERSONNEL;
        $sth = $db->prepare("select V.MAC as mac, D.Relay as relay, D.SecurityRelay as SecurityRelay 
        from OfficeAccessGroupBindUserList A 
        left join OfficeAccessGroupDevice D on D.OfficeAccessGroupUUID=A.OfficeAccessGroupUUID 
        left join Devices V on V.UUID=D.DevicesUUID 
        where A.PersonalAccountUUID=:PersonalAccountUUID AND A.BindType =:BindType");
        $sth->bindParam(':PersonalAccountUUID', $userData['UUID'], PDO::PARAM_STR);
        $sth->bindParam(':BindType', $psersonal, PDO::PARAM_STR);
        $sth->execute();
        $result = $sth->fetchALL(PDO::FETCH_ASSOC);
        foreach ($result as $key => $value) {
            \util\common\getAgMacInfo($maclist, $value);
        }
    } else if ($userData['Role'] == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        $groupTableName = GROUP_TABLE_ADMIN;
    } else {
        \util\log\akcsLog::debug("user role not support. role = " . $userData['Role']);
        return;
    }

    $group = OFFICE_ACCESS_GROUP_BIND_GROUP;
    $sth = $db->prepare("select V.MAC as mac, L.DevicesUUID as devices_uuid, L.ControlledRelay as relay, L.RelayType as relay_type 
        from " . $groupTableName . " G 
        left join OfficeAccessGroupBindUserList A on G.OfficeGroupUUID=A.OfficeGroupUUID 
        left join OfficeAccessGroupDoor D on D.OfficeAccessGroupUUID=A.OfficeAccessGroupUUID 
        left join DevicesDoorList L on D.DevicesDoorListUUID=L.UUID left join Devices V on V.UUID=D.DevicesUUID 
        where G.PersonalAccountUUID=:PersonalAccountUUID AND A.BindType =:BindType");
    $sth->bindParam(':PersonalAccountUUID', $userData['UUID'], PDO::PARAM_STR);
    $sth->bindParam(':BindType', $group, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetchALL(PDO::FETCH_ASSOC);

    // 先聚合每个mac的relay/securityRelay，再用getAgMacInfo处理
    $macRelayInfoMap = [];
    foreach ($result as $row) {
        $mac = $row['mac'];
        $doorIndex = intval(DOOR_RELAY_INDEX_MAPPING[$row['relay']]);
        $relayType = intval($row['relay_type']);
        if (!isset($macRelayInfoMap[$mac])) {
            $macRelayInfoMap[$mac] = [
                'mac' => $mac,
                'relay' => 0,
                'SecurityRelay' => 0
            ];
        }
        if ($relayType === 0) {
            $macRelayInfoMap[$mac]['relay'] |= (1 << ($doorIndex));
        } else if ($relayType === 1) {
            $macRelayInfoMap[$mac]['SecurityRelay'] |= (1 << ($doorIndex));
        }
    }
    foreach ($macRelayInfoMap as $macRelayInfo) {
        \util\common\getAgMacInfo($maclist, $macRelayInfo);
    }
}

// 新Office 从关联表 获取设备列表
function getRelatedDeviceListForNewOffice($userConf, &$macList)
{
    if ($userConf['Role'] == ROLE_TYPE_OFFICE_NEW_PER)
    {
        getPersonnelAssignedIndoorDeviceListForNewOffice($userConf['UUID'], $macList);
    } 
    else if ($userConf['Role'] == ROLE_TYPE_OFFICE_NEW_ADMIN)
    {
        getAdminRelatedAssignDeviceListForNewOffice($userConf['OfficeCompanyUUID'], $macList);
    }

    getCompanyDeviceList($userConf['OfficeCompanyUUID'], $macList);
}

// 给personnel分配的室内机列表
function getPersonnelAssignedIndoorDeviceListForNewOffice($accountUUID, &$maclist)
{
    $db = \util\container\getDb();

    $sth = $db->prepare("select V.MAC as mac from OfficeDeviceAssign A left join Devices V on V.UUID=A.DevicesUUID where A.PersonalAccountUUID=:PersonalAccountUUID and A.Type=:AssignType and V.Type=:DeviceType");
    $sth->bindParam(':PersonalAccountUUID', $accountUUID, PDO::PARAM_STR);
    $sth->bindValue(':AssignType', OFFICE_DEVICE_ASSIGN_TYPE_PERSONNEL, PDO::PARAM_INT);
    $sth->bindValue(':DeviceType', DEVICE_TYPE_INDOOR, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($result as $row) {
        // 设置的比较大，防止后面relay个数大于4时候没有处理到
        $maclist[$row["mac"]] = 127;
    }
}

// 获取belongs to同一公司下人员的所有的室内机
function getAdminRelatedAssignDeviceListForNewOffice($officeCompanyUUID, &$macList)
{
    $db = \util\container\getDb();

    $sth = $db->prepare("select V.MAC as mac from OfficeDeviceAssign A left join Devices V on V.UUID=A.DevicesUUID where A.OfficeCompanyUUID=:officeCompanyUUID and A.Type=:AssignType and V.Type=:DeviceType");
    $sth->bindParam(':officeCompanyUUID', $officeCompanyUUID, PDO::PARAM_STR);
    $sth->bindValue(':AssignType', OFFICE_DEVICE_ASSIGN_TYPE_PERSONNEL, PDO::PARAM_INT);
    $sth->bindValue(':DeviceType', DEVICE_TYPE_INDOOR, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($result as $row)
    {
        // 设置的比较大，防止后面relay个数大于4时候没有处理到
        $macList[$row["mac"]] = 127;
    }
}

// 获取所属公司的管理机设备列表
function getCompanyDeviceList($officeCompanyUUID, &$macList)
{
    $db = \util\container\getDb();

    $sth = $db->prepare("select V.MAC as mac from OfficeDeviceAssign A left join Devices V on V.UUID=A.DevicesUUID where A.OfficeCompanyUUID=:officeCompanyUUID and A.Type=:AssignType and V.Type=:DeviceType");
    $sth->bindParam(':officeCompanyUUID', $officeCompanyUUID, PDO::PARAM_STR);
    $sth->bindValue(':AssignType', OFFICE_DEVICE_ASSIGN_TYPE_COMPANY, PDO::PARAM_INT);
    $sth->bindValue(':DeviceType', DEVICE_TYPE_MANAGEMENT, PDO::PARAM_INT);

    $sth->execute();
    $result = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($result as $row) {
        // 设置的比较大，防止后面relay个数大于4时候没有处理到
        $macList[$row["mac"]] = 127;
    }
}

function getNewOfficeDevicesListByProjectUUID($projectUUID)
{
    return \util\container\medooDb()->select(DB_DEVICES_TABLE, DB_DEVICES_FIELD, ["AccountUUID" => $projectUUID]);
}

// 新Office 获取设备列表
function getNewOfficeDevicesList($userConf, &$resultArray)
{
    // 获取用户办公项目下的所有设备列表
    $officeAllDevList = \newoffice\model\getNewOfficeDevicesListByProjectUUID($userConf['MngUUID']);

    // 获取设备对应的DoorList
    $devDoorListMap = \newoffice\model\getNewOfficeDevicesDoorListMap($officeAllDevList);

    //获取关联设备 mac 列表
    //1. personnel展示belongs to personnel的室内机，admin app要展示勾选该公司人员的所有的室内机
    //2. personnel和admin app要展示勾选了所属company的管理机
    $userAssignMacList = [];
    \newoffice\model\getRelatedDeviceListForNewOffice($userConf, $userAssignMacList);

    //获取权限组设备 mac 列表
    $userAccessGroupMacList = [];
    \newoffice\model\getUserAGDeviceListForNewOffice($userConf, $userAccessGroupMacList);

    // 获取没有摄像头的设备型号
    $noMonitorList = \common\model\getNoMonitorList();

    // 获取支持高分辨率监控的设备型号
    $supportHighResolutionList = \common\model\getSupportHighResolutionMonitoringList();
    $isAdmin = $userConf['Role'] == ROLE_TYPE_OFFICE_NEW_ADMIN;

    foreach ($officeAllDevList as $row => $dev) {
        $mac = $dev['MAC'];

        // 判断用户对mac是否有权限
        if (!\newoffice\model\haveDeviceAccess($mac, $userAssignMacList, $userAccessGroupMacList)) {
            \util\log\akcsLog::debug("Devices not in userAssignMacList and userAccessGroupMacList, Account = " . $userConf['UserAccount'] . ", mac = " . $mac);
            continue;
        }
        
        $devInfo = array();
        $devInfo['mac'] = $mac;
        $devInfo['dtmf'] = '#';
        $devInfo['is_public'] = '0';
        $devInfo['uuid'] = $dev['UUID'];
        $devInfo['flags'] = $dev['Flags'];
        $devInfo['dev_type'] = $dev['Type'];
        $devInfo['sip'] = $dev['SipAccount'];
        $devInfo['firmware'] = $dev['Firmware'];
        $devInfo['location'] = $dev['Location'];
        $devInfo['status'] = strval($dev['Status']);
        $devInfo['dclient_ver'] = $dev['DclientVer'];
        $devInfo['arming_function'] = getArmingFunction($dev);
        $devInfo['door_relay_status'] = $dev['DoorRelayStatus'];
        $devInfo['door_se_relay_status'] = $dev['DoorSeRelayStatus'];
        $devInfo['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $devInfo['rtsp_nonce'] = \util\redisManage\getNonce($devInfo['mac']);
        $devInfo['camera_num'] = \util\utility\switchHandle($dev['Function'], DevFunctionSupportMultiMonitor) ? 2 : 1;
        $devInfo['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);

        // 获取门口机relay信息
        if (\util\common\deviceIsAGDevType($dev["Type"])) {
            \newoffice\model\getDevicesDoorRelayInfo($devInfo, $devDoorListMap, $userAccessGroupMacList, $isAdmin);
        }

        // 获取室内机relay信息
        if ($dev["Type"] == DEVICE_TYPE_INDOOR) {
            $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
            $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
            $devInfo['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus, $doorRelayStatus, $dev['Type'], RELAY_FULL_ACCESS_VALUE, RELAY_TYPE_LOCAL);
        }
        
        // 设备是否下发给APP展示
        if (!\util\common\checkDevDisplay($devInfo)) {
            continue;
        }

        $devInfo['is_support_high_resolution'] = \util\common\isSupportHighResolutionMonitoring($supportHighResolutionList, $dev['Firmware']);

        array_push($resultArray, $devInfo);
    }
}

function getDevicesDoorRelayInfo(&$devInfo, $devDoorListMap, $userAccessGroupMacList, $isAdmin = false)
{
    // 获取权限组relay value
    $accessGroupRelayValue = $userAccessGroupMacList[$devInfo['mac']];
    $accessGroupSecurityRelayValue = $userAccessGroupMacList[\util\common\getUserAGSecurityKey($devInfo['mac'])];

    // 获取relay status
    $relayStatus = \util\common\getRelayStatusFromFlags($devInfo['flags']);
    $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($devInfo['flags']);

    // 获取door relay status
    $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($devInfo['door_relay_status']);
    $doorSeRelayStatus = \util\common\getDoorRelayStatusFromStatus($devInfo['door_se_relay_status']);

    // 初始化relay info list
    $relayInfoList = array();
    $securityRelayInfoList = array();

    // 获取door list
    $devDoorList = isset($devDoorListMap[$devInfo["uuid"]]) ? $devDoorListMap[$devInfo["uuid"]] : [];

    foreach ($devDoorList as $doorInfo) {
        $relayIndex = DOOR_RELAY_INDEX_MAPPING[$doorInfo["ControlledRelay"]];

        $relayInfo = array();
        $relayInfo['relay_id'] = strval($relayIndex);
        $relayInfo['dtmf'] = $doorInfo['Dtmf'];
        $relayInfo['door_name'] = $doorInfo['Name'];
        // admin不受开关限制，全部展示
        $relayInfo['show_home'] = $isAdmin ? "1" : strval($doorInfo['ShowHome']);
        $relayInfo['show_talking'] = $isAdmin ? "1" : strval($doorInfo['ShowTalking']);
        $relayInfo['enable'] = intval($doorInfo['Enable']);

        if ($doorInfo["RelayType"] == DOOR_RELAY_TYPE) {
            $relayInfo['type'] = RELAY_TYPE_LOCAL_STR;         // 新版本下发relay type
            if (array_key_exists($doorInfo["LockStatus"], DOOR_LOCK_STATUS_DOOR_RELAY_STATUS_MAPPING)) {
                $relayInfo['door_relay_status'] = DOOR_LOCK_STATUS_DOOR_RELAY_STATUS_MAPPING[$doorInfo["LockStatus"]];
            } else {
                $relayInfo['door_relay_status'] = 0;  // 默认值，代表未获取到
            }            
            $relayInfo['relay_status'] = $relayStatus[$relayIndex] ? $relayStatus[$relayIndex] : 0;
            $relayInfo['enable'] = $relayInfo['enable'] << $relayIndex & $accessGroupRelayValue ? "1" : "0";
        } elseif ($doorInfo["RelayType"] == DOOR_SECURITY_RELAY_TYPE) {
            if (array_key_exists($doorInfo["LockStatus"], DOOR_LOCK_STATUS_DOOR_RELAY_STATUS_MAPPING)) {
                $relayInfo['door_relay_status'] = DOOR_LOCK_STATUS_DOOR_RELAY_STATUS_MAPPING[$doorInfo["LockStatus"]];
            } else {
                $relayInfo['door_relay_status'] = 0;  // 默认值，代表未获取到
            }     
            $relayInfo['relay_status'] = $securityRelayStatus[$relayIndex] ? $securityRelayStatus[$relayIndex] : 0;
            $relayInfo['enable'] = $relayInfo['enable'] << $relayIndex & $accessGroupSecurityRelayValue ? "1" : "0";
        }

        $relayInfo['subscription_status'] = strval(\newoffice\model\getDoorSubscriptionStatus($doorInfo)); 

        // 不存在Internal reader时，App的主界面和通话中不显示door
        if (!\newoffice\model\doorReaderExistInternal($doorInfo['UUID'])) {
            $relayInfo['show_home'] = strval(0);
            $relayInfo['show_talking'] = strval(0);
        }

        if ($doorInfo["RelayType"] == DOOR_RELAY_TYPE) {
            array_push($relayInfoList, $relayInfo);
        } elseif ($doorInfo["RelayType"] == DOOR_SECURITY_RELAY_TYPE) {
            array_push($securityRelayInfoList, $relayInfo);
        }
    }

    $devInfo['relay'] = $relayInfoList;
    $devInfo['security_relay'] = $securityRelayInfoList;
}

function getDoorSubscriptionStatus($doorInfo)
{
    $subscriptionStatus = DOOR_SUBSCRIPTION_STATUS_NORMAL;

    if (!$doorInfo["Active"]) {
        $subscriptionStatus = DOOR_SUBSCRIPTION_STATUS_NOT_ACTIVE;
    } elseif ($doorInfo["Active"] && $doorInfo["Expired"]) {
        $subscriptionStatus = DOOR_SUBSCRIPTION_STATUS_EXPIRE;
    }
    return $subscriptionStatus;
}

function getArmingFunction($dev)
{
    $armingFunction = 0;
    if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
        $armingFunction = 0;
    } elseif (array_key_exists('ArmingFunction', $dev)) {
        $armingFunction = intval($dev['ArmingFunction']);
    } else {
        $armingFunction = 1;
    }
    return $armingFunction;
}

function haveDeviceAccess($mac, $userAccessGroupMacList, $userAssignMacList)
{
   // 判断用户对mac是否有权限
   if (array_key_exists($mac, $userAssignMacList) == false && array_key_exists($mac, $userAccessGroupMacList) == false) {
        return false;
    }
    return true;
}

function checkHighEndDevOnline($userConf)
{
    $db = \util\container\getDb();
    $projectUUID = $userConf['MngUUID'];

    // 获取高端固件版本列表
    $highEndDevList = \common\model\getHighEndDevFirmwareList();
    
    if (empty($highEndDevList)) {
        return 0;
    }

    // 直接使用高端固件版本列表
    $sql = "SELECT UUID, Status, MAC, Location, Firmware, Type, Flags 
            FROM Devices 
            WHERE AccountUUID = :AccountUUID 
            AND SUBSTRING_INDEX(Firmware, '.', 1) IN ($highEndDevList)"; 
    $sth = $db->prepare($sql);
    $sth->bindParam(':AccountUUID', $projectUUID, PDO::PARAM_STR);

    $sth->execute();
    $devList = $sth->fetchAll(PDO::FETCH_ASSOC);
    return !empty($devList) ? 1 : 0;
}
