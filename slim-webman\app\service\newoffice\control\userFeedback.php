<?php

namespace  newoffice\control;

require_once __DIR__ . "/../../common/model/account.php";
require_once __DIR__ . "/../../common/model/customerService.php";
require_once __DIR__ . "/../../common/model/appFeedback.php";
require_once __DIR__ . "/../../newoffice/model/officeCompany.php";
require_once __DIR__ . "/../../common/control/userFeedback.php";
require_once __DIR__ . "/../../../util/s3Handle.php";
require_once __DIR__ . "/../../../util/common.php";
require_once __DIR__ . "/../../../notify/notify_newoffice.php";

function NewOfficeAppUserFeedback($request, $response)
{
    //发送方信息获取
    $userData = \util\container\getUserData();

    //endUser类型检查
    if(\util\common\isOfficeUser($userData['Role'])) {
        $emailInfo['user_type'] = USER_TYPE_OFFICE;
    }  else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"user role feedback function unsupported.");
    }

    $fileList = "";
    if (\common\control\UploadFeedbackFile($request, $fileList) != 0)
    {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"You did not upload a picture");
    }

    //更新数据库表
    $feedbackUUID = \util\common\getMysqlUUID();
    $parsedBody = $request->getParsedBody();
    \common\model\addAppFeedback($userData['UUID'],$parsedBody['content'],$parsedBody['email'],$fileList,$feedbackUUID);    
    $emailInfo = [];
    //根据MngUUID查ManageGroup
    $mngInfo = \common\model\getAccountByUUID($userData['MngUUID']);
    if(!$mngInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"ManageGroup not found");
    }
    //根据ManageGroup查ins
    $insAccountInfo = \common\model\getAccountByID($mngInfo['ManageGroup']);
    if(!$insAccountInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Installer not found");
    }
    //根据UUID查sub-dis
    $subDisAccountInfo = \common\model\getSubDisAccountByInsUUID($insAccountInfo['UUID']);
    $subDisFlag = 1;
    if(!$subDisAccountInfo) {
        \util\log\akcsLog::debug("sub dis not found");
        $subDisFlag = 0;
    }
    //根据ParentUUID查dis
    $disAccountInfo = \common\model\getAccountByUUID($insAccountInfo['ParentUUID']);
    if(!$disAccountInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Distributor not found.");
    }

    $oem_name = \common\model\getOemNameByDisUuid($disAccountInfo['UUID']);

    //通用字段
    $emailInfo['file_list'] = $fileList;
    $emailInfo['ins_name'] = $insAccountInfo['Account'];
    $emailInfo['sip_account'] = $userData['SipAccount'];
    $emailInfo['content'] = $parsedBody['content'];
    $emailInfo['contact_email'] = $parsedBody['email'];
    $emailInfo['email_type'] = "app_feedback"; 

    //办公用户字段 
    $emailInfo['project_name'] = $mngInfo['Location'];
    $companyName = $userData['OfficeCompanyName'];
    if(!$companyName) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"companyName not found.");
    }
    $emailInfo['company'] = $companyName;
    //是否发给内部邮箱标志
    $innerFlag = 1;
    //发送邮件:1.ins(sub-ins) 2.dis 3.sub-dis 4.内部邮箱
    $contactEmail = \common\model\checkFeedbackSwitchByAccount($insAccountInfo['Account']);
    if($contactEmail) {
        $innerFlag = 0;
        //ins有开启开关，则填入email
        $emailInfo['email'] = $contactEmail;
        $emailInfo['language'] = $insAccountInfo['Language'];
        $emailInfo['send_to_type'] = "ins";
        \common\model\addAppFeedbackReceiverList($insAccountInfo['Account'],$feedbackUUID);
        SendNewOfficeAppFeedbackEmail($emailInfo, $oem_name);
    }
    $contactEmail = \common\model\checkFeedbackSwitchByAccount($disAccountInfo['Account']);
    if($contactEmail) {
        $innerFlag = 0;
        //dis有开启开关，则填入email
        $emailInfo['email'] = $contactEmail;
        $emailInfo['language'] = $disAccountInfo['Language'];
        $emailInfo['send_to_type'] = "dis";
        $emailInfo['dis_name'] = $disAccountInfo['Account'];
        \common\model\addAppFeedbackReceiverList($disAccountInfo['Account'],$feedbackUUID);
        SendNewOfficeAppFeedbackEmail($emailInfo, $oem_name);
    }
    if($subDisFlag == 1) {
        //有sub-dis，检查是否开启开关
        $contactEmail = \common\model\checkFeedbackSwitchByAccount($subDisAccountInfo['Account']);
        if($contactEmail) {
            $innerFlag = 0;
            $emailInfo['email'] = $contactEmail;
            $emailInfo['language'] = $subDisAccountInfo['Language'];
            $emailInfo['send_to_type'] = "dis";
            $emailInfo['dis_name'] = $subDisAccountInfo['Account'];
            \common\model\addAppFeedbackReceiverList($subDisAccountInfo['Account'],$feedbackUUID);
            SendNewOfficeAppFeedbackEmail($emailInfo, $oem_name);            
        }
    }
    //内部邮箱,仅当上述角色都没开开关时才发
    if($innerFlag == 1) {
        $emailInfo['email'] = FEEDBACK_SEND_EMAIL_MAIN;
        $emailInfo['cc_list'] = implode(",", FEEDBACK_SNED_EMAIL_CC_LIST);
        $emailInfo['language'] = "en";
        $emailInfo['send_to_type'] = "company";
        SendNewOfficeAppFeedbackEmail($emailInfo, $oem_name);
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}