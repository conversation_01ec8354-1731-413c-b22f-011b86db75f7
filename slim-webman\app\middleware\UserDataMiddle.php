<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

require_once(__DIR__ . '/../service/common/model/logSlice.php');

class UserDataMiddle implements MiddlewareInterface
{
    public function process(Request $request, callable $next): Response
    {        
        if(\util\common\isSpecialReq($request))
        {
            return $next($request);
        }
        if(\app\middleware\AuthMiddle::isAuthWhiteList($request->getUri()->getPath()))
        {
            return $next($request);
        }

        $userData = \util\container\getUserData();
        $projectType = $userData['ProjectType'];
        $account = $userData['Account'];

        if($projectType == PROJECT_TYPE_OFFICE || $projectType == PROJECT_TYPE_NEW_OFFICE) {
            $data = \common\model\getOfficeCommInfo($account);
        } else {
            $data = \common\model\getCommInfo($account);
        }
        \util\container\setUserData($data);
        //设置log库分片uuid
        $deliveryUUID = \common\model\getLogProjectUUID($data);
        \util\container\setDeliveryUUID($deliveryUUID);

        \util\log\akcsLog::debug("userinfo : [account] = {account}, role = {role}, UserInfoUUID = {UserInfoUUID}", 
                                ['account' => $data['UserAccount'], 'role' => $data['Role'], 'UserInfoUUID' => $data['UserInfoUUID']]);

        $response = $next($request);
        
        return $response;
    }
}

