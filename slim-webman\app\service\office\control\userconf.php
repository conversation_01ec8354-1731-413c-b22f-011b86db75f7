<?php
namespace office\control;

require_once __DIR__ . "/../../common/model/personalAccount.php";
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";
require_once __DIR__ . "/../../common/model/appPushToken.php";
require_once __DIR__ . "/../../common/model/token.php";
require_once __DIR__ . "/../model/officeInfo.php";
require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/../model/personalAccountOfficeInfo.php";

function getOfficeCommConf($request, $response)
{
    $userConf = \util\container\getUserData();

    $commCode = \common\model\getCommCodeConf($userConf['UserAccount']);

    $bleCode = $commCode['BLECode'];
    $nfcCode = $commCode['NFCCode'];
    $enableNFC = strlen($commCode['NFCCode']) > 0 ? "1" : "0";
    $enableBLE = strlen($commCode['BLECode']) > 0 ? "1" : "0";

    $callTypeInfo = \common\model\getCalltypeByAccount($userConf['Account']);
        
    $appInfo = \common\model\getAppPushCnf($userConf['AppMainUserAccount']);

    $tokenInfo = \common\model\getTokenInfoByAppMainAccount($userConf['AppMainUserAccount']);

    //IOS V4.3版本在新的版本设置为不存在的calltype出现崩溃
    if ($appInfo['AppType'] == 0 && $appInfo['Version'] < 4400 && $callTypeInfo['CallType'] > 2) {
        $callTypeInfo['CallType'] = 0;
    }

    $accountInfo = \common\model\getAccountByParentId($userConf['MngID']);

    if ($accountInfo) {
        $commInfo = \office\model\getOfficeInfo($accountInfo['UUID']);
        if ($commInfo) {
            $commConf['calltype_show_phone'] = strval(\util\utility\switchHandle($commInfo['Switch'], DevSwitchEnableLandline));
        } else {
            $commConf['calltype_show_phone'] = "0";
        }
    } else {
        $commConf['calltype_show_phone'] = "0";
    }

    $datas = [
        'ble' => [
            'enable' => $enableBLE,
            'code' => $bleCode,
            'opendoor_type' => $commCode['BLEOpenDoorType']
        ],
        'nfc' => [
            'enable' => $enableNFC,
            'code' => $nfcCode
        ],
        'calltype' => strval($callTypeInfo['CallType']),
        'calltype_personal' => strval($callTypeInfo['EnableRobinCall']),
        'calltype_show_phone' => strval($commConf['calltype_show_phone']),
        'enable_callkit' => strval($tokenInfo['EnableCallkit'])
    ];
    
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function getOfficeUserConfV70($request, $response)
{
    $resultToken = \util\container\getUserData();

    $transType = $resultToken['SipType'];
    $mngSipType = \office\model\getOfficeUserMngSipType($resultToken['ParentID']);
    if ($mngSipType["SipType"] != 3) {//0udp 1tcp 2tls 3none 管理员不配置
        $transType = $mngSipType["SipType"];
    }
    //6.7修改，按25%的比例转换udp为tcp
    $transType = \util\common\transSipType($resultToken['UUID'], $transType);
    $appSipPwd = $resultToken['SipPwd'];
    $codec = $resultToken['Codec'];
    $isShowTmpkey = intval($resultToken['TempKeyPermission']);
    $switch = intval($resultToken['UserSwitch']);//旧版本兼容的标识
    $enableConfirmFlag = \util\utility\switchHandle($switch, PersonlAccountSwitchConfirm);
    $isShowLandLine = 1;
    $enablePinConfig = 1;

    $officeInfo = null;
    $accountInfo = \common\model\getAccountByParentId($resultToken['MngID']);
    if ($accountInfo) {
        $isShowTmpkey = intval(\office\model\getIsShowTempKey($resultToken, $accountInfo));

        $officeInfo = \office\model\getOfficeInfo($accountInfo['UUID']);
        if (!\office\model\officeFeaturePlanIsExpire($accountInfo['UUID']) && \common\model\checkCommunityFeaturePlan($resultToken['MngID'], FeatureItemPin)) {
            $enablePinConfig = \util\utility\switchHandle($officeInfo['Switch'], DevSwitchEnablePinConfig);
        } else {
            $enablePinConfig = 1;
        }
        $isShowLandLine = \util\utility\switchHandle($officeInfo['Switch'], DevSwitchEnableLandline);
    }

    $macs = array();
    $resultArray = array();
    $appConf = array();

    \office\model\getOfficeDevicesList($resultToken, $macs, $resultArray);

    $callEnable = 0;
    $accountOfficeInfo = \office\model\getOfficeInfoByUUID($resultToken['UUID']);
    if ($accountOfficeInfo) {
        $callEnable = \util\utility\switchHandle($accountOfficeInfo['Flags'], CALL_ENABLE_FLAG);
    }
    
    $pinInit = \util\utility\switchHandle($switch, PersonlAccountSwitchPinInit);
    $pinInit = $enablePinConfig ? $pinInit : 1; // 判断社区创建pin开关
    if (\util\version\isNeedReturnPinInit()) {
        $appConf['pin_init'] = intval($pinInit); //pin初始化标记
    }
    
    $appConf['show_payment'] = 0;
    $appConf['show_subscription'] = 0;
    $appConf['sip'] = $resultToken['SipAccount'];
    $appConf['display_name'] = $resultToken['Name'];
    $appConf['sip_passwd'] = \util\utility\passwdDecode($appSipPwd);
    $appConf['motion_alert'] = \util\redisManage\getAppMotionStatus($resultToken['UserAccount']); //app-uid的motion接收开发
    $appConf['video_res'] = "2";
    $appConf['video_bitrate'] = "512";
    $appConf['video_storage_time'] = strval(\common\model\getVideoStorageTime($resultToken['UserAccount']));
    //是否有公共设备
    $appConf['have_public_dev'] = "1";
    $appConf['show_landline'] = intval($isShowLandLine);
    $appConf['enable_pin_config'] = intval($enablePinConfig);
    $appConf['uid'] = $resultToken['UserAccount'];
    $appConf['node'] = $resultToken['Account'];
    $appConf['trans_type'] = strval($transType); //0-udp, 1-tcp, 2-tls
    $appConf['codec'] = strval($codec); //0=PCMU, 8 =PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义
    $appConf['data_collection'] = 0; //app数据收集到网管系统 后续如果用户较多可采样收集
    $appConf['landline'] = \util\common\getPbxLandlineNumber($resultToken['PhoneCode'], $resultToken['Phone'], $resultToken['Phone2'], $resultToken['Phone3']);
    $appConf['rtp_confuse'] = intval($mngSipType["RtpConFuse"]);
    $appConf['show_tempkey'] = intval($isShowTmpkey);
    $appConf['role'] = intval($resultToken['Role']);
    $appConf['check_dev'] = 1; //检查室内机的收费方案flag
    $appConf['check_slave'] = 1;
    $appConf['call_enable'] = intval($callEnable);
    $appConf['enable_confirm_flag'] = intval($enableConfirmFlag);  //高级设置开关
    $appConf['show_id_access'] = 0;

    // 获取主站点的sip信息
    $userInfo = \common\model\getUserInfoByUUID($resultToken['UserInfoUUID']);
    $mainSipInfo = \common\model\getUserSipInfo($userInfo['AppMainUserAccount']);
    $appConf['main_sip'] = $userInfo['AppMainUserAccount'];
        
    $appSipPwd = $resultToken['SipPwd'];
    $appMainSipPwd = $mainSipInfo['SipPwd'];
    \common\model\checkAccountExpire($appSipPwd, $appMainSipPwd, $appConf, $resultToken);
    $appConf['sip_passwd'] = \util\utility\passwdDecode($appSipPwd);
    $appConf['main_sip_passwd'] = \util\utility\passwdDecode($appMainSipPwd);

    // 是否为多套房用户
    $appConf['is_site_link'] = \common\model\isMultiSiteUser($resultToken['UserInfoUUID']);
    if ($appConf['is_site_link']) {
        $allLandlines = array();
        $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($resultToken['UserInfoUUID']);
        foreach ($personalAccountList as $siteInfo) {
            $siteLandline = \util\common\getPbxLandlineNumber($siteInfo['PhoneCode'], $siteInfo['Phone'], $siteInfo['Phone2'], $siteInfo['Phone3']);
            $allLandlines = array_merge($allLandlines, $siteLandline);
        }
        //app根据all_sites_landline添加app联系人，根据show_landline和landline字段判断是否弹窗，如果all_sites_landline有变化也需要弹窗
        $appConf['all_sites_landline'] = array_values(array_unique($allLandlines));
    } else {
        $appConf['all_sites_landline'] = $appConf['landline'];
    }

    $siteinfo = array();
    \common\control\getOfficeSiteInfo($resultToken, $siteinfo);
    $appConf['room_name'] = $siteinfo['room_name'];
    $appConf['project_name'] = $siteinfo['project_name'];
    $appConf['room_title'] = $siteinfo['room_title'];
    $appConf['enable_smarthome'] = 0; // 是否启用智能家居
    $appConf['is_old_community'] = 0;

    $unreadArr['activities_num'] = intval(\common\model\getActivitiesNum($resultToken));
   //要新增msgnum
    $unreadArr['messages_num'] = intval(\common\model\getMessagesNumV65());

    $datas = [
        "app_conf" => $appConf,
        "dev_list" => $resultArray,
        "unread_msg" => $unreadArr
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function getLoginConfV64($request, $response)
{
    $userData = \util\container\getUserData();
    
    $accountInfo = \common\model\getAccountByID($userData['MngID']);
    if ($accountInfo) {
        $officeInfo = \office\model\getOfficeInfo($accountInfo['UUID']);
        $enablePin = \util\utility\switchHandle($officeInfo['Switch'], DevSwitchEnablePinConfig);
    }
    $isInit = $enablePin ? $userData['Initialization'] : 1;
    $role = $userData['Role'];
    
    $code = ERR_CODE_SUCCESS;
    $wordKey = '';
    \common\control\getLoginInterceptCode($code, $wordKey);
    $old_intercept_mode = \common\control\GetInterceptModeByCode($code);

    $checkDev = 1;
    $checkSlave = 1;

    // 是否为多套房用户
    $isSiteLink = \common\control\checkIsMultiSiteUser();

    // 一人多套房:app忽略login的errCode,移到login_conf判断,用于App弹窗提示
    \common\control\getAppStatusCode($code);
    $old_app_status = \common\control\GetAppStatusByCode($code);

    $datas = [
            "intercept_mode" => intval($old_intercept_mode),
            "word_key" => strval($wordKey),
            "is_init" => strval($isInit),
            "role" => strval($role),
            "check_dev" => intval($checkDev),
            "check_slave" => intval($checkSlave),
            "is_site_link" => intval($isSiteLink),
            "app_status" => intval($old_app_status),
            "show_tempkey" => intval($userData['TempKeyPermission']),
    ];
    return \util\response\setResponseMessage($response, $code, $datas);
}