<?php

namespace common\model;
use PDO;

const DIS_INFO_OEM_DEFAULT      = '0';    // 默认
const DIS_INFO_OEM_HAGE         = '1';    // hager
const DIS_INFO_OEM_PALWINTERCS  = '2';    // PalwintecS
const DIS_INFO_OEM_FASTTEL      = '3';    // Fasttel

function getDistributorInfoByAccount($dis_account)
{
    $disInfo = \util\container\medooDb()->get(
        "DistributorInfo",
        [
            "ID",
            "IsEncryptPin",
            'IsVillaMonitor',
            'IsYaleLock',
            'IsEnableOfflineSolution',
            'IsCreateSubDis',
            'IsProjectLandLine',
            'IsEnableRfCardControl',
            'OemType',
            'IDAccess',
            'Incentives',
            'IsCustomizeModel',
            'IsEnableAptChargePlan'
        ],
        ["Account" => $dis_account]
    );

    return $disInfo;
}

function checkUserHaveIDAccess($userData)
{
    //获取社区相关信息
    $mngInfo = \common\model\getAccountByUUID($userData['MngUUID']);
    if (!$mngInfo) {
        return -1;
    }
    //根据ParentUUID查dis
    $disAccountInfo = \common\model\getAccountByUUID($mngInfo['ParentUUID']);
    if (!$disAccountInfo) {
        return -1;
    }

    return checkDisAllowIDAccess($disAccountInfo['Account']);
}

function checkDisAllowIDAccess($dis_account)
{
    $disInfo = getDistributorInfoByAccount($dis_account);
    if ($disInfo) {
        return $disInfo["IDAccess"];
    }

    return 0;
}

function getDistributorInfoByMngUUID($mng_uuid)
{
    //根据项目UUID查项目信息
    $mngInfo = \common\model\getAccountByUUID($mng_uuid);
    if (empty($mngInfo)) {
        return array();
    }

    //根据DIS_UUID(ParentUUID)查dis信息
    $disAccountInfo = \common\model\getAccountByUUID($mngInfo['ParentUUID']);
    if (empty($disAccountInfo)) {
        return array();
    }

    // 根据dis account 查 dis info
    $disInfo = \common\model\getDistributorInfoByAccount($disAccountInfo['Account']);
    $disInfo['DisUUID'] = $disAccountInfo['UUID'];
    return $disInfo;
}

// 根据项目UUID获取DisUUID  
function getDisUUIDByProjectUUID($project_uuid)
{
    //根据项目UUID查项目信息
    $mngInfo = \common\model\getAccountByUUID($project_uuid);
    if (empty($mngInfo) || !isset($mngInfo['ParentUUID']) || empty($mngInfo['ParentUUID'])) {
        return "";
    }

    return $mngInfo['ParentUUID'];
}

