<?php

namespace newoffice\model;

use PDO;

require_once __DIR__ . "/../../../util/common.php";
require_once __DIR__ . "/../../common/model/version.php";

const DB_DOORREADERLIST_TABLE = "DoorReaderList";

function getDoorReaderListByDevicesDoorListUUIDD($devicesDoorListUUID)
{
    return \util\container\medooDb()->select(DB_DOORREADERLIST_TABLE,  
        ["Mode", "Type", "TypeValue", "RS485Address"],
        ["DevicesDoorListUUID" => $devicesDoorListUUID]
    );
}

function doorReaderExistInternal($devicesDoorListUUID)
{   
    $internalDoorReader = \util\container\medooDb()->select(DB_DOORREADERLIST_TABLE,  
        ["Mode", "Type", "TypeValue", "RS485Address"],
        ["DevicesDoorListUUID" => $devicesDoorListUUID, "Type" => "0"]
    );
    return count($internalDoorReader) > 0;
}