<?php

namespace resident\model;

use PDO;


function getPerDevicesList(&$aptMacs)
{
    $devList = [];
    $userData = \util\container\getUserData(); 
    $db =  \util\container\getDb();
    $redis = \util\container\getRedis();
    $redis->select(4);

    //获取房间下的设备
    $sth = $db->prepare("select Status,MAC,SipAccount,Location,Firmware,Type,RtspPwd,SipPwd,Relay,SecurityRelay,DclientVer,ArmingFunction,Flags,Brand,IsRepost,DoorRelayStatus,DoorSeRelayStatus,UUID,AllowEndUserMonitor,Function,AllowUserCallDevices from PersonalDevices where Node = :account");
    $sth->bindParam(':account', $userData['Account'], PDO::PARAM_STR);
    $sth->execute();
    $aptDevList = $sth->fetchALL(PDO::FETCH_ASSOC);
    $noMonitorList = \common\model\getNoMonitorList();
    $supportHighResolutionList = \common\model\getSupportHighResolutionMonitoringList();
    foreach ($aptDevList as $row => $dev) {
        $curNode = array();
        $curNode['uuid'] = $dev['UUID'];
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce ? $nonce : "";
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = strval($dev['Type']);
        $curNode['is_public'] = '0';
        $curNode['dclient_ver'] = strval($dev['DclientVer']);
        $curNode['dtmf'] = '#';
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['repost'] = intval($dev['IsRepost']);
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        $curNode['allow_hold_door'] = 0;
        $curNode['is_need_call'] = \util\common\isEndUserNeedCall($dev['Type'], $dev['AllowUserCallDevices']);

        //relay相关
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], ALL_RELAY_ON, RELAY_TYPE_LOCAL);       
        if (\util\version\isSupportExRelay() && $dev['Type'] == DEVICE_TYPE_INDOOR)
        {
                    if(\resident\model\checkExRelaySwitch($dev['UUID']))
        {
            $exRelayInfoList = \resident\model\getExRelayInfoList($dev['UUID']);
            \util\common\addExRelayInfoList($curNode['relay'], $exRelayInfoList);
        }
        }
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], ALL_RELAY_ON, RELAY_TYPE_SE_LOCAL);
        }

        //arming_function
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } elseif ($dev['Brand'] != 0) {//第三方中控
            $curNode['arming_function'] = 0;
        } else {
            $curNode['arming_function'] = 1;
        }

        //转流的门口机状态置为在线
        if ($curNode['repost'] && \util\common\isDoorType($dev['Type']))
        {
            $curNode['status'] = "1";
        }

        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }

        $curNode['camera_num'] = \util\utility\switchHandle($dev['Function'], DevFunctionSupportMultiMonitor) ? 2 : 1;
        $curNode['is_support_high_resolution'] = \util\common\isSupportHighResolutionMonitoring($supportHighResolutionList, $dev['Firmware']);

        array_push($devList, $curNode);
        array_push($aptMacs, $dev['MAC']);
    }

    //处理公共设备
    //根据Account查找主账号下面的设备列表
    $sth = $db->prepare("select D.Status,D.MAC,D.SipAccount,D.Location,D.Type, D.RtspPwd,D.Relay,D.SecurityRelay,D.DclientVer,D.Firmware,D.Flags,D.ID,D.IsRepost,D.ArmingFunction,D.DoorRelayStatus,D.DoorSeRelayStatus,D.UUID,D.AllowEndUserMonitor,D.Function,D.AllowUserCallDevices from PerNodeDevices left join PersonalDevices D on D.id=PerDevID   where NodeID=:NodeID");
    $sth->bindParam(':NodeID', $userData['AccountID'], PDO::PARAM_STR);
    $sth->execute();
    $pubDevList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($pubDevList as $row => $dev) {
        $curNode = array();
        $curNode['uuid'] = $dev['UUID'];
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce ? $nonce : "";
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = strval($dev['Type']);
        $curNode['is_public'] = '1';
        $curNode['dclient_ver'] = strval($dev['DclientVer']);
        $curNode['dtmf'] = '#';
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['repost'] = intval($dev['IsRepost']);
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        $curNode['is_need_call'] = \util\common\isEndUserNeedCall($dev['Type'], $dev['AllowUserCallDevices']);

        //relay相关
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], ALL_RELAY_ON, RELAY_TYPE_LOCAL);
        if (\util\version\isSupportExRelay() && $dev['Type'] == DEVICE_TYPE_INDOOR)
        {
            //检查设备是否开启外接relay总开关
            if( \resident\model\checkExRelaySwitch($dev['UUID']))
            {
                $exRelayInfoList = \resident\model\getExRelayInfoList($dev['UUID']);
                \util\common\addExRelayInfoList($curNode['relay'], $exRelayInfoList);
            }
        }
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], ALL_RELAY_ON, RELAY_TYPE_SE_LOCAL);
        }

        //arming function
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } else {
            $curNode['arming_function'] = 1;
        }

        //转流的门口机状态置为在线
        if ($curNode['repost'] && \util\common\isDoorType($dev['Type']))
        {
            $curNode['status'] = "1";
        }

        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }

        $curNode['camera_num'] = \util\utility\switchHandle($dev['Function'], DevFunctionSupportMultiMonitor) ? 2 : 1;
        $curNode['is_support_high_resolution'] = \util\common\isSupportHighResolutionMonitoring($supportHighResolutionList, $dev['Firmware']);

        array_push($devList, $curNode);
    }
  
    return $devList;
}

function getOldCommDevicesList(&$aptMacs, &$accessDevs)
{
    $devList = [];
    $db =  \util\container\getDb();
    $userData = \util\container\getUserData();
    $redis = \util\container\getRedis();
    $redis->select(4);

    //获取房间下的设备
    $sth = $db->prepare("select Status,MAC,SipAccount,Location,Firmware,Type, RtspPwd,SipPwd, Relay,SecurityRelay, DclientVer,ArmingFunction,Flags,Brand,IsRepost,DoorRelayStatus,DoorSeRelayStatus,UUID,AllowEndUserMonitor,AllowUserCallDevices from Devices  where Node = :Node");
    $sth->bindParam(':Node', $userData['Account'], PDO::PARAM_STR);
    $sth->execute();
    $aptDevList = $sth->fetchALL(PDO::FETCH_ASSOC);
    $noMonitorList = \common\model\getNoMonitorList();
    foreach ($aptDevList as $row => $dev) {
        $curNode = array();
        $curNode['uuid'] = $dev['UUID'];
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce ? $nonce : "";
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = strval($dev['Type']);
        $curNode['is_public'] = '0';
        $curNode['dclient_ver'] = strval($dev['DclientVer']);
        $curNode['dtmf'] = '#';
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        $curNode['is_need_call'] = \util\common\isEndUserNeedCall($dev['Type'], $dev['AllowUserCallDevices']);

        //relay相关
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], ALL_RELAY_ON, RELAY_TYPE_LOCAL);
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], ALL_RELAY_ON, RELAY_TYPE_SE_LOCAL);
        }

        //arming相关
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } elseif ($dev['Brand'] != 0) {//第三方中控
            $curNode['arming_function'] = 0;
        } else {
            $curNode['arming_function'] = 1;
        }
        $curNode['repost'] = intval($dev['IsRepost']);

        //转流的门口机状态置为在线
        if ($curNode['repost'] && \util\common\isDoorType($dev['Type']))
        {
            $curNode['status'] = "1";
        }

        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }

        array_push($devList, $curNode);
        array_push($aptMacs, $dev['MAC']);
        $accessDevs[$dev['MAC']]['Relay'] = ALL_RELAY_ON;
        $accessDevs[$dev['MAC']]['SecurityRelay'] = ALL_RELAY_ON;
        $accessDevs[$dev['MAC']]['UUID'] = $dev['UUID'];
    }

    //社区公共设备
    $sth = $db->prepare("select D.Status,D.MAC,D.SipAccount,D.Location,D.Type, D.RtspPwd,D.Relay,D.SecurityRelay,D.DclientVer,D.Firmware,D.Flags,D.ID,D.Grade,D.ArmingFunction,D.IsRepost,D.DoorRelayStatus,D.DoorSeRelayStatus,D.UUID,D.AllowEndUserMonitor,D.UnitID,D.AllowUserCallDevices from Devices D where MngAccountID=:MngAccountID and Grade in (1,2);");
    $sth->bindParam(':MngAccountID', $userData['MngID'], PDO::PARAM_STR);
    $sth->execute();
    $pubDevList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($pubDevList as $row => $dev) {
        //楼栋管理
        if (\util\common\deviceIsManageBuilding($dev['Type'], $dev['Flags'], $dev['Grade'])) {
            if (!\common\model\isManageBuilding($dev['ID'], $userData['UnitID'])) {
                \util\log\akcsLog::debug("public devices not manage building, Account:" . $userData['UserAccount']. " dev:".$dev['MAC'] . "  unit id:" . $userData['UnitID']);
                continue;
            }
        }
        if(\util\common\unitCheckError($dev, $userData['UnitID']))
        {
            \util\log\akcsLog::debug($dev['MAC'] . " unit devices not belong ". $userData['UserAccount']);
            continue;
        }

        $curNode = array();
        $curNode['uuid'] = $dev['UUID'];
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce ? $nonce : "";
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = strval($dev['Type']);
        $curNode['is_public'] = '1';
        $curNode['dclient_ver'] = strval($dev['DclientVer']);
        $curNode['dtmf'] = '#';
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['repost'] = intval($dev['IsRepost']);
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        $curNode['is_need_call'] = \util\common\isEndUserNeedCall($dev['Type'], $dev['AllowUserCallDevices']);

        //relay相关
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], ALL_RELAY_ON, RELAY_TYPE_LOCAL);
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], ALL_RELAY_ON, RELAY_TYPE_SE_LOCAL);
        }

        //arming function
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } else {
            $curNode['arming_function'] = 1;
        }

        //转流的门口机状态置为在线
        if ($curNode['repost'] && \util\common\isDoorType($dev['type']))
        {
            $curNode['status'] = "1";
        }

        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }

        array_push($devList, $curNode);
        $accessDevs[$dev['MAC']]['Relay'] = ALL_RELAY_ON;
        $accessDevs[$dev['MAC']]['SecurityRelay'] = ALL_RELAY_ON;
        $accessDevs[$dev['MAC']]['UUID'] = $dev['UUID'];
    }

    return $devList;
}

function getNewCommDevicesList(&$aptMacs, &$accessDevs)
{
    $devList = [];
    $userData = \util\container\getUserData();
    $db =  \util\container\getDb();
    $redis = \util\container\getRedis();
    $redis->select(4);

    //获取房间下的设备
    $sth = $db->prepare("select Status,MAC,SipAccount,Location,Firmware,Type, RtspPwd,SipPwd, Relay,SecurityRelay, DclientVer,ArmingFunction,Flags,Brand,IsRepost,DoorRelayStatus,DoorSeRelayStatus,UUID,AllowEndUserMonitor,AllowEndUserHoldDoor,Function,AllowUserCallDevices from Devices  where Node = :Node");
    $sth->bindParam(':Node', $userData['Account'], PDO::PARAM_STR);
    $sth->execute();
    $aptDevList = $sth->fetchALL(PDO::FETCH_ASSOC);

    //获取权限组设备列表
    $agMacList = [];//key is mac,value is relay
    $haveDefaultAg = 0;
    \common\model\getUserAGDeviceList($userData['UserAccount'], $agMacList, $haveDefaultAg);

    $noMonitorList = \common\model\getNoMonitorList();
    $supportHighResolutionList = \common\model\getSupportHighResolutionMonitoringList();
    foreach ($aptDevList as $row => $dev) {
        if (\util\common\deviceIsAGDevType($dev['Type']) && !array_key_exists($dev['MAC'], $agMacList)) {
            \util\log\akcsLog::debug("user devices not in account access. account:" . $userData['UserAccount']. " dev:".$dev['MAC']);
            continue;
        }

        $agRelay = ALL_RELAY_ON;//设置的比较大，防止后面relay个数大于4时候没有处理到
        $agSecurityRelay = ALL_RELAY_ON;
        if (\util\common\deviceIsAGDevType($dev['Type'])) {
            $agRelay = $agMacList[$dev['MAC']];
            $agSecurityRelay = $agMacList[\util\common\getUserAGSecurityKey($dev['MAC'])];
        }

        $curNode = [];
        $curNode['uuid'] = $dev['UUID'];
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce ? $nonce : "";
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = strval($dev['Type']);
        $curNode['is_public'] = '0';
        $curNode['dclient_ver'] = strval($dev['DclientVer']);
        $curNode['dtmf'] = '#';
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['repost'] = intval($dev['IsRepost']);
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        // 允许设备门常开开关,社区主账号才有这个配置
        $curNode['allow_hold_door'] = $userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER ? intval($dev['AllowEndUserHoldDoor']) : 0;
        $curNode['is_need_call'] = \util\common\isEndUserNeedCall($dev['Type'], $dev['AllowUserCallDevices']);

        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], $agRelay, RELAY_TYPE_LOCAL);       
        if (\util\version\isSupportExRelay() && $dev['Type'] == DEVICE_TYPE_INDOOR)
        {
            if( \resident\model\checkExRelaySwitch($dev['UUID']))
            {
                $exRelayInfoList = \resident\model\getExRelayInfoList($dev['UUID']);
                \util\common\addExRelayInfoList($curNode['relay'], $exRelayInfoList);
            }
        }
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], $agSecurityRelay, RELAY_TYPE_SE_LOCAL);
        }

        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } elseif ($dev['Brand'] != 0) {//第三方中控
            $curNode['arming_function'] = 0;
        } else {
            $curNode['arming_function'] = 1;
        }

        //转流的门口机状态置为在线
        if ($curNode['repost'] && \util\common\isDoorType($dev['type']))
        {
            $curNode['status'] = "1";
        }

        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }

        $curNode['camera_num'] = \util\utility\switchHandle($dev['Function'], DevFunctionSupportMultiMonitor) ? 2 : 1;
        $curNode['is_support_high_resolution'] = \util\common\isSupportHighResolutionMonitoring($supportHighResolutionList, $dev['Firmware']);

        array_push($devList, $curNode);
        array_push($aptMacs, $dev['MAC']);
        $accessDevs[$dev['MAC']]['Relay'] = $agRelay;
        $accessDevs[$dev['MAC']]['SecurityRelay'] = $agSecurityRelay;
        $accessDevs[$dev['MAC']]['UUID'] = $dev['UUID'];
    }

    //社区公共设备
    $sth = $db->prepare("select D.Status,D.MAC,D.SipAccount,D.Location,D.Type, D.RtspPwd,D.Relay,D.SecurityRelay,D.DclientVer,D.Firmware,D.Flags,D.ID,D.Grade,D.ArmingFunction,D.IsRepost,D.DoorRelayStatus,D.DoorSeRelayStatus,D.UUID,D.AllowEndUserMonitor,D.UnitID,D.Function,D.AllowUserCallDevices from Devices D where MngAccountID=:MngAccountID and Grade in (1,2);");
    $sth->bindParam(':MngAccountID', $userData['MngID'], PDO::PARAM_STR);
    $sth->execute();
    $pubDevList = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($pubDevList as $row => $dev) {
        //楼栋管理
        if (\util\common\deviceIsManageBuilding($dev['Type'], $dev['Flags'], $dev['Grade'])) {
            if (!\common\model\isManageBuilding($dev['ID'], $userData['UnitID'])) {
                \util\log\akcsLog::debug("public devices not manage building, Account:" . $userData['UserAccount']. " dev:".$dev['MAC'] . "  unit id:" . $userData['UnitID']);
                continue;
            }
        }
        //权限组管理
        $agRelay = ALL_RELAY_ON;
        $agSecurityRelay = ALL_RELAY_ON;
        if($haveDefaultAg) {
            $removeDefaultAgMacs = \resident\model\getRemoveDefaultAgMacs($userData['MngUUID']);
            $unitDefaultAg = \util\common\devHavaDefaultAccessGroup($haveDefaultAg, $dev, $agMacList, $removeDefaultAgMacs);
        }
        if(\util\common\deviceIsAGDevType($dev['Type']))
        { 
            if($unitDefaultAg)
            {
                $agRelay = ALL_RELAY_ON;
                $agSecurityRelay = ALL_RELAY_ON;
            }
            if (!$unitDefaultAg && !array_key_exists($dev['MAC'], $agMacList)) 
            {
                \util\log\akcsLog::debug("public devices not in account access. account:" . $userData['UserAccount']. " dev:".$dev['MAC']);
                continue;
            }
            //没在默认权限组，且有在自定义权限组中
            if (!$unitDefaultAg && array_key_exists($dev['MAC'], $agMacList))
            {
                $agRelay = $agMacList[$dev['MAC']];
                $agSecurityRelay = $agMacList[\util\common\getUserAGSecurityKey($dev['MAC'])];
            }
        }
        else
        {
            if (\util\common\unitCheckError($dev, $userData['UnitID']))
            {
                \util\log\akcsLog::debug($dev['MAC'] . " unit devices not belong ". $userData['UserAccount']);
                continue;
            }
        }

        $curNode = [];
        $curNode['uuid'] = $dev['UUID'];
        $curNode['mac'] = $dev['MAC'];
        $curNode['status'] = strval($dev['Status']);
        $nonce = $redis->get($curNode['mac']);
        $curNode['rtsp_nonce'] = $nonce ? $nonce : "";
        $curNode['sip'] = $dev['SipAccount'];
        $curNode['location'] = $dev['Location'];
        $curNode['rtsp_pwd'] = \util\utility\passwdDecode($dev['RtspPwd']);
        $curNode['dev_type'] = strval($dev['Type']);
        $curNode['is_public'] = '1';
        $curNode['dclient_ver'] = strval($dev['DclientVer']);
        $curNode['dtmf'] = '#';
        $curNode['repost'] = intval($dev['IsRepost']);
        $curNode['firmware'] = $dev['Firmware'];
        $curNode['is_need_monitor'] = \util\common\isEndUserNeedMonitor($noMonitorList, $dev['Type'], $dev['Firmware'], $dev['AllowEndUserMonitor']);
        $curNode['is_need_call'] = \util\common\isEndUserNeedCall($dev['Type'], $dev['AllowUserCallDevices']);

        //relay相关
        $relayStatus = \util\common\getRelayStatusFromFlags($dev['Flags']);
        $securityRelayStatus = \util\common\getSecurityRelayStatusFromFlags($dev['Flags']);
        $doorRelayStatus = \util\common\getDoorRelayStatusFromStatus($dev['DoorRelayStatus']);
        $curNode['relay'] = \util\common\relay2DataArray($dev['Relay'], $relayStatus,$doorRelayStatus, $dev['Type'], $agRelay, RELAY_TYPE_LOCAL);
        if (\util\version\isSupportExRelay() && $dev['Type'] == DEVICE_TYPE_INDOOR)
        {
            //检查设备是否开启外接relay总开关
            if( \resident\model\checkExRelaySwitch($dev['UUID']))
            {
                $exRelayInfoList = \resident\model\getExRelayInfoList($dev['UUID']);
                \util\common\addExRelayInfoList($curNode['relay'], $exRelayInfoList);
            }
        }
        if ($dev['SecurityRelay']) {
            $doorSeRelayStatus = \util\common\getDoorSeRelayStatusFromStatus($dev['DoorSeRelayStatus']);
            $curNode['security_relay'] = \util\common\relay2DataArray($dev['SecurityRelay'], $securityRelayStatus,$doorSeRelayStatus, $dev['Type'], $agSecurityRelay, RELAY_TYPE_SE_LOCAL);
        }

        //arming function
        if ($dev['Type'] != DEVICE_TYPE_INDOOR) {
            $curNode['arming_function'] = 0;
        } elseif (array_key_exists('ArmingFunction', $dev)) {
            $curNode['arming_function'] = intval($dev['ArmingFunction']);
        } else {
            $curNode['arming_function'] = 1;
        }

        //转流的门口机状态置为在线
        if ($curNode['repost'] && \util\common\isDoorType($dev['type']))
        {
            $curNode['status'] = "1";
        }

        //设备是否下发给APP展示
        if(!\util\common\checkDevDisplay($curNode)) {
            continue;
        }

        $curNode['camera_num'] = \util\utility\switchHandle($dev['Function'], DevFunctionSupportMultiMonitor) ? 2 : 1;
        $curNode['is_support_high_resolution'] = \util\common\isSupportHighResolutionMonitoring($supportHighResolutionList, $dev['Firmware']);

        array_push($devList, $curNode);
        $accessDevs[$dev['MAC']]['Relay'] = $agRelay;
        $accessDevs[$dev['MAC']]['SecurityRelay'] = $agSecurityRelay;
        $accessDevs[$dev['MAC']]['UUID'] = $dev['UUID'];
    }

    return $devList;
}




function updateDevicesRelayHoldDelayByUUID($deviceUUID, $autoCloseTime, $relayID) 
{
    $userData = \util\container\getUserData();
    $db = \util\container\getDB();
    $device = \common\model\getDeviceInfoByRoleAndUUID($userData['Role'], $deviceUUID);
    if (!$device) 
    {
        return -1;
    }
    
    $relayData = json_decode($device['Relay'], true);
    if ($relayData === null) 
    {
        $relayData = [];
    }
     
    $relayIndex = intval($relayID);
    
    // 确保数组有足够的元素
    while (count($relayData) <= $relayIndex) 
    {
        $relayData[] = [];
    }
    
    $relayData[$relayIndex]['hold_delay'] = $autoCloseTime;
    
    $relayJson = json_encode($relayData);
    
    $updateSql = "UPDATE Devices SET Relay = :relay WHERE UUID = :uuid";
    $updateStmt = $db->prepare($updateSql);
    $updateStmt->bindParam(':relay', $relayJson, \PDO::PARAM_STR);
    $updateStmt->bindParam(':uuid', $deviceUUID, \PDO::PARAM_STR);
    $result = $updateStmt->execute();
    
    if ($result) 
    {
        return 0;
    } 
    else 
    {
        return -1;
    }
}

function getPerIndoorDevicesList()
{
    $userData = \util\container\getUserData(); 
    $db = \util\container\getDb();
    
    // 个人用户：查询 PersonalDevices 表
    $sth = $db->prepare("SELECT UUID, MAC, Status, Relay, Flags, DoorRelayStatus, CreateTime 
                        FROM PersonalDevices 
                        WHERE Node = :account AND Type = :type");
    $sth->bindParam(':account', $userData['Account'], PDO::PARAM_STR);
    $sth->bindValue(':type', DEVICE_TYPE_INDOOR, PDO::PARAM_INT);
    $sth->execute();
    $indoorDevices = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $indoorDevices;
}

function getCommIndoorDevicesList()
{
    $userData = \util\container\getUserData();
    $db = \util\container\getDb();
    
    // 社区用户：查询 Devices 表
    $sth = $db->prepare("SELECT UUID, MAC, Status, Relay, Flags, DoorRelayStatus, CreateTime 
                        FROM Devices 
                        WHERE Node = :Node AND Type = :type");
    $sth->bindParam(':Node', $userData['Account'], PDO::PARAM_STR);
    $sth->bindValue(':type', DEVICE_TYPE_INDOOR, PDO::PARAM_INT);
    $sth->execute();
    $aptDevList = $sth->fetchAll(PDO::FETCH_ASSOC);
    
    // 查询社区公共设备
    $sth = $db->prepare("SELECT UUID, MAC, Status, Relay, Flags, DoorRelayStatus, CreateTime, UnitID 
    FROM Devices 
    WHERE MngAccountID = :MngAccountID AND Type = :type AND (Grade = " . COMMUNITY_DEVICE_TYPE_PUBLIC . " OR (Grade = " . COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT . " AND UnitID = :UnitID))");
    $sth->bindParam(':MngAccountID', $userData['MngID'], PDO::PARAM_STR);
    $sth->bindParam(':UnitID', $userData['UnitID'], PDO::PARAM_STR);
    $sth->bindValue(':type', DEVICE_TYPE_INDOOR, PDO::PARAM_INT);
    $sth->execute();
    $pubDevList = $sth->fetchAll(PDO::FETCH_ASSOC);
    
    $indoorDevices = array_merge($aptDevList, $pubDevList);
    
    return $indoorDevices;
}

/**
 * 获取室内机设备列表
 */
function getIndoorDevicesList()
{
    $userData = \util\container\getUserData();
    
    if(\util\common\isSingleHouseUser($userData['Role'])) 
    {
        return \resident\model\getPerIndoorDevicesList();
    } 
    else 
    {
        return \resident\model\getCommIndoorDevicesList();
    }
}
