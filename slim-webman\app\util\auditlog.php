<?php

namespace util\auditlog;

use PDO;
use PDOException;

require_once(dirname(__FILE__) . '/../config/define.php');

class AuditLog
{
    public function insertAuditLog($remoteIP, $operator, $type, $keyInfo, $role, $mngAccountId)
    {
        $db = \util\container\getDb();
        $operaType = $this->getOperaType($role);
        if ($role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_PERSONNAL_SLAVE) {
            $data = $this->getPersonalDistributor($mngAccountId);
        } else {
            $data = $this->getCommunityDistributor($mngAccountId);
        }

        $currentTime = date("Y-m-d H:i:s");
        $keyList = [];
        $keyList[] = $keyInfo;
        $keyInfo = json_encode($keyList);
        \util\log\akcsLog::debug("ip=" . $remoteIP . ";time=" . $currentTime . ";operator=" . $operator . ";type=" . $type . ";keyInfo=" . $keyInfo . ";operaType=" . $operaType . ";distributor=" . $data['Distributor'] . ";installer=" . $data['Installer'] . ";community=" . $data['Community']);
        $sql = "insert into AuditLog(IP, Operator, CreateTime, Type, KeyInfo, OperaType, Distributor, Installer, Community) values(:ip, :operator, :createTime, :type, :keyInfo, :operaType, :distributor, :installer, :community)";
        try {
            $sth = $db->prepare($sql);
            $sth->bindParam(':ip', $remoteIP, PDO::PARAM_STR);
            $sth->bindParam(':operator', $operator, PDO::PARAM_STR);
            $sth->bindParam(':createTime', $currentTime, PDO::PARAM_STR);
            $sth->bindParam(':type', $type, PDO::PARAM_INT);
            $sth->bindParam(':keyInfo', $keyInfo, PDO::PARAM_STR);
            $sth->bindParam(':operaType', $operaType, PDO::PARAM_STR);
            $sth->bindParam(':distributor', $data['Distributor'], PDO::PARAM_STR);
            $sth->bindParam(':installer', $data['Installer'], PDO::PARAM_STR);
            $sth->bindParam(':community', $data['Community'], PDO::PARAM_STR);
            $sth->execute();
        } catch (PDOException $e) {
            \util\log\akcsLog::debug("insertAuditLog db exception=" . $e->getMessage());
            return -1;
        }

        $auditId = $db->lastInsertId();
        return $auditId;
    }

    public function getOperaType($role)
    {
        if ($role == ROLE_TYPE_PERSONNAL_MASTER) {
            return "SingleMaster";
        } elseif ($role == ROLE_TYPE_PERSONNAL_SLAVE) {
            return "SingleMember";
        } elseif ($role == ROLE_TYPE_COMMUNITY_MASTER) {
            return "CommunityMaster";
        } elseif ($role == ROLE_TYPE_COMMUNITY_SLAVE) {
            return "CommunityMember";
        } elseif ($role == ROLE_TYPE_COMMUNITY_PM) {
            return "PM";
        } else {
            return "EndUser";
        }
    }

    public function getAuditType($callType)
    {
        $auditType = intval($callType) + AUDIT_TYPE_SET_CALL_TYPE_SMARTPLUS_INDOOR;
        return $auditType;
    }

    public function getCommunityName($mngId)
    {
        $db = \util\container\getDb();
        $sql = "select Location from Account where ID = :id";
        try {
            $sth = $db->prepare($sql);
            $sth->bindParam(':id', $mngId, PDO::PARAM_INT);
            $sth->execute();
            $data = $sth->fetch(PDO::FETCH_ASSOC);
            if ($data) {
                return $data['Location'];
            } else {
                return "";
            }
        } catch (PDOException $e) {
            \util\log\akcsLog::debug("getCommunityName db exception=" . $e->getMessage());
            return "";
        }
    }

    public function getCommunitUnitName($unitId)
    {
        $db = \util\container\getDb();
        $sql = "select UnitName from CommunityUnit where ID = :id";
        try {
            $sth = $db->prepare($sql);
            $sth->bindParam(':id', $unitId, PDO::PARAM_INT);
            $sth->execute();
            $data = $sth->fetch(PDO::FETCH_ASSOC);
            if ($data) {
                return $data['UnitName'];
            } else {
                return "";
            }
        } catch (PDOException $e) {
            \util\log\akcsLog::debug("getCommunitUnitName db exception=" . $e->getMessage());
            return "";
        }
    }

    public function getRoomName($roomId)
    {
        $db = \util\container\getDb();
        $sql = "select RoomName from CommunityRoom where ID = :id";
        try {
            $sth = $db->prepare($sql);
            $sth->bindParam(':id', $roomId, PDO::PARAM_INT);
            $sth->execute();
            $data = $sth->fetch(PDO::FETCH_ASSOC);
            if ($data) {
                return $data['RoomName'];
            } else {
                return "";
            }
        } catch (PDOException $e) {
            \util\log\akcsLog::debug("getRoomName db exception=" . $e->getMessage());
            return "";
        }
    }

    public function getCallTypeKeyInfo($mngId, $unitId, $roomId)
    {
        $communityName = $this->getCommunityName($mngId);
        $unitName = $this->getCommunitUnitName($unitId);
        $roomName = $this->getRoomName($roomId);

        return $communityName . ',' . $unitName . ',' . $roomName;
    }

    public function getPersonalDistributor($installerId)
    {
        $db = \util\container\getDb();
        $sql = "SELECT a.Account Installer, b.Account Distributor, '' Community  FROM Account a LEFT JOIN Account b ON a.ParentUUID = b.UUID WHERE a.id = :id LIMIT 1";
        try {
            $sth = $db->prepare($sql);
            $sth->bindParam(':id', $installerId, PDO::PARAM_INT);
            $sth->execute();
            $data = $sth->fetch(PDO::FETCH_ASSOC);
            if ($data) {
                return $data;
            } else {
                return [];
            }
        } catch (PDOException $e) {
            \util\log\akcsLog::debug("getPersonalDistributor db exception=" . $e->getMessage());
            return [];
        }
    }

    public function getCommunityDistributor($communityId)
    {
        $db = \util\container\getDb();
        $sql = "SELECT A.Account Community, B.Account Installer  FROM Account A LEFT JOIN Account B ON A.ManageGroup = B.ManageGroup WHERE A.ID = :id and B.Grade = 22 LIMIT 1";
        try {
            $sth = $db->prepare($sql);
            $sth->bindParam(':id', $communityId, PDO::PARAM_INT);
            $sth->execute();
            $data = $sth->fetch(PDO::FETCH_ASSOC);
            if ($data) {
                $querySql = "SELECT  b.Account Distributor FROM Account a LEFT JOIN Account b ON a.ParentUUID = b.UUID WHERE a.account = :account LIMIT 1 ";
                $sth = $db->prepare($querySql);
                $sth->bindParam(':account', $data['Installer'], PDO::PARAM_STR);
                $sth->execute();
                $disData = $sth->fetch(PDO::FETCH_ASSOC);
                if ($disData) {
                    $data['Distributor'] = $disData['Distributor'];
                    return $data;
                } else {
                    $data['Distributor'] = "";
                    return $data;
                }
            } else {
                return [];
            }
        } catch (PDOException $e) {
            \util\log\akcsLog::debug("getCommunityDistributor db exception=" . $e->getMessage());
            return [];
        }
    }
}
