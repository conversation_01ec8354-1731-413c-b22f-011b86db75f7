<?php

/**
 * Redis连接池测试脚本
 * 用于验证Redis连接池的获取、归还和并发处理能力
 */

require_once __DIR__ . '/app/util/RedisConnectionPool.php';
require_once __DIR__ . '/define.php';

echo "=== Redis连接池测试 ===\n\n";

try {
    // 测试配置
    $config = [
        'min_connections' => 2,
        'max_connections' => 5,
        'connection_timeout' => 5,
        'idle_timeout' => 300,
        'health_check_interval' => 60,
        'retry_attempts' => 3,
        'debug' => true
    ];

    echo "1. 初始化Redis连接池\n";
    $pool = \util\pool\RedisConnectionPool::getInstance($config);
    echo "   ✓ Redis连接池初始化成功\n";

    // 显示初始统计
    $stats = $pool->getStats();
    echo "   初始统计: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "\n\n";

    echo "2. 测试连接获取和归还\n";
    $connections = [];
    
    // 获取多个连接
    for ($i = 1; $i <= 3; $i++) {
        try {
            $connection = $pool->getConnection();
            $connections[] = $connection;
            echo "   ✓ 获取连接 #{$i} ID: {$connection['id']}\n";
            
            // 测试Redis操作
            $redis = $connection['redis'];
            $redis->set("test_key_{$i}", "test_value_{$i}");
            $value = $redis->get("test_key_{$i}");
            echo "     Redis操作测试: {$value}\n";
            
        } catch (Exception $e) {
            echo "   ✗ 获取连接 #{$i} 失败: " . $e->getMessage() . "\n";
        }
    }

    // 显示当前统计
    $stats = $pool->getStats();
    echo "\n   当前统计: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "\n\n";

    echo "3. 归还连接\n";
    foreach ($connections as $i => $connection) {
        try {
            $result = $pool->releaseConnection($connection);
            if ($result) {
                echo "   ✓ 归还连接 ID: {$connection['id']}\n";
            } else {
                echo "   ✗ 归还连接 ID: {$connection['id']} 失败\n";
            }
        } catch (Exception $e) {
            echo "   ✗ 归还连接失败: " . $e->getMessage() . "\n";
        }
    }

    // 显示最终统计
    $stats = $pool->getStats();
    echo "\n   最终统计: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "\n\n";

    echo "4. 测试连接池满的情况\n";
    $maxConnections = [];
    
    // 尝试获取超过最大连接数的连接
    for ($i = 1; $i <= 6; $i++) {
        try {
            $connection = $pool->getConnection();
            $maxConnections[] = $connection;
            echo "   ✓ 获取连接 #{$i} ID: {$connection['id']}\n";
        } catch (Exception $e) {
            echo "   ✗ 获取连接 #{$i} 失败: " . $e->getMessage() . "\n";
            break;
        }
    }

    // 清理测试连接
    foreach ($maxConnections as $connection) {
        $pool->releaseConnection($connection);
    }

    echo "\n5. 测试连接健康检查\n";
    $connection = $pool->getConnection();
    echo "   ✓ 获取连接进行健康检查 ID: {$connection['id']}\n";
    
    // 模拟连接使用
    $redis = $connection['redis'];
    $redis->ping();
    echo "   ✓ 连接健康检查通过\n";
    
    $pool->releaseConnection($connection);
    echo "   ✓ 连接已归还\n\n";

    echo "6. 最终连接池状态\n";
    $finalStats = $pool->getStats();
    echo "   最终统计: " . json_encode($finalStats, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 计算一些有用的指标
    $totalRequests = $finalStats['total_created'];
    $connectionReuse = $totalRequests > 0 ? ($totalRequests - $finalStats['total_destroyed']) / $totalRequests : 0;
    echo "   连接复用率: " . number_format($connectionReuse * 100, 2) . "%\n";
    echo "   当前连接利用率: " . number_format(($finalStats['current_active'] / $config['max_connections']) * 100, 2) . "%\n";

    echo "\n=== 测试完成 ===\n";

} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
