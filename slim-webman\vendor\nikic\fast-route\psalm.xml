<?xml version="1.0"?>
<psalm
    name="Example Psalm config with recommended defaults"
    stopOnFirstError="false"
    useDocblockTypes="true"
    totallyTyped="false"
    requireVoidReturnType="false"
>
    <projectFiles>
        <directory name="src" />
    </projectFiles>

    <issueHandlers>
        <LessSpecificReturnType errorLevel="info" />

        <!-- level 3 issues - slightly lazy code writing, but provably low false-negatives -->
        <DeprecatedMethod errorLevel="info" />

        <MissingClosureReturnType errorLevel="info" />
        <MissingReturnType errorLevel="info" />
        <MissingPropertyType errorLevel="info" />
        <InvalidDocblock errorLevel="info" />
        <MisplacedRequiredParam errorLevel="info" />

        <PropertyNotSetInConstructor errorLevel="info" />
        <MissingConstructor errorLevel="info" />
    </issueHandlers>
</psalm>
