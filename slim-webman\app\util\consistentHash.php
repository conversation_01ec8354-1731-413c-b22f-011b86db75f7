<?php
/**
 * 一致性哈希队列分配（PHP实现，等价于C++ QueueConsistentHash.cpp/.h）
 * 适用于分布式队列负载均衡
 * 兼容 PHP 5.6/7.x/8.x
 */

namespace util\consistentHash;

class VNode
{
    public $db_index;
    public $vnode_id;

    public function __construct($db_index, $vnode_id)
    {
        $this->db_index = $db_index;
        $this->vnode_id = $vnode_id;
    }

    public function toStr()
    {
        return "{$this->db_index}-{$this->vnode_id}";
    }
}

class ConsistentHashRing
{
    // 哈希环，key为虚拟节点哈希值，value为VNode对象
    private $ring = array();

    /**
     * 插入虚拟节点
     */
    public function insert($node)
    {
        $hash = self::crc32_hash($node->toStr());
        $this->ring[$hash] = $node;
        // 不在这里排序，提升性能
    }

    // 排序方法
    public function sortRing()
    {
        ksort($this->ring, SORT_NUMERIC);
    }

    /**
     * 查找key对应的VNode
     * @param int $hash
     * @return VNode|null
     */
    public function find($hash)
    {
        if (empty($this->ring)) {
            return null;
        }
        // 二分查找第一个大于等于hash的节点
        $keys = array_keys($this->ring);
        $count = count($keys);
        $l = 0;
        $r = $count - 1;
        $res = null;
        while ($l <= $r) {
            $m = intval(($l + $r) / 2);
            if ($keys[$m] >= $hash) {
                $res = $m;
                $r = $m - 1;
            } else {
                $l = $m + 1;
            }
        }
        if ($res !== null) {
            return $this->ring[$keys[$res]];
        }
        // 没有找到，返回第一个节点（环形结构）
        return $this->ring[$keys[0]];
    }

    /**
     * 计算字符串的crc32哈希
     */
    public static function crc32_hash($str)
    {
        // 保证无符号32位
        return sprintf('%u', crc32($str));
    }
}

class DbConsistentHash
{
    const KVNODE_NUM = 5000;
    private $ring;

    public function __construct()
    {
        $this->ring = new ConsistentHashRing();
    }

    /**
     * 初始化虚拟节点
     * @param int $dbNum 队列总数
     */
    public function initDbNumList($dbNum)
    {
        for ($i = 0; $i < $dbNum; $i++) {
            for ($j = 0; $j < self::KVNODE_NUM; $j++) {
                $vnode = new VNode($i, $j);
                $this->ring->insert($vnode);
            }
        }
        // 所有虚拟节点插入后再排序一次
        $this->ring->sortRing();
    }

    /**
     * 对key做crc32哈希
     */
    public function crc32_hash($key)
    {
        return ConsistentHashRing::crc32_hash($key);
    }

    /**
     * 根据key获取队列编号
     * @param string $key
     * @return int|null
     */
    public function getDbNumByKey($key)
    {
        $key_hash = $this->crc32_hash($key);
        $vnode = $this->ring->find($key_hash);
        if ($vnode === null) {
            // 没有初始化
            return null;
        }
        return $vnode->db_index;
    }
}

// ================== 示例用法 ==================
//$qch = new DbConsistentHash();
//$qch->initDbNumList(4); // 假设有4个db
//$dbId = $qch->getDbNumByKey('test-key');
//echo "test-key 分配到队列: $dbId\n";
