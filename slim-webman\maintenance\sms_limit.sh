#!/bin/bash

HOST_IP=/etc/ip
SLIM_SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
SLIM_PORT=9404
BASE_URL="http://${SLIM_SERVER_INNER_IP}:${SLIM_PORT}"
# 打印菜单
function show_menu() {
    echo "========================="
    echo "短信限流管理脚本"
    echo "========================="
    echo "1. 查询所有短信限流的 Key"
    echo "2. 清理特定的短信限流 Key"
    echo "3. 清理所有短信限流 Key"
    echo "4. 退出"
    echo "========================="
    echo -n "请输入选项: "
}

# 查询所有限流 Key 
function get_all_sms_limit_key() {
    echo "正在查询所有短信限流的 Key..."

    # 调用接口并获取返回的 JSON 数据
    response=$(curl -s "$BASE_URL/maintenance/get_all_sms_limit_key")

    # 提取并解析 JSON 中的 `datas` 部分
    echo "$response" | grep -o '"datas":\[[^]]*]' | sed 's/"datas":\[//' | sed 's/]$//' | tr '}' '\n' | while read -r line; do
        # 提取 key, count, ttl 的值
        key=$(echo "$line" | grep -o '"key":"[^"]*"' | sed 's/"key":"//;s/"$//')
        count=$(echo "$line" | grep -o '"count":[0-9]*' | sed 's/"count"://')
        ttl=$(echo "$line" | grep -o '"ttl":[0-9]*' | sed 's/"ttl"://')
        formatted_ttl=$(printf "%02d:%02d:%02d" $((ttl/3600)) $(((ttl%3600)/60)) $((ttl%60)))

        # 如果 key 不为空，输出结果
        if [ -n "$key" ]; then
            echo "Key: $key, Count: $count, TTL:  $formatted_ttl"
        fi
    done
}

# 清理特定的短信限流 Key
function clear_sms_limit_key() {
    echo -n "请输入需要清理的 Key(手机号): "
    read key
    if [ -z "$key" ]; then
        echo "Key 不能为空！"
        return
    fi

    echo -n "请输入用户类型 (0表示终端用户,1表示新办公admin): "
    read user_type
    if [[ "$user_type" != "0" && "$user_type" != "1" ]]; then
        echo "用户类型无效！必须是0或1"
        return
    fi

    echo "正在清理 Key: $key..."
    curl -s "$BASE_URL/maintenance/clear_sms_limit_key?key=$key&user_type=$user_type" 
}

# 清理所有短信限流 Key
function clear_all_sms_limit_key() {
    echo "确认清理所有短信限流的 Key (yes/no)?"
    read confirm
    if [ "$confirm" == "yes" ]; then
        echo "正在清理所有短信限流的 Key..."
        curl -s "$BASE_URL/maintenance/clear_all_sms_limit_key" 
    else
        echo "操作已取消"
    fi
}

# 主逻辑
while true; do
    show_menu
    read choice
    case $choice in
        1)
            get_all_sms_limit_key
            ;;
        2)
            clear_sms_limit_key
            ;;
        3)
            clear_all_sms_limit_key
            ;;
        4)
            echo "退出脚本"
            exit 0
            ;;
        *)
            echo "无效选项，请重新输入"
            ;;
    esac
    echo ""
done
