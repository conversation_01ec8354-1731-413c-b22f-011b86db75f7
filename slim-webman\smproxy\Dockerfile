FROM php:8.1-cli

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    unzip \
    libzip-dev \
    && docker-php-ext-install zip

# 安装Swoole扩展
RUN pecl install swoole \
    && docker-php-ext-enable swoole

# 安装Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 设置工作目录
WORKDIR /app

# 克隆SMProxy项目
RUN git clone https://github.com/louislivi/SMProxy.git . \
    && composer install --no-dev --optimize-autoloader

# 创建必要的目录
RUN mkdir -p logs/pid

# 复制配置文件
COPY conf/database.json conf/database.json
COPY conf/server.json conf/server.json

# 设置权限
RUN chmod +x bin/SMProxy \
    && chmod -R 777 logs

# 暴露端口
EXPOSE 3366

# 启动命令
CMD ["php", "bin/SMProxy", "start"]
