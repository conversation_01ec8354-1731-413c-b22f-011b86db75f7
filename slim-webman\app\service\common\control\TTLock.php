<?php

namespace common\control\TTLock;

require_once __DIR__ . '/../model/devices.php';
require_once __DIR__ . '/../model/personalDevices.php';
require_once __DIR__ . '/../model/TTLock.php';
require_once __DIR__ . '/../model/thirdLock.php';



function getEnduserLockList()
{
    $userConf = \util\container\getUserData();
    $aptLockList = \common\model\TTLock\getAptLockList($userConf['NodeUUID']);
    if (empty($aptLockList)) {
        return [];
    }

    getLinkDeviceInfo($aptLockList);
    $adaptApiLockList = adaptApiLockList($aptLockList);

    return $adaptApiLockList;
}

// 转换为下发给app的数据格式
function adaptApiLockList($lockList)
{
    $adaptApiLockList = array();

    foreach ($lockList as $eachLock) {
        $adaptedLock = array(
            'brand' => 'TT',
            'type' => TT_LOCK_TYPE,
            'uuid' => strval($eachLock['UUID']),
            'location' => strval($eachLock['Name']),
        );

        // 构建绑定设备信息
        $bondedDevices = array();
        $bondedDevices['mac'] = $eachLock['mac'] ? $eachLock['mac'] : '';
        // log运算将1248转换为0123,接口请求时开0123relay,每把锁只能绑定一个relay
        $bondedDevices['relay_id'] = $eachLock['Relay'] ? log($eachLock['Relay'], 2) : -1;

        // 将绑定设备信息添加到适配后的锁数据中
        $adaptedLock['bonded_devices'] = $bondedDevices;

        // 将适配后的锁数据添加到结果数组的末尾
        $adaptApiLockList[] = $adaptedLock;
    }

    return $adaptApiLockList;
}

function getLinkDeviceInfo(&$allLockList)
{
    $userConf = \util\container\getUserData();
    foreach ($allLockList as &$lock) {
        if (!$lock['DeviceUUID']) {
            continue;
        }

        $deviceInfo = \common\model\getDeviceInfoByRoleAndUUID($userConf['Role'], $lock['DeviceUUID']);
        if($deviceInfo)
        {
            $lock['mac'] = $deviceInfo['MAC'];
        }
    }
}

function openDoor($userConf, $uuid)
{
    $linkMAC = "";
    $isJson = true;
    $heaader = array();
    $lockInfo = \common\model\TTLock\getLockInfo($uuid);
    if (isset($lockInfo['DeviceUUID'])) 
    {
        $deviceInfo = \common\model\getDeviceInfoByRoleAndUUID($userConf['Role'], $lockInfo['DeviceUUID']);
        if($deviceInfo) {
            $linkMAC = $deviceInfo['MAC'];
        }
    }

    $data = array();
    $data['link_mac'] = $linkMAC;
    $data['lock_id'] = $lockInfo['LockId'];
    $data['initiator'] = $userConf['Name'];
    $data['lock_name'] = $lockInfo["Name"];
    $data['lock_type'] = TT_LOCK_TYPE;
    $data['personal_account_uuid'] = $userConf['NodeUUID'];
    $data['personal_account_uuid_for_operator'] = $userConf['UUID']; // 添加操作者UUID
    $data['message_type'] = LINKER_MSG_TYPE_TT_OPEN_DOOR;
    $data['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR;

    // 请求到cslinke进行开锁
    $response = \util\common\httpRequest(
        "post",
        "http://" . CSLINKER_HTTP_SERVER . '/app_open_tt_lock',
        $heaader,
        $data,
        $isJson,
        20
    );

    $result = ERR_CODE_OPEN_DOOR_FAILED;
    $json_data = json_decode($response, true);

    if ($json_data['code'] == 0 && $json_data['data']['err_code'] == \common\model\FAILED_TYPE_SUCCESS) {
        $result = ERR_CODE_SUCCESS;
    } 
    else if($json_data['data']['err_code'] == \common\model\THIRD_LOCK_FAILED_TYPE_TTLOCK_NOT_EXIST) {
        $result = ERR_CODE_TT_LOCK_NOT_EXIST;
    }  
    else {
        $result = ERR_CODE_OPEN_DOOR_FAILED;
    }

    \util\log\akcsLog::debug('tt open door response=' . $response);
    return $result;
}


