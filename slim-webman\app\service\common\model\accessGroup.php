<?php

namespace common\model;
require_once __DIR__ . "/account.php";
use PDO;

function checkAccessGroup($account, $mac, $relay, $type)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select Relay,SecurityRelay,MngAccountID,UnitID,Grade from Devices where Mac=:Mac;");
    $sth->bindParam(':Mac', $mac, PDO::PARAM_STR);
    $ret = $sth->execute();
    $macInfo = $sth->fetch(PDO::FETCH_ASSOC);

    $removeDefaultAgMacs = \resident\model\getRemoveDefaultAgMacs($macInfo['MngAccountID']);

    $agList = array();
    if ($macInfo['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC || $macInfo['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT) {
        $sth = $db->prepare("select G.<PERSON>,G.<PERSON>,G.<PERSON>,G.<PERSON>,G.Date<PERSON>,G.Be<PERSON>ime,G.EndTime,G.StartTime,G.StopTime From AccessGroup G left join AccountAccess A on A.AccessGroupID=G.ID where A.Account=:Account;");
        $sth->bindParam(':Account', $account, PDO::PARAM_STR);
        $ret = $sth->execute();
        $list = $sth->fetchALL(PDO::FETCH_ASSOC);
        foreach ($list as $key => $ag) {
            $communityID = $ag['CommunityID'];
            $unitID = $ag['UnitID'];
            $agID = $ag['ID'];
            if ($unitID > 0 && !in_array($mac, $removeDefaultAgMacs)) {
                $ag['default'] = 1;
                if ($macInfo['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC && $macInfo['MngAccountID'] == $communityID) {
                    array_push($agList, $ag);
                } elseif ($macInfo['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && $macInfo['UnitID'] == $unitID) {
                    array_push($agList, $ag);
                }
            } else {
                $sth = $db->prepare("select MAC,Relay,SecurityRelay From AccessGroupDevice where AccessGroupID=:AGID and MAC=:MAC;");
                $sth->bindParam(':MAC', $mac, PDO::PARAM_STR);
                $sth->bindParam(':AGID', $agID, PDO::PARAM_INT);
                $ret = $sth->execute();
                $existMac = $sth->fetch(PDO::FETCH_ASSOC);
                if ($existMac) {
                    if ($type == ACCESS_GROUP_CHECK_RELAY) {
                        $ag['Relay'] = $existMac['Relay'];
                    } elseif ($type == ACCESS_GROUP_CHECK_SECURITY_RELAY) {
                        $ag['SecurityRelay'] = $existMac['SecurityRelay'];
                    }
                    array_push($agList, $ag);
                }
            }
        }
    } else {
        $sth = $db->prepare("select Relay,SecurityRelay,SchedulerType,DateFlag,BeginTime,EndTime,StartTime,StopTime From UserAccessGroupDevice D left join UserAccessGroup A on A.ID=D.UserAccessGroupID where A.Account=:Account and D.Mac=:MAC;");
        $sth->bindParam(':Account', $account, PDO::PARAM_STR);
        $sth->bindParam(':MAC', $mac, PDO::PARAM_STR);
        $ret = $sth->execute();
        $agList = $sth->fetchALL(PDO::FETCH_ASSOC);
    }
    $timezone = \common\model\getCommunityTime($db, $macInfo['MngAccountID']);
    return \common\model\checkAccessGroupTime($timezone, $agList, $relay, $type);
}

function checkAccessGroupTime($timezone, $agList, $relay, $type)
{
    $ret = 0;
    $nowTime =  \util\time\getNow();
    $nowTime = \util\time\setTimeZone($nowTime, $timezone, "3", "+");

    foreach ($agList as $key => $ag) {
        if ($ag['default']) {
            return 1;
        }
        if ($type == ACCESS_GROUP_CHECK_RELAY) {
            $agRelay = $ag['Relay'];
        } elseif ($type == ACCESS_GROUP_CHECK_SECURITY_RELAY) {
            $agRelay = $ag['SecurityRelay'];
        }
        
        if ($ag['SchedulerType'] == 0) {
            if (strtotime($ag['BeginTime']) < strtotime($nowTime) && strtotime($ag['EndTime']) > strtotime($nowTime) && \util\common\checkRelay($agRelay, $relay)) {
                return 1;
            }
        } elseif ($ag['SchedulerType'] == 1) {
            if (\util\time\checkTime($ag['StartTime'], $ag['StopTime'], $nowTime) && \util\common\checkRelay($agRelay, $relay)) {
                return 1;
            }
        } elseif ($ag['SchedulerType'] == 2) {
            $week = date('w', time());
            if ((\util\time\checkWeek($ag['DateFlag'], $week)) && \util\time\checkTime($ag['StartTime'], $ag['StopTime'], $nowTime) && \util\common\checkRelay($agRelay, $relay)) {
                return 1;
            }
        }
    }
    return $ret;
}