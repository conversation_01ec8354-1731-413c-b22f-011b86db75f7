<?php

namespace  common\control;
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";
require_once __DIR__ . "/../../common/model/communityInfo.php";

function setPackageDetection($request, $response)
{
    $userData = \util\container\getUserData();
    $postData = $request->getParsedBody();
    $enablePackageDetection = $postData['enable_package_detection'];
    $role = $userData['Role'];
    if ($role == ROLE_TYPE_COMMUNITY_PM) {
        \common\model\updateCommunityPackageDetectionStatus($enablePackageDetection, $userData['MngID']);
    } else {
        \common\model\updatePersonalPackageDetectionStatus($enablePackageDetection, $userData['Account']);
    }

    setDetectionNotify($userData);

    return \util\response\setResponseMessage($response);
}