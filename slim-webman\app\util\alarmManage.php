<?php

namespace util\alarmManage;

function addAkcsAlarm($key, $description)
{
    $serverinfo = parse_ini_file("/etc/ip");
    $nsqdAddr = $serverinfo["SERVER_INNER_IP"];

    $data = array();
    $data["node"] = "slim";
    $data["time"] = date('Y-m-d H:i:s');
    $data["description"] = $description;
    $data["key"] = $key;
    $data["ip"] = $serverinfo["SERVERIP"];
    $data["hostname"] = $serverinfo["AKCS_HOSTNAME"];
    $jsonData = json_encode($data);

    \util\log\akcsLog::debug("push alarm, key:$key ,description:$description");

    $cmd = "curl -s -d '$jsonData' " . 'http://' . $nsqdAddr . ':8513/pub?topic=akcs_alarm';
    \util\log\akcsLog::debug("cmd=" . $cmd);
    if (!shell_exec($cmd)) {
        \util\log\akcsLog::debug("push alarm error,description:" . $description);
    }
}