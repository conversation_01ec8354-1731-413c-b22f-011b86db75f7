<?php

namespace common\model;

use PDO;

function queryDND($personalAccount)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("/*master*/ select Status, StartTime, EndTime from AppCallDND where  Account = :account");
    $sth->bindParam(':account', $personalAccount, PDO::PARAM_STR);
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function insertDND($personalAccount, $status, $startMinutes, $endMinutes)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("insert into AppCallDND(Account, Status, StartTime, EndTime) values(:account, :status, :startTime, :endTime)");
    $sth->bindParam(':account', $personalAccount, PDO::PARAM_STR);
    $sth->bindParam(':status', $status, PDO::PARAM_INT);
    $sth->bindParam(':startTime', $startMinutes, PDO::PARAM_INT);
    $sth->bindParam(':endTime', $endMinutes, PDO::PARAM_INT);

    $sth->execute();
}

function updateDND($personalAccount, $status, $startMinutes, $endMinutes)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("update AppCallDND set Status = :status, StartTime = :startTime, EndTime = :endTime where Account = :account ");
    $sth->bindParam(':status', $status, PDO::PARAM_INT);
    $sth->bindParam(':startTime', $startMinutes, PDO::PARAM_INT);
    $sth->bindParam(':endTime', $endMinutes, PDO::PARAM_INT);
    $sth->bindParam(':account', $personalAccount, PDO::PARAM_STR);

    $sth->execute();
}


