<?php

namespace newoffice\control;

require_once __DIR__ . "/../../../notify/notify_newoffice.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";

function setOfficeBleConf($request, $response)
{
    $postDatas = $request->getParsedBody();
    $userConf = \util\container\getUserData();
    $type = @$postDatas['opendoor_type'];

    $randBLEcode = "";
    if ($postDatas['enable'] == '1') {
        \util\common\getBLECode($randBLEcode);
    }

    \common\model\UpdateBLECode($randBLEcode, $userConf['UserAccount'], $type);
    $jsondata = \util\common\createGeneralMessage(
        "account_modify",
        array(
            "account_uuid" => strval($userConf['UUID']),
            "office_uuid" => strval($userConf['MngUUID'])
        )
    );
    JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);

    $datas = [
        'blecode' => $randBLEcode,
        'opendoor_type' => intval($type)
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}
