<?php

namespace common\model;

function isSmsRateLimited($request, $mobileNumber, $userType)
{
    $ipAddress = $request->getRealIp();
    $redis = \util\container\getRedis();
    $redis->select(REDIS_DB_SMS_RATE_LIMIT);
    $rateLimitKey = GetRateLimitKeyPrefix($userType) . $mobileNumber . "_" . $ipAddress;
    $num = $redis->get($rateLimitKey);
    if (!$num) 
    {
        //新纪录
        $redis->setex($rateLimitKey, SMS_RATE_LIMIT_EXPIRE_TIME, 1);
        \util\log\akcsLog::debug("isSmsRateLimited: New record created for {$rateLimitKey}");
        return false;
    }
    else
    {
        if ($num < SMS_RATE_LIMIT_NUM) 
        {
            //次数没到限流次数，增加次数
            $num = $num + 1;
            $ttl = $redis->ttl($rateLimitKey);
            $redis->setex($rateLimitKey, $ttl, $num);
            \util\log\akcsLog::debug("isSmsRateLimited: Incremented count for {$rateLimitKey} to {$num}");
            return false;
        }
        else
        {
            \util\log\akcsLog::debug("isSmsRateLimited: Rate limit reached for {$rateLimitKey}");
            return true;
        }
    }
}

function GetRateLimitKeyPrefix($userType)
{
    if ($userType == SMS_CODE_USER_TYPE_NEW_OFFICE_ADMIN) {
        return "new_office_admin_sms_limit_";
    }
    return "sms_limit_";
}
