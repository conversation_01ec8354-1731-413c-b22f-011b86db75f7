<?php

namespace common\model;

use PDO;

//办公、社区用户根据UnitID获取UnitName
function getBuildingNameByID($id) {
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT UnitName FROM CommunityUnit WHERE ID = :unitID");
    $sth->bindParam(':unitID', $id, PDO::PARAM_INT);
    $sth->execute();
    $tmpResult = $sth->fetch(PDO::FETCH_ASSOC);
    return $tmpResult['UnitName'];
}

//社区用户获取房间号
function getRoomNumberByRoomID($id) {
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT RoomName FROM CommunityRoom WHERE ID = :RoomID");
    $sth->bindParam(':RoomID', $id, PDO::PARAM_INT);
    $sth->execute();
    $tmpResult = $sth->fetch(PDO::FETCH_ASSOC);
    return $tmpResult['RoomName'];
}