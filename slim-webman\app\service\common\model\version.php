<?php

namespace common\model;

use PDO;

function getNoMonitorList()
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select VersionNumber from VersionModel where IsNoMonitor = 1");
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_COLUMN);
    return $result;
}


function getHighEndDevFirmwareList()
{
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT VersionNumber FROM VersionModel WHERE IsHighEndDevice = 1");
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_COLUMN);

    if (!empty($result)) {
        return implode(', ', $result); // 直接返回用逗号分隔的字符串
    }

    return '';
}

function getSupportHighResolutionMonitoringList()
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select VersionNumber from VersionModel where IsHighResolutionMonitoring = 1");
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_COLUMN);
    return $result;
}

function getSupportPackageDetectionList()
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select VersionNumber,VersionName,ID from VersionModel where IsPackageDetection = 1");
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}

function getSupportSoundDetectionList()
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select VersionNumber,VersionName from VersionModel where IsSoundDetection = 1");
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}