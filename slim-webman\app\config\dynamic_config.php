<?php

#需要变动的配置存放在这个目录下
const SERVERURL = '**************:9999';
const SERVERNUMBER = '172';
const SERVER_NUMBER_EXPAND = [172, 173];
const IPV4IMG = 'https://*************:8091';
const IPV6IMG = 'https://[]:8091';
const IPV4VIDEO = 'http://*************:8188';
const IPV6VIDEO = 'http://[]:8188';
const SERVERHOST = 'https://*************';
const SIPSERVER = 'http://*************:118';
const G_PBX_IPV6 = "[*************]:5070";
const G_PBX_IPV4 = "*************:5070";
const CSFACECUT_ADDR = "*************:8798";


const WEB_IP = '**************';
const CSGAET_NET = '**************:9999';
const CSGAET_NET_IPV6 = '[]:19999';
const CSGAET_DOMAIN = '*************:9999';
const WEB_DOMAIN = '*************';
const REMOTE_CONFIG_API_URL = 'https://rexx.dev.com:1301';

#bm
const BMURL = 'https://*******/bmserver/';
const BMAPYURL = 'https://bmsys.akuvox.com1/dist/pay.html';

const PBX_LANDLINE_NUMBER =['+15036837202','+815031965407','+19027019367','+12563304579'];
const CANGETTOOLBOX = 1;
const SERVER_LOCATION = "na";

#db
const AKCS_DATABASEIP="12,3,3.4";
const AKCS_DATABASEPORT = 3308;
const LOG_DATABASEIP="12,3,3.4";
const LOG_DATABASEPORT = 3308;
const DATABASEPWD = "";

#redis
const REDISIP = "*************";
const ENABLE_REDIS_SENTINEL = "1";
const REDIS_SENTINEL_HOSTS = "92.168.14:8506, *************:8599";

#cslinker
const CSLINKER_HTTP_SERVER = "*************:8799";


//kafka集群服务器地址 多个以,分隔
const KAFKA_BROKER_LIST = '${KAFKA_INNER_IP}';

const MQTT_DOMAIN = '${mqtt_domain}';
const MQTT_TLS_PORT = '${mqtt_port}';
const WEB_ADAPT_ENTRY = '${WEB_ADAPT_ENTRY}';

const SMARTLOCK_HTTP_GATE_SERVER="https://xxxxx";
const FILE_SERVER = "https://xxxxx";
