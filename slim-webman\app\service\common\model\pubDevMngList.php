<?php

namespace common\model;

use PDO;

function isManageBuilding($devId, $unitId)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("SELECT ID FROM PubDevMngList WHERE DevicesID = :DevicesID AND UnitID = :UnitID");
    $sth->bindParam(':DevicesID', $devId, PDO::PARAM_INT);
    $sth->bindParam(':UnitID', $unitId, PDO::PARAM_INT);
    $sth->execute();
    if ($sth->fetch(PDO::FETCH_ASSOC)) {
        return true;
    }
    return false;
}