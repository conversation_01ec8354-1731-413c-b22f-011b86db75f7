<?php

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/define.php';
require_once __DIR__ . '/../app/config/dynamic_config.php';
require_once __DIR__ . '/../app/util/RedisConnectionPool.php';
require_once __DIR__ . '/../support/bootstrap/DatabasePool.php';

use util\pool\RedisConnectionPool;
use support\bootstrap\DatabasePool;

/**
 * Redis连接池测试类
 * 
 * 验证Redis连接池的各项功能是否正常工作
 */
class RedisPoolTest
{
    private $config;
    private $pool;
    
    public function __construct()
    {
        // 测试配置
        $this->config = [
            'min_connections' => 2,
            'max_connections' => 5,
            'connection_timeout' => 5,
            'idle_timeout' => 60,
            'health_check_interval' => 30,
            'debug' => true,
            'enable_stats' => true,
        ];
        
        echo "=== Redis连接池测试开始 ===\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        try {
            $this->testPoolInitialization();
            $this->testConnectionAcquisition();
            $this->testConnectionRelease();
            $this->testConcurrentAccess();
            $this->testPoolExhaustion();
            $this->testHealthCheck();
            $this->testRedisCommands();
            $this->testStats();
            
            echo "\n=== 所有Redis连接池测试通过 ===\n";
            
        } catch (Exception $e) {
            echo "\n=== Redis连接池测试失败: " . $e->getMessage() . " ===\n";
            throw $e;
        } finally {
            $this->cleanup();
        }
    }
    
    /**
     * 测试连接池初始化
     */
    public function testPoolInitialization()
    {
        echo "\n1. 测试Redis连接池初始化...\n";
        
        $this->pool = RedisConnectionPool::getInstance($this->config);
        $stats = $this->pool->getStats();
        
        $this->assert($stats['current_idle'] >= $this->config['min_connections'], 
            "初始空闲连接数应该 >= 最小连接数");
        
        echo "   ✓ Redis连接池初始化成功，空闲连接数: {$stats['current_idle']}\n";
    }
    
    /**
     * 测试连接获取
     */
    public function testConnectionAcquisition()
    {
        echo "\n2. 测试Redis连接获取...\n";
        
        $connection = $this->pool->getConnection();
        
        $this->assert(isset($connection['id']), "连接应该有ID");
        $this->assert(isset($connection['redis']), "连接应该包含Redis实例");
        $this->assert($connection['redis'] instanceof Redis, "Redis实例类型正确");
        
        // 测试连接是否可用
        $result = $connection['redis']->ping();
        $this->assert($result === true || $result === '+PONG', "连接应该可以执行PING命令");
        
        echo "   ✓ Redis连接获取成功，连接ID: {$connection['id']}\n";
        
        // 保存连接用于后续测试
        $this->testConnection = $connection;
    }
    
    /**
     * 测试连接释放
     */
    public function testConnectionRelease()
    {
        echo "\n3. 测试Redis连接释放...\n";
        
        $statsBefore = $this->pool->getStats();
        $result = $this->pool->releaseConnection($this->testConnection);
        $statsAfter = $this->pool->getStats();
        
        $this->assert($result === true, "连接释放应该成功");
        $this->assert($statsAfter['current_idle'] > $statsBefore['current_idle'], 
            "释放后空闲连接数应该增加");
        
        echo "   ✓ Redis连接释放成功\n";
    }
    
    /**
     * 测试并发访问
     */
    public function testConcurrentAccess()
    {
        echo "\n4. 测试Redis并发访问...\n";
        
        $connections = [];
        $maxConnections = min(3, $this->config['max_connections']);
        
        // 同时获取多个连接
        for ($i = 0; $i < $maxConnections; $i++) {
            $connection = $this->pool->getConnection();
            $connections[] = $connection;
            echo "   获取Redis连接 {$i+1}: ID {$connection['id']}\n";
        }
        
        $stats = $this->pool->getStats();
        $this->assert($stats['current_active'] == $maxConnections, 
            "活跃连接数应该等于获取的连接数");
        
        // 释放所有连接
        foreach ($connections as $connection) {
            $this->pool->releaseConnection($connection);
        }
        
        echo "   ✓ Redis并发访问测试通过\n";
    }
    
    /**
     * 测试连接池耗尽
     */
    public function testPoolExhaustion()
    {
        echo "\n5. 测试Redis连接池耗尽...\n";
        
        $connections = [];
        $maxConnections = $this->config['max_connections'];
        
        try {
            // 获取所有可用连接
            for ($i = 0; $i < $maxConnections; $i++) {
                $connections[] = $this->pool->getConnection();
            }
            
            // 尝试获取超出限制的连接
            try {
                $extraConnection = $this->pool->getConnection();
                $this->assert(false, "应该抛出Redis连接池耗尽异常");
            } catch (Exception $e) {
                $this->assert(strpos($e->getMessage(), '连接池已满') !== false, 
                    "应该抛出Redis连接池已满异常");
                echo "   ✓ 正确处理Redis连接池耗尽情况\n";
            }
            
        } finally {
            // 清理连接
            foreach ($connections as $connection) {
                $this->pool->releaseConnection($connection);
            }
        }
    }
    
    /**
     * 测试健康检查
     */
    public function testHealthCheck()
    {
        echo "\n6. 测试Redis健康检查...\n";
        
        $connection = $this->pool->getConnection();
        
        // 模拟连接断开（这里只是测试检查逻辑）
        $reflection = new ReflectionClass($this->pool);
        $method = $reflection->getMethod('isConnectionHealthy');
        $method->setAccessible(true);
        
        $isHealthy = $method->invoke($this->pool, $connection);
        $this->assert($isHealthy === true, "健康的Redis连接应该通过健康检查");
        
        $this->pool->releaseConnection($connection);
        
        echo "   ✓ Redis健康检查功能正常\n";
    }
    
    /**
     * 测试Redis命令
     */
    public function testRedisCommands()
    {
        echo "\n7. 测试Redis命令执行...\n";
        
        $connection = $this->pool->getConnection();
        $redis = $connection['redis'];
        
        // 测试基本命令
        $redis->set('test_key', 'test_value');
        $value = $redis->get('test_key');
        $this->assert($value === 'test_value', "Redis SET/GET命令应该正常工作");
        
        // 测试删除
        $redis->del('test_key');
        $value = $redis->get('test_key');
        $this->assert($value === false, "Redis DEL命令应该正常工作");
        
        // 测试数据库选择
        $redis->select(1);
        $redis->set('test_key_db1', 'value_in_db1');
        $redis->select(0);
        $value = $redis->get('test_key_db1');
        $this->assert($value === false, "数据库选择应该正常工作");
        
        // 清理测试数据
        $redis->select(1);
        $redis->del('test_key_db1');
        $redis->select(0);
        
        $this->pool->releaseConnection($connection);
        
        echo "   ✓ Redis命令执行测试通过\n";
    }
    
    /**
     * 测试统计信息
     */
    public function testStats()
    {
        echo "\n8. 测试Redis连接池统计信息...\n";
        
        $stats = $this->pool->getStats();
        
        $requiredFields = [
            'total_created', 'total_destroyed', 'current_active', 
            'current_idle', 'peak_active', 'connection_errors'
        ];
        
        foreach ($requiredFields as $field) {
            $this->assert(isset($stats[$field]), "统计信息应该包含字段: {$field}");
        }
        
        echo "   ✓ Redis连接池统计信息完整\n";
        echo "   当前统计: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    /**
     * 清理资源
     */
    public function cleanup()
    {
        echo "\n9. 清理Redis连接池资源...\n";
        
        if ($this->pool) {
            $this->pool->close();
        }
        
        echo "   ✓ Redis连接池资源清理完成\n";
    }
    
    /**
     * 断言方法
     */
    private function assert($condition, $message)
    {
        if (!$condition) {
            throw new Exception("断言失败: {$message}");
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    try {
        $test = new RedisPoolTest();
        $test->runAllTests();
    } catch (Exception $e) {
        echo "Redis连接池测试异常: " . $e->getMessage() . "\n";
        echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
        exit(1);
    }
}
