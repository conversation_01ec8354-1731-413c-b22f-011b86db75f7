<?php

namespace common\model;
use PDO;

function checkFeedbackSwitchByAccount($account)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("SELECT ReceiveFeedback,Email FROM CustomerService WHERE MngAccount = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $feedbackInfo = $sth->fetch(PDO::FETCH_ASSOC);
    if(!$feedbackInfo) {
        return false;
    }
    if($feedbackInfo['ReceiveFeedback']){
        return $feedbackInfo['Email'];
    }
    return false;
}