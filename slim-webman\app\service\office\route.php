<?php
require_once __DIR__ . "/../office/control/userconf.php";
require_once __DIR__ . "/../office/control/bleCode.php";
require_once __DIR__ . "/../office/control/dealAlarm.php";
require_once __DIR__ . "/../office/control/callType.php";
require_once __DIR__ . "/../office/control/faceHandle.php";
require_once __DIR__ . "/../office/control/nfcCode.php";
require_once __DIR__ . "/../common/control/dndHandle.php";
require_once __DIR__ . "/../common/control/userFeedback.php";
require_once __DIR__ . "/../common/control/msgHandle.php";
require_once __DIR__ . "/../office/control/officeOpenDoor.php";
$gApp->setProjectType(PROJECT_TYPE_OFFICE);

$gApp->post('/opendoor', function ($request, $response) {
    $response = \office\control\OpenSitedoor($request, $response);
    return $response;
});

$gApp->get('/login_conf', function ($request, $response) {
    $response = \office\control\getLoginConfV64($request, $response);
    return $response;
});


$gApp->get('/commconf', function ($request, $response) {
    $response = \office\control\getOfficeCommConf($request, $response);
    return $response;
});

$gApp->get('/userconf', function ($request, $response) {
    $response = \office\control\getOfficeUserConfV70($request, $response);
    return $response;
});

$gApp->post('/setbleconf', function ($request, $response) {
    $response = \office\control\setOfficeBleConf($request, $response);
    return $response;
});

$gApp->post('/setnfcconf', function ($request, $response) {
    $response = \office\control\setOfficeNfcCode($request, $response);
    return $response;
});

$gApp->post('/setcalltype', function ($request, $response) {
    $response = \office\control\setOfficeCallType($request, $response);
    return $response;
});

// 上传一张人脸用于注册人脸
$gApp->post('/upload_face', function ($request, $response) {
    $response = \office\control\uploadOfficeFaceV70($request, $response);
    return $response;
});

$gApp->get('/delete_face', function ($request, $response) {
    $response = \office\control\deleteFace($request, $response);
    return $response;
});

$gApp->post('/dealpersonalalarm', function ($request, $response) {
    $response = \office\control\dealOfficePersonalAlarm($request, $response);
    return $response;
});

// 设置免打扰配置
$gApp->post('/set_dnd', function ($request, $response) {
    $response = \common\control\setDnd($request, $response);
    return $response;
});

// 上传多张人脸，通过算法选择最优的人脸用于注册
$gApp->post('/upload_face_dynamic', function ($request, $response) {
    $response = \office\control\uploadFaceDynamic($request, $response);
    return $response;
});

$gApp->get('/get_face_status', function ($request, $response) {
    $response = \common\control\getFaceStatus($request, $response);
    return $response;
});

//6.7支持customer service相关feedback
$gApp->post('/feedback', function ($request, $response) {
    $response = \common\control\AppUserFeedback($request, $response);
    return $response;
});

$gApp->post('/set_msg_read', function ($request, $response) {
    $response = \common\control\setMsgRead($request, $response);
    return $response;
});