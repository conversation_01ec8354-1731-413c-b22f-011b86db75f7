version: '3.8'

services:
  smproxy:
    build: .
    container_name: smproxy
    ports:
      - "3366:3366"
    volumes:
      - ./conf:/app/conf
      - ./logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
    networks:
      - webman-network
    depends_on:
      - mysql
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "php", "-r", "echo (new PDO('mysql:host=127.0.0.1;port=3366', 'root', '123456'))->query('SELECT 1')->fetchColumn();"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  mysql:
    image: mysql:8.0
    container_name: mysql-main
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: akcs_db
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    networks:
      - webman-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  mysql-slave:
    image: mysql:8.0
    container_name: mysql-slave
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: akcs_db
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_slave_data:/var/lib/mysql
      - ./mysql/slave-conf.d:/etc/mysql/conf.d
    networks:
      - webman-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    depends_on:
      - mysql

volumes:
  mysql_data:
  mysql_slave_data:

networks:
  webman-network:
    driver: bridge
