<?php

namespace  common\control;
require_once __DIR__ . "/../../common/model/appPolicySign.php";
require_once __DIR__ . "/../../common/model/account.php";
require_once __DIR__ . "/../../common/model/providerInfo.php";
require_once __DIR__ . "/../../resident/model/indoorMonitorZigbeeDevice.php";

function getAppFlags($request, $response)
{
    $userData = \util\container\getUserData();
    $policySign = \common\model\getPolicySignByPersonalAccountUUID($userData['UUID']);
    if (!$policySign) {
        //获取用户Ins/DisUUID
        $comInfo = \common\model\getAccountByUUID($userData['MngUUID']);
        $insInfo = \common\model\getAccountByID($comInfo['ManageGroup']);
        $disInfo = \common\model\getAccountByUUID($insInfo['ParentUUID']);
        if(\common\model\canGetProviderInfo($insInfo, $disInfo))
        {     
            $isNeedResignPolicy = 1;
        }
        else
        {
            $isNeedResignPolicy = 0;
        }
    }
    else
    {
        $isNeedResignPolicy = (int)$policySign['IsNeedReSign'];
    }
    //阿塞拜疆默认不重新签署
    if (SERVERNUMBER == GATEWAY_NUM_ASBJ)
    {
        $isNeedResignPolicy = 0;
    }
    // 检查是否有Zigbee设备
    $hasZigbeeDevice = \resident\model\checkUserHasZigbeeDevice();
    
    $datas = [
        "is_need_sign_policy" => $isNeedResignPolicy,
        "has_zigbee_device" => $hasZigbeeDevice,
    ];
    \util\log\akcsLog::debug("getAppFlags [is_need_sign_policy] = $isNeedResignPolicy, [has_zigbee_device] = $hasZigbeeDevice");
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}