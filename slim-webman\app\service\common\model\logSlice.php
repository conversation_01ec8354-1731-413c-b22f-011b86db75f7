<?php

namespace common\model;

use PDO;

function getLogProjectUUID($resultToken)
{
    if ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE){
        return $resultToken['NodeUUID'];
    } 

    return $resultToken['MngUUID'];
}

function getLogDeliveryInfo($table_name)
{
    $db = \util\container\getLOGDb();
    $sth = $db->prepare("select Delivery,LastDelivery,MaxSaveMonth,unix_timestamp(DeliveryTime) as DeliveryTime FROM LogSlice where LogTableName = :table_name");
    $sth->bindParam(':table_name', $table_name, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    $data['LogTableName'] = $table_name;
    return $data;
}

function getLogDbTableIndex($projectUUID, $delivery)
{
    $hash = sprintf('%u', crc32($projectUUID));
    return $hash%$delivery;
}

//日志记录中途是否分片
function IsDeliveredWhileRecord($tableSliceInfo)
{
    $db = \util\container\getLOGDb();
    $sth = $db->prepare("select unix_timestamp(NOW() -interval 30*:max_save_month day) < :delivery_time as Flag");
    $sth->bindParam(':max_save_month', $tableSliceInfo['MaxSaveMonth'], PDO::PARAM_STR);
    $sth->bindParam(':delivery_time', $tableSliceInfo['DeliveryTime'], PDO::PARAM_STR);
    $ret = $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return  $result['Flag'];
}

function getSqlTables($basicTable, $projectUUID, &$akcsTables, &$logTables)
{
    $tableSliceInfo = array();
    if ($basicTable == "PersonalCapture"){
        $tableSliceInfo = getLogDeliveryInfo("PersonalCapture");
    } else if ($basicTable == "PersonalMotion"){
        $tableSliceInfo = getLogDeliveryInfo("PersonalMotion");
    } else if ($basicTable == "CallHistory"){
        $tableSliceInfo = getLogDeliveryInfo("CallHistory");
    }

    $lastDelivery = $tableSliceInfo['LastDelivery'];
    //当前时间 减去 $DeliveryTime时间 是否小于 $MaxSaveMonth
    //超过最大保存时间，说明所有的数据都落到最新的分片中，否则还有数据在旧的分片中
    $haveOldDelivery = IsDeliveredWhileRecord($tableSliceInfo);
    $index = getLogDbTableIndex($projectUUID, $tableSliceInfo['Delivery']);

    if ($lastDelivery == 0){
        //第一次分片
        if ($haveOldDelivery){
            //如AKCS.PersonalCapture, AKCS.PersonalCapture_202303
            //加first day of避免大小月问题 如5月31号时执行会获取成5月
            $akcsMonth = date("Ym", strtotime("first day of -1 month"));
            $akcsTables = [$basicTable.'_'.$akcsMonth, $basicTable];  
        }

        //如LOG.PersonalCapture_2, LOG.PersonalCapture_2_202303, LOG.PersonalCapture_2_202302, ...
        //根据projectUUID获取当前社区/办公的分片
        array_push($logTables, $basicTable.'_'.$index);
        for ($i=1; $i<=$tableSliceInfo['MaxSaveMonth']; $i++){
            $logMonth = date("Ym", strtotime("first day of -$i month"));
            array_push($logTables, $basicTable.'_'.$index.'_'.$logMonth);
        }
    } else {
        if ($haveOldDelivery){
            //如LOG.PersonalCapture_2, LOG.PersonalCapture_2_202303, LOG.PersonalCapture_5_202303, ...
            $lastDeliveryIndex = getLogDbTableIndex($projectUUID, $tableSliceInfo['LastDelivery']);
            $logTables = [$basicTable.'_'.$index, $basicTable.'_'.$lastDeliveryIndex];
            for ($i=1; $i<=$tableSliceInfo['MaxSaveMonth']; $i++){
                $logMonth = date("Ym", strtotime("first day of -$i month"));
                array_push($logTables, $basicTable.'_'.$index.'_'.$logMonth);
                array_push($logTables, $basicTable.'_'.$lastDeliveryIndex.'_'.$logMonth);
            }
        } else  {
            //如LOG.PersonalCapture_2, LOG.PersonalCapture_2_202303, LOG.PersonalCapture_2_202302, ...
            array_push($logTables, $basicTable.'_'.$index);
            for ($i=1; $i<=$tableSliceInfo['MaxSaveMonth']; $i++){
                $logMonth = date("Ym", strtotime("first day of -$i month"));
                array_push($logTables, $basicTable.'_'.$index.'_'.$logMonth);
            }
        }
    }

}

function LOGTableExist($logtable)
{
    $db = \util\container\getLOGDb();
    $sth = $db->prepare("show tables like \"$logtable\";");
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data) {
        return 1;
    }
    return 0;
}

function AKCSTableExist($logtable)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("show tables like \"$logtable\";");
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data) {
        return 1;
    }
    return 0;
}