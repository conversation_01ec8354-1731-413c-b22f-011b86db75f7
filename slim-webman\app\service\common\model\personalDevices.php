<?php

namespace common\model;

const DB_TABLE_PERSONALDEVICES = "PersonalDevices";
const DB_FIELD_PERSONALDEVICES = ["Type", "Community", "Node", "MAC", "UUID", "Relay", "Flags", "Status", "CreateTime"];

function getPersonalDevicesInfoByUUID($uuid)
{
    return \util\container\medooDb()->get(DB_TABLE_PERSONALDEVICES, DB_FIELD_PERSONALDEVICES, ["UUID" => $uuid]);
}

function getPersonalDevicesInfoByMac($mac)
{
    return \util\container\medooDb()->get(DB_TABLE_PERSONALDEVICES, DB_FIELD_PERSONALDEVICES, ["MAC" => $mac]);
}