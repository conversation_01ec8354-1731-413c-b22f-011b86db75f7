<?php

namespace util\route;


class RouteCollect
{
    private $routeList = [];
    private $projectType;

    public static $instance;

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function setProjectType($type)
    {
        $this->projectType = $type;
    }

    public function get($uri, $callback)
    {
        $this->routeList[$uri][$this->projectType] = $callback;
    }
    public function post($uri, $callback)
    {
        $this->routeList[$uri][$this->projectType] = $callback;
    }

    public function getCallback($uri, $type)
    {
        $uriCallback = $this->routeList[$uri];
        if(array_key_exists($type, $uriCallback))
        {
            return $this->routeList[$uri][$type];
        }
        else
        {
            return $this->routeList[$uri][PROJECT_TYPE_COMMON];
        }
    }

    public function getAllUri()
    {
        return array_keys($this->routeList);
    }

}