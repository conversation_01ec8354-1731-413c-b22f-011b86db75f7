<?php

namespace newoffice\model;

require_once __DIR__ . "/../../../config/define.php";

use PDO;

function getRBACDataByPersonalAccountUUID($personalAccountUUID)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("/*master*/  select RBACDataGroupUUID from PersonalAccountDataGroup where PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();

    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function getFacePicInfoByPersonalAccountUUID($personalAccountUUID)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("/*master*/ select ID,AccountUUID,FaceUrl,FaceMD5 from Face where PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();

    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function updateFace($personalAccountInfo, $faceMngID, $fileInfo)
{
    $db = \util\container\getDB();

    $sth = $db->prepare("update Face set FaceUrl=:FaceUrl,FaceMD5=:FaceMD5,CreatorType=:CreatorType,CreatorPersonalAccountUUID=:CreatorPersonalAccountUUID where ID=:ID");
    $sth->bindParam(':FaceUrl', $fileInfo[FACE_FILE_URL], PDO::PARAM_STR);
    $sth->bindParam(':FaceMD5', $fileInfo[FACE_FILE_MD5], PDO::PARAM_STR);
    $sth->bindValue(':CreatorType', FACE_CREATOR_TYPE_ENDUSER, PDO::PARAM_INT);
    $sth->bindParam(':CreatorPersonalAccountUUID', $personalAccountInfo['PersonalAccountUUID'], PDO::PARAM_STR);
    $sth->bindParam(':ID', $faceMngID, PDO::PARAM_INT);
    $sth->execute();
    return true;
}

function insertFace($personalAccountInfo, $fileInfo)
{
    // 获取: RBACDataGroupUUID
    $RbacData = getRBACDataByPersonalAccountUUID($personalAccountInfo['PersonalAccountUUID']);
    if ($RbacData == false && array_key_exists('RBACDataGroupUUID', $RbacData) == false) {
        \util\log\akcsLog::debug("get RBAC failed: uuid=" . $personalAccountInfo['PersonalAccountUUID']);
        return false;
    }

    // 插入人脸
    $db = \util\container\getDB();
    $sth = $db->prepare("insert into Face (UUID, AccountUUID, PersonalAccountUUID, FaceMD5, FaceUrl, CreatorType, CreatorPersonalAccountUUID, RBACDataGroupUUID, Version) values(:UUID, :AccountUUID, :PersonalAccountUUID, :FaceMD5, :FaceUrl, :CreatorType, :CreatorPersonalAccountUUID, :RBACDataGroupUUID, :Version)");

    $sth->bindParam(':UUID', \util\common\getMysqlUUID(), PDO::PARAM_STR);
    $sth->bindParam(':AccountUUID', $personalAccountInfo["AccountUUID"], PDO::PARAM_STR);
    $sth->bindParam(':PersonalAccountUUID', $personalAccountInfo['PersonalAccountUUID'], PDO::PARAM_STR);
    $sth->bindParam(':FaceMD5', $fileInfo[FACE_FILE_MD5], PDO::PARAM_STR);
    $sth->bindParam(':FaceUrl', $fileInfo[FACE_FILE_URL], PDO::PARAM_STR);
    $sth->bindValue(':CreatorType', FACE_CREATOR_TYPE_ENDUSER, PDO::PARAM_INT);
    $sth->bindParam(':CreatorPersonalAccountUUID', $personalAccountInfo['PersonalAccountUUID'], PDO::PARAM_STR);
    $sth->bindParam(':RBACDataGroupUUID', $RbacData['RBACDataGroupUUID'], PDO::PARAM_STR); // 
    $sth->bindParam(':Version', time(), PDO::PARAM_INT);
    $sth->execute();

    return true;
}

function mergeFace($personalAccountInfo, $fileInfo)
{
    $retsult = true;
    $picInfo = \newoffice\model\getFacePicInfoByPersonalAccountUUID($personalAccountInfo['PersonalAccountUUID']);

    if ($picInfo) {
        $fdfsRet = \util\fdfs\storagePictoFdfs($fileInfo[FACE_FILE_URL], $picInfo['FaceUrl']);
        if ($fdfsRet) {
            $fileInfo[FACE_FILE_URL] = $fdfsRet;
        } else { 
            \util\alarmManage\addAkcsAlarm(AKCS_MONITOR_ALARM_FDFS_UPLOAD_FACE_FAILED, $fileInfo[FACE_FILE_URL]);
            return false;
        }
        $retsult = \newoffice\model\updateFace($personalAccountInfo, $picInfo['ID'], $fileInfo);
    } else {
        $fdfsRet = \util\fdfs\storagePictoFdfs($fileInfo[FACE_FILE_URL], "");
        if ($fdfsRet) {
            $fileInfo[FACE_FILE_URL] = $fdfsRet;
        } else {
            \util\alarmManage\addAkcsAlarm(AKCS_MONITOR_ALARM_FDFS_UPLOAD_FACE_FAILED, $fileInfo[FACE_FILE_URL]);
            return false;
        }

        $retsult = \newoffice\model\insertFace($personalAccountInfo, $fileInfo);
    }

    return $retsult;
}

function queryFace($personalAccountUUID)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("/*master*/ select ID,AccountUUID,FaceMD5,FaceUrl from Face where PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function deleteFace($personalAccountUUID)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("delete from Face where PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(':PersonalAccountUUID', $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $affected = $sth->rowCount();
    return $affected;
}
