<?php

namespace resident\control;

function dealResidentAlarm($request, $response)
{
    $userConf = \util\container\getUserData();

    $bodyValue = $request->getParsedBody();
    $alarmID = $bodyValue["ID"];
    $alarmResult = $bodyValue["Result"];
    $alarmNode = $userConf['Account'];

    if ($userConf['Role'] === ROLE_TYPE_PERSONNAL_MASTER || $userConf['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        \common\model\updatePersonalAlarms($alarmID, $alarmResult);
        AlarmDealNotify($alarmNode, $userConf['UserAccount'], $alarmID, $alarmResult);
    } else {
        \common\model\updateAlarms($alarmID, $alarmResult);
        communityAlarmDealNotify($alarmNode, $userConf['UserAccount'], $alarmID, $alarmResult);
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}
