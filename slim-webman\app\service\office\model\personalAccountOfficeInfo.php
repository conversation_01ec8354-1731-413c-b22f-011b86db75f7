<?php

namespace office\model;

use PDO;

function getOfficeInfoByUUID($personalAccountUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select EmployeeID, Flags from PersonalAccountOfficeInfo where PersonalAccountUUID = :PersonalAccountUUID");
    $sth->bindParam(":PersonalAccountUUID", $personalAccountUUID, PDO::PARAM_STR);
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}