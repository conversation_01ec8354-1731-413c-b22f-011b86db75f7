<?php

namespace newoffice\control;

require_once __DIR__ . "/../../../notify/notify.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";

function setOfficeNfcCode($request, $response)
{
    $userConf = \util\container\getUserData();
    $postDatas = $request->getParsedBody();

    $randNFCcode = "";
    if ($postDatas['enable'] == '1') {
        \util\common\getNFCCode($randNFCcode);
    }

    \common\model\UpdateNFCCode($randNFCcode, $userConf['UserAccount']);
    $jsondata = \util\common\createGeneralMessage(
        "account_modify",
        array(
            "account_uuid" => strval($userConf['UUID']),
            "office_uuid" => strval($userConf['MngUUID'])
        )
    );
    JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);

    $datas = [
        'nfccode' => $randNFCcode
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}