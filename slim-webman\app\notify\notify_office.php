<?php

require_once(dirname(__FILE__).'/socket.php');
require_once(dirname(__FILE__).'/socket_office.php');

/*******************************办公消息枚举*******************************/

//权限组相关：用户修改
const MSG_P2A_NOTIFY_OFFICE_COMMUNITY_ACCOUNT_MODIFY = MSG_P2A + 2007;
//office更新配置
const MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE = MSG_P2A + 2000;
//社区警告被处理通知
const MSG_P2A_OFFICE_ALARM_DEAL         = MSG_P2A + 2019;


function officeWebCommunityModifyNotify($changeType, $node = "", $mac = "", $officeid, $department = 0)
{
    LOG_TRACE("[OfficeWebCommunityModifyNotify]changeType=[" . $changeType . "] node=[" . $node . "] mac=[" . $mac . "] officeid=[" . $officeid . "] department=" . $department);

    $data[0] = $changeType;
    $data[1] = $node;
    $data[2] = $mac;
    $data[3] = $officeid;
    $data[4] = $department;

    $Socket = new CWebOfficeModifyNotify();
    $Socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE);
    $Socket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $Socket->copy($data);
}

function WebOfficeAccountModifyNotify($officeid, $Accounts, $AccessGroupIDs = 0)
{
    $Accounts = is_array($Accounts) ? $Accounts : [$Accounts];
    $AccessGroupIDs = is_array($AccessGroupIDs) ? $AccessGroupIDs : [$AccessGroupIDs];
    $AccessGroupIDs = array_unique($AccessGroupIDs);

    $accounts_str = implode(",", $Accounts);
    $groupids_str = implode(",", $AccessGroupIDs);
    $nodes_str = implode(",", $nodes);
    \util\log\akcsLog::debug("[WebOfficeAccountModifyNotify]: officeid=$officeid,Account=$accounts_str groupid=$groupids_str");

    $data[0] = $AccessGroupIDs;
    $data[1] = $officeid;
    $data[2] = $Accounts;
    $data[3] = [];

    \common\model\updateAccountDataVersion($Accounts);

    $socket = new CWebCommunityAccountModifyNotifySocket();
    $socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_COMMUNITY_ACCOUNT_MODIFY);
    $socket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $socket->copy($data);
}

function officeAlarmDealNotify($node, $user, $id, $result)
{
    \util\log\akcsLog::debug("[AlarmDealNotify]node=[" . $node . "] user=[" . $user . "] id=[" . $id . "] result=" . $result);
    $data[] = $node;
    $data[] = $user;
    $data[] = $id;
    $data[] = $result;
    $alarmDealSocket = new CAlarmDealSocket();
    $alarmDealSocket->setMsgID(MSG_P2A_OFFICE_ALARM_DEAL);
    $alarmDealSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $alarmDealSocket->copy($data);
}

