<?php

require_once(dirname(__FILE__) . '/../config/define.php');
require_once(dirname(__FILE__) . '/proto/proto.php');

define("UNIX_DOMAIN", "/var/adapt_sock/adapt.sock");

class CSendEmailNotify extends CKafka
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendEmailNotifyMsg();
            $TempData->setKey($data[0]);
            $TempData->setPayload($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/* * app上删除视频
 */
class CDelVideoNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\NotityDelVideo();
            $TempData->setVideoId($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/* * app开门
 */

class COpenDoorNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\OpenDoorNotify();
            $TempData->setMac($data[0]);
            $TempData->setUid($data[1]);
            $TempData->setRelay($data[2]);
            $TempData->setRepost($data[3]);
            $TempData->setMsgTraceid($data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebPersonalModifyNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\WebPersonalModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setInstallerId((int) $data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityModifyNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\WebCommunityModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setCommunityId((int) $data[3]);
            $TempData->setUnitId((int) $data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

//Deal Alarm相关通知
class CAlarmDealSocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PersonalAlarmDeal();
            $TempData->setAreaNode((string)$data[0]);
            $TempData->setUser((string)$data[1]);
            $TempData->setAlarmId((int)$data[2]);
            $TempData->setResult((string)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendSmsCodeSocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendSmsCode();
            $TempData->setType((int) $data[0]);
            $TempData->setCode((string) $data[1]);
            $TempData->setAreaCode((string) $data[2]);
            $TempData->setPhone((string) $data[3]);
            $TempData->setUserType((int) $data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CFeedbackNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\FeedbackNotify();
            $TempData->setDatas((string) $data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityAccountModifyNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityAccountModifyNotify();

            $TempData->SetAccessGroupIdList($data[0]);
            $TempData->SetCommunityId((int)$data[1]);
            $TempData->SetAccountList($data[2]);
            $TempData->SetNodeList($data[3]);
           
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/* * app开门 开security relay
 */

class COpenSecurityRelayNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\OpenSecurityRelayNotify();
            $TempData->setMac($data[0]);
            $TempData->setUid($data[1]);
            $TempData->setSecurityRelay((int)$data[2]);
            $TempData->setMsgTraceid($data[3]);
            $TempData->setRepost((int)$data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPerNewMessageSocket extends CKafka
{
	public function copy($data){
		if(is_string($data)||is_int($data)||is_float($data)){
			$data[]=$data;
		}
		if(is_array($data)){
			
			$TempData = new AK\Adapt\PerNewMessage();
			if(strlen($data[0]) != 0)
			{
				$TempData->setData($data[0]);
			}
			$PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
		}
		$this->setMsgHead();
		$this->sendMsg();
        return;
	}
}

class CThirdPartyLockNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\ThirdPartyLockNotify();
            $TempData->setMessagetype($data[0]);
            $TempData->setCaptureType($data[1]);
            $TempData->setLockType($data[2]);
            $TempData->setUuid($data[3]);
            $TempData->setPersonalAccountUuid($data[4]);
            $TempData->setInitiator($data[5]);
            $TempData->setLockName($data[6]);
            if (isset($data[7])) {
                $TempData->setPersonalAccountUuidForOperator($data[7]);
            }
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendEmailNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendEmailNotifyMsg();
            $TempData->setKey($data[0]);
            $TempData->setPayload($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSingleContactRuleNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SingleCallModifyNotify();
            $TempData->setAccount($data[0]);
            $TempData->setUnitId($data[1]);
            $TempData->setMngId($data[2]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSmartlockUpdateNotifySocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SmartLockUpdateNotify();
            $TempData->setLockUUID((string) $data[0]);
            $TempData->setLockType((int) $data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSmartlockHttpUpRouteMessageSocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SmartLockHttpUpP2PRouteMessage();
            $TempData->setClientID((string) $data[0]);
            $TempData->setJsonMessage((string) $data[1]);
            $TempData->setTraceID((string) $data[2]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CUnlockSL50SmartLockSocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\UnlockSL50SmartLockNotifyMsg();
            $TempData->setLockUUID((string) $data[0]);
            $TempData->setTraceID((string) $data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CLockSL50SmartLockSocket extends CKafka
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\LockSL50SmartLockNotifyMsg();
            $TempData->setLockUUID((string) $data[0]);
            $TempData->setTraceID((string) $data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}