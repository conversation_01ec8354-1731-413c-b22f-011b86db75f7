<?php

namespace resident\control;

require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/../model/personalDevices.php";
require_once __DIR__ . "/../model/indoorExtraDeviceRelayAction.php";
require_once __DIR__ . "/../model/indoorMonitorConfig.php";
require_once __DIR__ . "/../model/indoorExtraDevice.php";
require_once __DIR__ . "/../model/indoorMonitorZigbeeDevice.php";
require_once __DIR__ . "/../../common/model/devices.php";
require_once __DIR__ . "/../../../notify/notify.php";

function setAutoCloseTime($request, $response) {
    $userData = \util\container\getUserData();
    $params = $request->getParsedBody();
    
    $smartControlDeviceUUID = array_key_exists('smart_control_device_uuid', $params) ? $params['smart_control_device_uuid'] : '';
    $autoCloseTime = array_key_exists('auto_close_time', $params) ? $params['auto_close_time'] : '';
    $type = array_key_exists('type', $params) ? $params['type'] : '';
    $smartControlDeviceID = array_key_exists('smart_control_device_id', $params) ? $params['smart_control_device_id'] : '';

    \util\log\akcsLog::debug("setAutoCloseTime - Parameters check: smart_control_device_uuid='$smartControlDeviceUUID', auto_close_time='$autoCloseTime', type='$type', smart_control_device_id='$smartControlDeviceID'");

    if ($smartControlDeviceUUID === '' || $autoCloseTime === '' || $type === '' || $smartControlDeviceID === '')
    {
        \util\log\akcsLog::debug("setAutoCloseTime - Missing required parameters");
        return \util\response\setResponseMessage($response, ERR_CODE_PARAM_INVALID);
    }

    $autoCloseTime = intval($autoCloseTime);
    if ($autoCloseTime < 0 || $autoCloseTime > 600) 
    {
        return \util\response\setResponseMessage($response, ERR_CODE_PARAM_INVALID);
    }

    if ($type == "Local") 
    {
        if(\util\common\isSingleHouseUser($userData['Role'])) 
        {
            $result = \resident\model\updatePersonalDevicesRelayHoldDelayByUUID($smartControlDeviceUUID, $autoCloseTime, $smartControlDeviceID);
        } 
        else 
        {
            $result = \resident\model\updateDevicesRelayHoldDelayByUUID($smartControlDeviceUUID, $autoCloseTime, $smartControlDeviceID);
        }
    } 
    elseif ($type == "Extern") 
    {
        $result = \resident\control\handleExternRelayUpdate($smartControlDeviceUUID, $autoCloseTime, $userData);
    }
    else 
    {
        return \util\response\setResponseMessage($response, ERR_CODE_PARAM_INVALID);
    }

    if ($result == 0)
    {
        
        // Relay设备需要通知云端进行配置更新
        \resident\control\setRelayAutoCloseTimeNotify($userData);
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, [],"Successed to update auto close time");
    } 
    elseif($result == -2)
    {
        return \util\response\setResponseMessage($response, ERR_CODE_SET_INDOOR_RELAY_AUTO_CLOSE_TIME_LIMITED);
    }
    else 
    {
        return \util\response\setResponseMessage($response, ERR_CODE_SET_INDOOR_RELAY_AUTO_CLOSE_TIME_FAILED);
    }
} 

function handleExternRelayUpdate($smartControlDeviceUUID, $autoCloseTime, $userData) 
{
    // 获取ExtraDeviceRelayAction的相关信息，包括ExtraRelayType和DeviceAddress
    $relayInfo = \resident\model\getExtraDeviceRelayActionInfoByUUID($smartControlDeviceUUID);
    if (!$relayInfo) 
    {
        \util\log\akcsLog::debug("handleExternRelayUpdate - relay action not found for UUID: $smartControlDeviceUUID");
        return -1;
    }
    
    $extraRelayType = $relayInfo['ExtraRelayType'];
    $deviceAddress = $relayInfo['DeviceAddress'];
    $deviceUUID = $relayInfo['DeviceUUID'];
    
    if ($extraRelayType == RELAY_TYPE_RSAC_C1_R8) 
    {
        // ExtraRelayType为RELAY_TYPE_RSAC_C1_R8时，需要检查DeviceAddress是否不为空
        if ($deviceAddress === null || $deviceAddress === '') 
        {
            \util\log\akcsLog::debug("handleExternRelayUpdate - DeviceAddress is empty for ExtraRelayType=3, UUID: $smartControlDeviceUUID");
            //云端还未编辑r8的相关配置，不允许修改
            return -2;
        }
    }
    
    // 执行更新HoldDelay操作
    $updateResult = \resident\model\updateExtraDeviceRelayActionHoldDelayByUUID($smartControlDeviceUUID, $autoCloseTime);
    if ($updateResult != 0) 
    {
        \util\log\akcsLog::debug("handleExternRelayUpdate - failed to update HoldDelay for UUID: $smartControlDeviceUUID");
        return -1;
    }
    
    // 更新Meta+1
    \resident\model\incrementIndoorMonitorConfigMeta($deviceUUID);
    return 0;
}


function setRelayAutoCloseTimeNotify($userData)
{
    $mac = "";
    $installID = 0;
    $role = $userData['Role'];
    $node = $userData['Account'];
    $communityID = $userData['MngID'];
    $unitID = $userData['UnitID'];

    if ($role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_PERSONNAL_SLAVE) 
    {  
        $changeType = APP_PER_UPDATE_NODE_CONFIG;
        \webPersonalModifyNotify($changeType, $node, $mac, $installID);
    } 
    else if ($role == ROLE_TYPE_COMMUNITY_MASTER || $role == ROLE_TYPE_COMMUNITY_SLAVE)
    {
        $changeType = APP_COMM_MODIFY_NODE_CONFIG;
        \webCommunityModifyNotify($changeType, $node, $mac, $communityID, $unitID);
    }   
} 



