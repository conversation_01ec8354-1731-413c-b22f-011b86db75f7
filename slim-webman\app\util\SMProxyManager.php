<?php

namespace util\smproxy;

require_once __DIR__ . '/log.php';

/**
 * SMProxy管理类
 * 
 * 负责SMProxy的健康检查、配置管理、故障转移等功能
 */
class SMProxyManager
{
    private static $instance = null;
    private $config;
    private $isHealthy = false;
    private $lastHealthCheck = 0;
    private $healthCheckInterval = 30; // 30秒检查一次
    
    /**
     * 获取单例实例
     */
    public static function getInstance($config = null)
    {
        if (self::$instance === null) {
            self::$instance = new self($config);
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct($config = null)
    {
        $this->config = $config ?: $this->getDefaultConfig();
        $this->logInfo("SMProxy管理器初始化完成");
    }
    
    /**
     * 获取默认配置
     */
    private function getDefaultConfig()
    {
        return [
            'host' => '127.0.0.1',
            'port' => 3366,
            'username' => 'root',
            'password' => '123456',
            'timeout' => 5,
            'charset' => 'utf8mb4',
            'health_check_interval' => 30,
            'max_retry_times' => 3,
            'retry_interval' => 5,
        ];
    }
    
    /**
     * 检查SMProxy健康状态
     */
    public function checkHealth()
    {
        $now = time();
        
        // 如果距离上次检查时间不足间隔，返回缓存结果
        if ($now - $this->lastHealthCheck < $this->healthCheckInterval) {
            return $this->isHealthy;
        }
        
        $this->lastHealthCheck = $now;
        
        try {
            $startTime = microtime(true);
            
            // 尝试连接SMProxy
            $dsn = sprintf(
                'mysql:host=%s;port=%d;charset=%s',
                $this->config['host'],
                $this->config['port'],
                $this->config['charset']
            );
            
            $pdo = new \PDO($dsn, $this->config['username'], $this->config['password'], [
                \PDO::ATTR_TIMEOUT => $this->config['timeout'],
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
            ]);
            
            // 执行简单查询测试
            $result = $pdo->query('SELECT 1 as health_check')->fetch();
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            if ($result && $result['health_check'] == 1) {
                $this->isHealthy = true;
                $this->logInfo("SMProxy健康检查通过，响应时间: {$duration}ms");
            } else {
                $this->isHealthy = false;
                $this->logError("SMProxy健康检查失败：查询结果异常");
            }
            
        } catch (\Exception $e) {
            $this->isHealthy = false;
            $this->logError("SMProxy健康检查失败: " . $e->getMessage());
        }
        
        return $this->isHealthy;
    }
    
    /**
     * 获取SMProxy连接
     */
    public function getConnection()
    {
        if (!$this->checkHealth()) {
            throw new \Exception('SMProxy服务不可用');
        }
        
        try {
            $dsn = sprintf(
                'mysql:host=%s;port=%d;charset=%s',
                $this->config['host'],
                $this->config['port'],
                $this->config['charset']
            );
            
            $pdo = new \PDO($dsn, $this->config['username'], $this->config['password'], [
                \PDO::ATTR_TIMEOUT => $this->config['timeout'],
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
            ]);
            
            $this->logDebug("成功获取SMProxy连接");
            return $pdo;
            
        } catch (\Exception $e) {
            $this->logError("获取SMProxy连接失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取SMProxy统计信息
     */
    public function getStats()
    {
        try {
            $connection = $this->getConnection();
            
            // 获取连接数统计
            $result = $connection->query("SHOW STATUS LIKE 'Threads_connected'")->fetch();
            $threadsConnected = $result ? (int)$result['Value'] : 0;
            
            $result = $connection->query("SHOW STATUS LIKE 'Max_used_connections'")->fetch();
            $maxUsedConnections = $result ? (int)$result['Value'] : 0;
            
            $result = $connection->query("SHOW VARIABLES LIKE 'max_connections'")->fetch();
            $maxConnections = $result ? (int)$result['Value'] : 0;
            
            return [
                'healthy' => $this->isHealthy,
                'threads_connected' => $threadsConnected,
                'max_used_connections' => $maxUsedConnections,
                'max_connections' => $maxConnections,
                'connection_utilization' => $maxConnections > 0 ? 
                    round(($threadsConnected / $maxConnections) * 100, 2) : 0,
                'last_health_check' => date('Y-m-d H:i:s', $this->lastHealthCheck),
                'config' => [
                    'host' => $this->config['host'],
                    'port' => $this->config['port'],
                    'timeout' => $this->config['timeout'],
                ]
            ];
            
        } catch (\Exception $e) {
            $this->logError("获取SMProxy统计信息失败: " . $e->getMessage());
            return [
                'healthy' => false,
                'error' => $e->getMessage(),
                'last_health_check' => date('Y-m-d H:i:s', $this->lastHealthCheck),
            ];
        }
    }
    
    /**
     * 测试SMProxy性能
     */
    public function performanceTest($testCount = 10)
    {
        $results = [];
        $totalTime = 0;
        $successCount = 0;
        
        for ($i = 0; $i < $testCount; $i++) {
            $startTime = microtime(true);
            
            try {
                $connection = $this->getConnection();
                $result = $connection->query('SELECT 1, NOW()')->fetch();
                
                $endTime = microtime(true);
                $duration = ($endTime - $startTime) * 1000;
                
                $results[] = [
                    'test_id' => $i + 1,
                    'success' => true,
                    'duration_ms' => round($duration, 2),
                    'result' => $result
                ];
                
                $totalTime += $duration;
                $successCount++;
                
            } catch (\Exception $e) {
                $endTime = microtime(true);
                $duration = ($endTime - $startTime) * 1000;
                
                $results[] = [
                    'test_id' => $i + 1,
                    'success' => false,
                    'duration_ms' => round($duration, 2),
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return [
            'test_count' => $testCount,
            'success_count' => $successCount,
            'success_rate' => round(($successCount / $testCount) * 100, 2),
            'avg_duration_ms' => $successCount > 0 ? round($totalTime / $successCount, 2) : 0,
            'total_duration_ms' => round($totalTime, 2),
            'results' => $results,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 重新加载配置
     */
    public function reloadConfig($newConfig)
    {
        $this->config = array_merge($this->config, $newConfig);
        $this->isHealthy = false; // 重置健康状态，强制重新检查
        $this->lastHealthCheck = 0;
        
        $this->logInfo("SMProxy配置已重新加载");
        return $this->checkHealth();
    }
    
    /**
     * 获取当前配置
     */
    public function getConfig()
    {
        return $this->config;
    }
    
    /**
     * 记录信息日志
     */
    private function logInfo($message)
    {
        \util\log\akcsLog::debug("[SMProxyManager][INFO] " . $message);
    }
    
    /**
     * 记录调试日志
     */
    private function logDebug($message)
    {
        \util\log\akcsLog::debug("[SMProxyManager][DEBUG] " . $message);
    }
    
    /**
     * 记录错误日志
     */
    private function logError($message)
    {
        \util\log\akcsLog::debug("[SMProxyManager][ERROR] " . $message);
    }
}
