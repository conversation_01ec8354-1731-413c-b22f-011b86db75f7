<?php

namespace  common\control;

require_once __DIR__ . "/../model/devices.php";
require_once __DIR__ . "/../model/videoStorage.php";
require_once __DIR__ . "/../model/personalDevices.php";

function deviceVideoStorageInfo($mac)
{
    $devUUID = \common\model\getDeviceUUIDByMac($mac);

    $devStorageInfo = array();
    if ($devUUID) {
        $devStorageInfo = \common\model\getVideoStorageDeviceInfo($devUUID);
    }
    return $devStorageInfo;
} 