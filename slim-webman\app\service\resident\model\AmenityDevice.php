<?php

namespace resident\model;

use PDO;


function getRemoveDefaultAgMacs($mngUUID)
{
    $macs = [];
    $result = \util\container\medooDb()->select("AmenityDevice", 
    [
        "[>]Devices" => ["AmenityDevice.DeviceUUID" => "UUID"]
    ],
    ["Devices.MAC"], 
    ["AmenityDevice.ProjectUUID" => $mngUUID, "AmenityDevice.IsRemoveAccess" => 1]);

    foreach($result as $dev) {
        array_push($macs, $dev['MAC']);
    }

    return $macs;
}

